import { Injectable, ComponentFactoryResolver, ApplicationRef, Injector } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ShareDataService {
  public selectedBranchesSource = new BehaviorSubject<any[]>([]);
  selectedBranches$ = this.selectedBranchesSource.asObservable();

  public getBranch = new BehaviorSubject<any>([]);
  sharedBranchData = this.getBranch.asObservable();
  jsonData = []
  private existingData = new BehaviorSubject<any>({});
  private existingDataForRecipe = new BehaviorSubject<any>({});

  private setRolesModule = new BehaviorSubject<any>({});
  getRolesModule = this.setRolesModule.asObservable();

  private setVersionNumber = new BehaviorSubject<any>({});
  getVersionNumber = this.setVersionNumber.asObservable();

  private setTimeOutData = new BehaviorSubject<any>({});
  getTimeOutData = this.setTimeOutData.asObservable();

  private sendItemNames = new BehaviorSubject<any>({});
  getItemNames = this.sendItemNames.asObservable();

  private sendPartyNames = new BehaviorSubject<any>({});
  getPartyNames = this.sendPartyNames.asObservable();

  private sendPartiesRecipe = new BehaviorSubject<any>({});
  getPartiesRecipe = this.sendPartiesRecipe.asObservable();

  private sendViewRecipe = new BehaviorSubject<any>({});
  getViewRecipe = this.sendViewRecipe.asObservable();

  private sendCheckMapping = new BehaviorSubject<any>({});
  getCheckMapping = this.sendCheckMapping.asObservable();

  private sendRecipeNames = new BehaviorSubject<any>({});
  getRecipeNames = this.sendRecipeNames.asObservable();

  branchesbranches
  private sendServingNames = new BehaviorSubject<any>({});
  getServingNames = this.sendServingNames.asObservable();

  private sendServingSizes = new BehaviorSubject<any>({});
  getServingSizes = this.sendServingSizes.asObservable();

  private sendMenuData = new BehaviorSubject<any>({});
  getMenuData = this.sendMenuData.asObservable();

  private sendUserItem = new BehaviorSubject<any>({});
  getUserItem = this.sendUserItem.asObservable();

  private sendRole = new BehaviorSubject<any>([]);
  getRole = this.sendRole.asObservable();

  private sendRecipeItemNames = new BehaviorSubject<any>({});
  getRecipeItemNames = this.sendRecipeItemNames.asObservable();

  private getRecipe = new BehaviorSubject<any>({});
  getItemNameAndCode = this.getRecipe.asObservable();

  private checkSyncAvailability = new BehaviorSubject<any>({});
  sendSyncAvailability = this.checkSyncAvailability.asObservable();

  private setAccessData = new BehaviorSubject<any>({});
  getAccessData = this.setAccessData.asObservable();

  private setCopiedParty = new BehaviorSubject<any>({});
  getCopiedParty = this.setCopiedParty.asObservable();

  private sendDraftClear = new BehaviorSubject<any>({});
  // private sendDraftClear = new BehaviorSubject<{ status: boolean, condition: any }>({ status: true, condition: '' });
  getDraftClear = this.sendDraftClear.asObservable();

  private sendPackage = new BehaviorSubject<any>({});
  getPackage = this.sendPackage.asObservable();

  private setItems = new BehaviorSubject<any>({});
  getItems = this.setItems.asObservable();

  private setInvCategories = new BehaviorSubject<any>({});
  getInvCategories = this.setInvCategories.asObservable();

  private setPackageItem = new BehaviorSubject<any>({});
  getPackageItems = this.setPackageItem.asObservable();

  private setSubRecipeItem = new BehaviorSubject<any>({});
  getSubRecipeItem = this.setSubRecipeItem.asObservable();

  menuMaster: any;
  exception: boolean =  false;

  private forSettingButton = new BehaviorSubject<any>({});
  checkSettingAvailable = this.forSettingButton.asObservable();

  private forUploadButton = new BehaviorSubject<any>({});
  checkUploadAvailable = this.forUploadButton.asObservable();

  private sendPartyData = new BehaviorSubject<any>([]);
  getPartyData = this.sendPartyData.asObservable();

  // No navigation cache needed - using hardcoded placeholders

  private locationList = new BehaviorSubject<any>([]);
  private globalLocation = new BehaviorSubject<any>([]);
  private priceList = new BehaviorSubject<any>([]);
  private modifiers = new BehaviorSubject<any>([]);
  private CurrentPosList = new BehaviorSubject<any>([]);
  private currentPriceTier : number = 0;
  private inventoryItem : boolean = true ;
  private ingredientCategoryList = new BehaviorSubject<any>([]);

  private getPOSItemList = new BehaviorSubject<any>([]);
  POSItemCount: number;
  navigateFromMenu: boolean;
  partyNames: any = []
  dataSource: any=[];
  package: any=[];

  constructor() { }
  updateSelectedBranches(branches: any[]) {
    this.selectedBranchesSource.next(branches);
  }
  getSelectedBranches() {
    return this.selectedBranchesSource.value;
  }
  addData(newItem: any) {
    this.jsonData = newItem
  }

  getData() {
    return this.jsonData;
  }

  getBaseData() {
    return this.existingData
  }

  shareMenuRecipe(menuMaster){
    this.menuMaster = menuMaster ;
  }

  getMenuRecipe(){
    return this.menuMaster ;
  }

  clickedException(data){
    this.exception = data;
  }

  sendRolesModule(data){
    this.setRolesModule.next(data);
  }

  exceptionResult(){
    return this.exception;
  }

  setItemNames(obj, existingData) {
    this.sendItemNames.next(obj);
    this.existingData.next(existingData);
  }

  setPartyNames(obj) {
    this.partyNames.push(obj)
    const flatArray = Array.from(new Set(this.partyNames.flat()));
    this.sendPartyNames.next(flatArray);
  }

  setPartiesRecipe(obj) {
    this.dataSource.push(obj)
    this.sendPartiesRecipe.next(this.dataSource);
  }

  setViewRecipe(obj) {
    this.sendViewRecipe.next(obj);
  }

  sendVersionNumber(data){
    this.setVersionNumber.next(data);
  }

  checkMapping(data){
    this.sendCheckMapping.next(data);
  }


  sendTimeOutData(data){
    this.setTimeOutData.next(data);
  }

  setRecipeNames(obj, existingData){
    this.sendRecipeNames.next(obj);
    this.existingData.next(existingData);
  }

  setCategories(location){
    this.ingredientCategoryList.next(location) ;
  }

  sendInvCategories(cat){
    this.setInvCategories.next(cat) ;
  }

  getCategories() {
    return this.ingredientCategoryList
  }

  setLocations(location){
    this.locationList.next(location) ;
  }

  getLocation() {
    return this.locationList
  }

  setGlLocation(location){
    this.globalLocation.next(location) ;
  }

  getGlLocation() {
    return this.globalLocation
  }

  setPriceList(pl) {
    this.priceList.next(pl);
  }

  getPriceList() {
    return this.priceList
  }

  setModifiers(modifiers) {
    this.modifiers.next(modifiers);
  }

  getModifiers() {
    return this.modifiers
  }

  setCurrentPosList(modifiers) {
    this.CurrentPosList.next(modifiers);
  }

  getCurrentPosList() {
    return this.CurrentPosList
  }

  setDefaultPriceTier(pt) {
    this.currentPriceTier = pt;
  }

  getDefaultPriceTier() {
    return this.currentPriceTier
  }

  setItemType(pt) {
    this.inventoryItem = pt;
  }

  checkNavigate(data){
    this.navigateFromMenu = data
  }

  checkMenuNavigate(){
    return this.navigateFromMenu
  }

  getItemType() {
    return this.inventoryItem
  }

  setPOSItems(items){
    this.getPOSItemList.next(items) ;
  }

  getPOSItems() {
    return this.getPOSItemList
  }

  setServingSizeNames(obj, existingData){
    this.sendServingNames.next(obj);
    this.existingData.next(existingData);
  }

  setServingSizes(obj, existingData){
    this.sendServingSizes.next(obj);
    this.existingData.next(existingData);
  }

  setMenuData(obj){
    this.sendMenuData.next(obj);
  }

  setUserData(obj) {
    this.sendUserItem.next(obj);
  }

  setRoles(obj) {
    this.sendRole.next(obj);
  }

  checkSetting(isCheck){
    this.forSettingButton.next(isCheck);
  }

  checkUploads(isCheck){
    this.forUploadButton.next(isCheck);
  }

  setPartyData(data){
    this.sendPartyData.next(data);
  }

  // No navigation cache methods needed - using hardcoded placeholders

  setItemForRecipe(existingData){
    this.existingDataForRecipe.next(existingData);
  }

  setBaseData(existingData){
    this.existingData.next(existingData);
  }

  setItemNamesForRecipe(obj){
    this.sendRecipeItemNames.next(obj);
  }

  checkSync(obj){
    this.checkSyncAvailability.next(obj);
  }

  setAccess(obj){
    this.setAccessData.next(obj);
  }
  sharedBranchArr(obj){
    this.getBranch.next(obj)
  }

  copyParty(obj){
    this.setCopiedParty.next(obj)
  }

  setDraftClear(obj) {
    this.sendDraftClear.next(obj);
    // this.sendDraftClear.next({ status, condition });
  }

  setPackages(objArray: any[]) {
    objArray.forEach(obj => {
      const uniqueId = obj.row_uuid;

      const existingIndex = this.package.findIndex(item => item.row_uuid === uniqueId);

      if (existingIndex !== -1) {
        this.package[existingIndex] = obj;
      } else {
        this.package.push(obj);
      }
    });
    this.sendPackage.next(this.package);
  }

  getDataForFillTheForm(data, tabName) {
    let invItems
    if(this.existingData.value[tabName]) {
      this.existingData = this.existingData
    } else {
      this.existingData = this.existingDataForRecipe
    }
    if (tabName == 'vendors') {
      invItems = this.existingData.value[tabName].find(item => item.vendorName === data);
    } else if(tabName == 'Subrecipe Master') {
      invItems = this.existingData.value[tabName].find(item => (item.menuItemName) === data);
    }else if(tabName == 'menu master') {
      invItems = this.existingData.value[tabName].find(item => (item.menuItemName.toLowerCase() === data.toLowerCase()));
    }else if(tabName == 'users'){
      invItems = this.existingData.value[tabName].find(item => item.email === data);
    } else if(tabName == 'branches'){
      invItems = this.existingData.value[tabName].find(item => item.branchName === data);
    }  else if(tabName == 'servingsize conversion'){
      invItems = this.existingData.value[tabName].find(item => item['Serving Size'] === data);
    } else {
      invItems = this.existingData.value[tabName].find(item => item.itemName === data);
    }
    this.setItems.next(this.existingData.value)
    return invItems
  }

  generateSRMCode() {
    var data
    let code = this.existingData.value['Subrecipe Master'].map(item => item.menuItemCode);
    const sortedNumbers = code.map((str) => Number(str.replace(/\D/g, ''))).sort((a, b) => b - a);
    let newCode = sortedNumbers[0] + 1000 + 1;
    for (let i = 0; i < sortedNumbers.length; i++) {
      if (sortedNumbers[i] === newCode) {
        const tempCode = newCode + 1;
        data = tempCode;
        break;
      } else {
        data = newCode
      }
    }
    return 'SRA' + data.toString().slice(-3)
  }

}