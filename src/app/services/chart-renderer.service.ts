import { Injectable } from '@angular/core';
import { ChartConfiguration, ChartData, ChartType } from 'chart.js';
import { DashboardConfigService } from './dashboard-config.service';
import { SmartDashboardService } from './smart-dashboard.service';

// ===== INTERFACES =====
export interface ChartModel {
  id: string;
  title: string;
  type: string;
  data: ChartData;
  options?: ChartConfiguration['options'];
  size?: string;
}

// ===== SERVICE =====
@Injectable({
  providedIn: 'root'
})
export class ChartRendererService {

  constructor(
    private readonly configService: DashboardConfigService,
    private readonly smartDashboardService: SmartDashboardService
  ) {}

  // ===== CHART PROCESSING =====
  /**
   * Process chart data from backend and prepare for ng2-charts
   */
  processChart(chartData: any): ChartModel {
    const processedChart: ChartModel = {
      id: chartData.id,
      title: chartData.title,
      type: chartData.type,
      data: this.processChartData(chartData),
      options: this.processChartOptions(chartData)
    };

    // Preserve backend size configuration if provided
    if (chartData.size) {
      processedChart.size = chartData.size;
    }

    return processedChart;
  }

  /**
   * Process multiple charts from backend response
   */
  processCharts(chartsData: any[]): ChartModel[] {
    return chartsData.map(chartData => this.processChart(chartData));
  }

  // ===== PRIVATE PROCESSING METHODS =====
  /**
   * Process chart data structure
   */
  private processChartData(chartData: any): ChartData {
    // Backend provides complete chart data
    if (chartData.data) {
      return chartData.data;
    }

    // Fallback for legacy format
    return {
      labels: chartData.labels || [],
      datasets: chartData.datasets || []
    };
  }

  /**
   * Process chart options - merge backend options with defaults
   */
  private processChartOptions(chartData: any): ChartConfiguration['options'] {
    const backendOptions = chartData.options || {};
    const mergedOptions = this.configService.mergeChartOptions(backendOptions);

    return this.enhanceOptionsForChartType(mergedOptions, chartData.type, chartData.title);
  }

  /**
   * Enhance options based on chart type
   */
  private enhanceOptionsForChartType(options: any, chartType: string, title: string): ChartConfiguration['options'] {
    const enhanced = { ...options };

    switch (chartType) {
      case 'doughnut':
      case 'pie':
        return this.enhancePieChartOptions(enhanced, title);
      case 'bar':
        return this.enhanceBarChartOptions(enhanced, title);
      case 'horizontalBar':
        return this.enhanceHorizontalBarChartOptions(enhanced, title);
      case 'line':
        return this.enhanceLineChartOptions(enhanced);
      default:
        return enhanced;
    }
  }

  // ===== CHART TYPE ENHANCEMENT METHODS =====

  /**
   * Enhance pie/doughnut chart options
   */
  private enhancePieChartOptions(options: any, title: string): ChartConfiguration['options'] {
    return {
      ...options,
      plugins: {
        ...options.plugins,
        legend: {
          ...options.plugins?.legend,
          position: 'right'
        },
        tooltip: {
          ...options.plugins?.tooltip,
          callbacks: {
            label: (context: any) => {
              const label = context.label || '';
              const value = context.parsed;
              const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
              const percentage = ((value / total) * 100).toFixed(1);

              if (this.isCurrencyChart(title)) {
                return `${label}: ${this.smartDashboardService.formatCurrency(value)} (${percentage}%)`;
              } else {
                return `${label}: ${this.smartDashboardService.formatExactNumber(value)} (${percentage}%)`;
              }
            }
          }
        }
      }
    };
  }

  /**
   * Enhance bar chart options
   */
  private enhanceBarChartOptions(options: any, title: string): ChartConfiguration['options'] {
    return {
      ...options,
      plugins: {
        ...options.plugins,
        tooltip: {
          ...options.plugins?.tooltip,
          callbacks: {
            label: (context: any) => {
              const label = context.dataset.label || '';
              const value = context.parsed.y;

              if (this.isCurrencyChart(title)) {
                return `${label}: ${this.smartDashboardService.formatCurrency(value)}`;
              } else if (this.isPercentageChart(title)) {
                return `${label}: ${value.toFixed(1)}%`;
              } else {
                return `${label}: ${this.smartDashboardService.formatExactNumber(value)}`;
              }
            }
          }
        }
      },
      scales: {
        ...options.scales,
        y: {
          ...options.scales?.y,
          ticks: {
            ...options.scales?.y?.ticks,
            callback: (value: any) => {
              if (this.isCurrencyChart(title)) {
                return this.smartDashboardService.formatCurrency(value);
              } else if (this.isPercentageChart(title)) {
                return `${value}%`;
              } else {
                return this.smartDashboardService.formatExactNumber(value);
              }
            }
          }
        }
      }
    };
  }

  /**
   * Enhance horizontal bar chart options
   */
  private enhanceHorizontalBarChartOptions(options: any, title: string): ChartConfiguration['options'] {
    const enhanced = this.enhanceBarChartOptions(options, title);

    return {
      ...enhanced,
      indexAxis: 'y' as const,
      plugins: {
        ...enhanced.plugins,
        legend: {
          ...enhanced.plugins?.legend,
          display: false
        }
      },
      scales: {
        x: enhanced.scales?.['y'],
        y: {
          ...enhanced.scales?.['x'],
          grid: { display: false }
        }
      }
    };
  }

  /**
   * Enhance line chart options
   */
  private enhanceLineChartOptions(options: any): ChartConfiguration['options'] {
    return {
      ...options,
      interaction: {
        mode: 'index',
        intersect: false
      },
      plugins: {
        ...options.plugins,
        tooltip: {
          ...options.plugins?.tooltip,
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        ...options.scales,
        x: {
          ...options.scales?.x,
          grid: { display: true, color: 'rgba(0,0,0,0.1)' }
        },
        y: {
          ...options.scales?.y,
          grid: { display: true, color: 'rgba(0,0,0,0.1)' }
        }
      }
    };
  }

  // ===== UTILITY METHODS =====
  /**
   * Check if chart deals with currency values
   */
  private isCurrencyChart(title: string): boolean {
    const currencyKeywords = ['value', 'amount', 'cost', 'price', 'revenue', 'profit', 'loss', '₹'];
    return currencyKeywords.some(keyword =>
      title.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * Check if chart deals with percentage values
   */
  private isPercentageChart(title: string): boolean {
    const percentageKeywords = ['percentage', '%', 'rate', 'ratio', 'growth'];
    return percentageKeywords.some(keyword =>
      title.toLowerCase().includes(keyword.toLowerCase())
    );
  }



  // ===== PUBLIC UTILITY METHODS =====
  /**
   * Get chart type for ng2-charts
   */
  getChartType(type: string): ChartType {
    const typeMap: Record<string, ChartType> = {
      bar: 'bar',
      horizontalBar: 'bar',
      line: 'line',
      doughnut: 'doughnut',
      pie: 'pie',
      radar: 'radar',
      polarArea: 'polarArea',
      table: 'bar' // Fallback for table type, but won't be used since we handle tables separately
    };

    return typeMap[type] || 'bar';
  }

  /**
   * Get chart CSS class based on backend size configuration
   */
  getChartCssClass(chart: ChartModel): string {
    const classes = ['chart-container', `chart-${chart.type}`];

    // Use backend-specified size if available
    if (chart.size) {
      const sizeMap: Record<string, string> = {
        full: 'full-width',
        half: 'half-width',
        third: 'third-width',
        quarter: 'quarter-width',
        'two-thirds': 'two-thirds-width'
      };
      classes.push(sizeMap[chart.size] || 'half-width');
    } else {
      // Enhanced type-based logic for better space utilization
      if (chart.type === 'line' || chart.type === 'table' || chart.title.toLowerCase().includes('trend') || chart.title.toLowerCase().includes('analysis')) {
        classes.push('full-width');
      } else if (chart.type === 'doughnut' || chart.type === 'pie') {
        classes.push('third-width');
      } else if (chart.type === 'bar' && chart.title.toLowerCase().includes('summary')) {
        classes.push('two-thirds-width');
      } else {
        classes.push('half-width');
      }
    }



    return classes.join(' ');
  }


}
