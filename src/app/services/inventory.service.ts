import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Inventory } from '../models/user.model';
import { environment } from 'src/environments/environment';
import { catchError, map, switchMap } from 'rxjs/operators';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class InventoryService {
  private baseUrl: string = environment.baseUrl
  private engineUrl: string = environment.engineUrl
  constructor(private http: HttpClient) { }

  postRegistration(registerObj: any) {
    return this.http.post<any>(`${this.baseUrl}`, registerObj)
  }

  getBaseData(tenantId:string) {
    return this.http.get<any[]>(`${this.baseUrl}/getBaseDataForMD?tenantId=${tenantId}`)
  }

  getScreens(tenantId:string) {
    return this.http.get<any[]>(`${this.baseUrl}/getScreensForMD?tenantId=${tenantId}`)
  }

  getCategories(obj) {
    let tenantId = obj['tenantId'];
    let type = obj['type'];
    return this.http.get<any[]>(`${this.baseUrl}/getCategories?tenantId=${tenantId}&type=${type}`)
  }
  getSubCategories(obj) {
    let tenantId = obj['tenantId'];
    let category = obj['category'];
    let type = obj['type'];
    return this.http.get<any[]>(`${this.baseUrl}/getSubCategories?tenantId=${tenantId}&category=${category}&type=${type}`)
  }

  // getPresentData(obj:object) {
  //   let specific
  //   let itemCode
  //   let tenantId = obj['tenantId'];
  //   let type = obj['type'];
  //   let userEmail = obj['userEmail'];
  //   if(obj['specific']){
  //     specific = obj['specific'];
  //   }
  //   if(obj['itemCode'] !== undefined){
  //     itemCode = obj['itemCode'];
  //   }

  //   if("specific" in obj && obj['itemCode'] === undefined){
  //     return this.http.get<any[]>(`${this.baseUrl}/getRecipeData?tenantId=${tenantId}&type=${type}&userEmail=${userEmail}&specific=${specific}`)
  //   }else if("itemCode" in obj){
  //     return this.http.get<any[]>(`${this.baseUrl}/getRecipeData?tenantId=${tenantId}&type=${type}&userEmail=${userEmail}&specific=${specific}&itemCode=${itemCode}`)
  //   }else{
  //     return this.http.get<any[]>(`${this.baseUrl}/getRecipeData?tenantId=${tenantId}&type=${type}&userEmail=${userEmail}`)
  //   }
  // }

  uploadExcel(obj) {
    return this.http.post(`${this.baseUrl}uploadExcel/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  resetSession(obj) {
    return this.http.post(`${this.baseUrl}resetSession/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updateRegisterUser(registerObj: any, id: number) {
    return this.http.put<any>(`${this.baseUrl}/${id}`, registerObj)
  }

  deleteRegistered(id: number) {
    return this.http.delete<any>(`${this.baseUrl}/${id}`)
  }

  getRegisteredUserId(id: number) {
    return this.http.get<any>(`${this.baseUrl}/${id}`)
  }

  getCategoryList(tenantId) {
    return this.http.get<any[]>(`${this.baseUrl}/getMenuCategoriesForMD/?tenantId=${tenantId}`)
  }

  getRecipeCode(tenantId) {
    return this.http.get<any[]>(`${this.baseUrl}/getRecipeCode/?tenantId=${tenantId}`)
  }

  getPartyCode(tenantId) {
    return this.http.get<any[]>(`${this.baseUrl}/getPartyCode/?tenantId=${tenantId}`)
  }

  getSubCategoryList(obj : object) {
    let tenantId = obj['tenantId'];
    let category = obj['category'];
    return this.http.get<any[]>(`${this.baseUrl}/getMenuSubCategoriesForMD/?tenantId=${tenantId}&category=${category}`)
  }

  getServingSizeList(tenantId) {
    return this.http.get<any[]>(`${this.baseUrl}/getServingSizeForMD/?tenantId=${tenantId}`)
  }

  getSections(obj : object) {
    let tenantId = obj['tenantId'];
    let storeId = obj['storeId'];
    return this.http.get<any[]>(`${this.baseUrl}/getPOSFloors/?tenantId=${tenantId}&storeId=${storeId}`)
  }

  getInvList(obj : object) {
    let tenantId = obj['tenantId'];
    let restaurantId = obj['restaurantId'];
    return this.http.get<any[]>(`${this.baseUrl}/getInventoryListForMD/?tenantId=${tenantId}&restaurantId=${restaurantId}`)
  }

  getInventoryListForSubrecipeMD(obj : object) {
    let tenantId = obj['tenantId'];
    let restaurantId = obj['restaurantId'];
    return this.http.get<any[]>(`${this.baseUrl}/getInventoryListForSubrecipeMD/?tenantId=${tenantId}&restaurantId=${restaurantId}`)
  }

  updateData(obj: any): Observable<any> {
    return this.http.post(`${this.engineUrl}master_data/updateData`, obj).pipe(
      map((res: any) => {
        if (res.out_of_sync) {
          alert(res.message);
          window.location.reload();
          throw new Error("Session is out of sync");
        }
        return res;
      }),
      catchError((error: any) => {
        console.error("Error updating data:", error);
        throw error;
      })
    );
  }


  getPresentData(obj:object) {
    let specific
    let itemCode
    let tenantId = obj['tenantId'];
    let type = obj['type'];
    let userEmail = obj['userEmail'];
    if(obj['specific']){
      specific = obj['specific'];
    }
    if(obj['itemCode'] !== undefined){
      itemCode = obj['itemCode'];
    }

    if("specific" in obj && obj['itemCode'] === undefined){
      return this.http.get<any[]>(`${this.engineUrl}master_data/getData?tenantId=${tenantId}&category=${type}&userEmail=${userEmail}&specific=${specific}`)
    }else if("itemCode" in obj){
      return this.http.get<any[]>(`${this.engineUrl}master_data/getData?tenantId=${tenantId}&category=${type}&userEmail=${userEmail}&specific=${specific}&itemCode=${itemCode}`)
    }else{
      return this.http.get<any[]>(`${this.engineUrl}master_data/getData?tenantId=${tenantId}&category=${type}&userEmail=${userEmail}`)
    }
  }

  wacRetrigger(obj: object) {
    return this.triggerRequest('wacRetrigger', obj);
  }

  salesRetrigger(obj: object) {
    return this.triggerRequest('salesRetrigger', obj);
  }

  forecastRetrigger(obj: object) {
    return this.triggerRequest('forecastRetrigger', obj);
  }

  private triggerRequest(endpoint: string, obj: object) {
    const tenantId = obj['tenantId'];
    const event = obj['event'];
    return this.http.get<any[]>(`${this.baseUrl}/${endpoint}?tenantId=${tenantId}&event=${event}`);
  }

  salesRerun(obj){
    return this.http.post(`${this.baseUrl}salesRerun/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  weightedAvg(obj){
    return this.http.post(`${this.baseUrl}weightedAvg/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  forecastData(obj){
    return this.http.post(`${this.baseUrl}forecastData/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  deleteData(obj){
    return this.http.post(`${this.baseUrl}deleteData/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updateRecipe(obj) {
    return this.http.post(`${this.baseUrl}updateRecipe/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getPages(tenantId) {
    return this.http.get<any[]>(`${this.baseUrl}/getPages/?tenantId=${tenantId}`)
  }

  getRecipeList(tenantId) {
    return this.http.get<any[]>(`${this.baseUrl}/getRecipeList/?tenantId=${tenantId}`)
  }

  getRecipeDataById(recipeId) {
    return this.http.get<any[]>(`${this.baseUrl}/getRecipeById/?recipeId=${recipeId}`)
  }

  getLocations(tenantId: string) {
    return this.http.get<any[]>(`${this.baseUrl}/getBranchesForMD?tenantId=${tenantId}`)
  }

  getTenantConfigDetails(tenantId: string) {
    return this.http.get<any[]>(`${this.baseUrl}/getMasterDataConfigByTenantId?tenantId=${tenantId}`)
  }

  getClientConfigDetails(tenantId: string) {
    return this.http.get<any[]>(`${this.baseUrl}/getClientConfigDetails?tenantId=${tenantId}`)
  }

  getRoles(tenantId: string) {
    return this.http.get<any[]>(`${this.baseUrl}/getRoles?tenantId=${tenantId}`)
  }

  getPOSPriceTires(tenantId: string, branch?: string) {
    let url = `${this.baseUrl}/getPOSPriceTires?tenantId=${tenantId}`;
    if (branch) {
      url += `&restaurant=${branch}`;
    }
    return this.http.get<any[]>(url);
    }

  getUIAccess(tenantId: string) {
    return this.http.get<any[]>(`${this.baseUrl}/getUIAccess?tenantId=${tenantId}`)
  }

  getModifiers(tenantId: string) {
    return this.http.get<any[]>(`${this.baseUrl}/getPOSModifiers?tenantId=${tenantId}`)
  }

  getPOSServingSizes(tenantId: string) {
    return this.http.get<any[]>(`${this.baseUrl}/getPOSServingSize?tenantId=${tenantId}`)
  }

  updateClientConfigDetails(obj) {
    return this.http.post(`${this.baseUrl}updateClientConfigDetails/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  refreshApiCall(obj) {
    return this.http.post(`${this.baseUrl}refreshApiCall/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updateAccess(obj) {
    return this.http.post(`${this.baseUrl}updateAccess/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  removeData(obj) {
    return this.http.post(`${this.baseUrl}removeData/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updatePermission(obj) {
    return this.http.post(`${this.baseUrl}updatePermission/`, obj).pipe(map((res: any) => {
      return res
    }))
  }
  syncToInventory(obj) {
    return this.http.post(`${this.baseUrl}createUpdateJob/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  retrieveHistory(obj) {
    return this.http.post(`${this.baseUrl}retrieveUpdates/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getErrorlog(obj) {
    return this.http.post(`${this.baseUrl}getErrorLog/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getConfig() {
    let obj = {}
    return this.http.post(`${this.baseUrl}masterDataUpdateConfig/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getItemCost(obj) {
    return this.http.post(`${this.baseUrl}getRecipeCost/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getInvCode() {
    return this.http.get<any[]>(`${this.baseUrl}/generateInvCode`)
  }

  getIPAddress() {
    // return this.http.get<any[]>('https://api.ipify.org/?format=json');
    return this.http.get<any[]>('https://api.bigdatacloud.net/data/client-ip');
  }

  readIPConfig(tenantId: string) {
    return this.http.get<any[]>(`${this.baseUrl}/readIPConfig?tenantId=${tenantId}`)

  }

  updateIPConfig(obj) {
    return this.http.post(`${this.baseUrl}updateIPConfig/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  dicontinuedData(obj){
    return this.http.post(`${this.baseUrl}dicontinuedData/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getRolesListDiscontinuedLocations(tenantId : string){
    return this.http.get<any[]>(`${this.baseUrl}/getRolesListDiscontinuedLocations?tenantId=${tenantId}`)
  }

  itemNameSearch(obj : object) {
    let tenantId = obj['tenantId'];
    let ingredient_name = obj['ingredient_name'];
    return this.http.get<any[]>(`${this.engineUrl}llm/search?tenantId=${tenantId}&ingredient_name=${ingredient_name}`)
  }

  getPOS_MenuItems(tenantId: string) {
    return this.http.get<any[]>(`${this.baseUrl}/getPOSMenuItems?tenantId=${tenantId}`)
  }

  getPOS_MenuItemById(obj : object) {
    let tenantId = obj['tenantId'];
    let menu_id = obj['menu_id'];
    return this.http.get<any[]>(`${this.baseUrl}/getPOSMenuById/?tenantId=${tenantId}&menu_id=${menu_id}`)
  }

  getCode(obj : object) {
    let tenantId = obj['tenantId'];
    let code = obj['code'];
    return this.http.get<any[]>(`${this.baseUrl}/getCode/?tenantId=${tenantId}&code=${code}`)
  }

  getDetailedPriceList(obj : object) {
    let tenantId = obj['tenantId'];
    let priceId = obj['priceId'];
    let restaurantId = obj['restaurantId'];
    return this.http.get<any[]>(`${this.baseUrl}/getPOSMenuPriceById/?tenantId=${tenantId}&priceId=${priceId}&restaurantId=${restaurantId}`)
  }

  getDetailedModifierList(obj : object) {
    let tenantId = obj['tenantId'];
    let modifierId = obj['modifierId'];
    return this.http.get<any[]>(`${this.baseUrl}/getModifierById/?tenantId=${tenantId}&modifierId=${modifierId}`)
  }

  getPOS_MenuCost(obj : object) {
    let tenantId = obj['tenantId'];
    let servingSize = obj['servingSize'];
    let itemCode = obj['itemCode'];
    let restaurantId = obj['restaurantId'];
    return this.http.get<any[]>(`${this.baseUrl}/getMenuCost/?tenantId=${tenantId}&servingSize=${servingSize}&itemCode=${itemCode}&restaurantId=${restaurantId}`)
  }

  getMenuMappingList(obj : object) {
    let tenantId = obj['tenantId'];
    let restaurantId = obj['restaurantId'];
    let exp = obj.hasOwnProperty('export') ? obj['export'] : false ;
    let itemCode = obj['itemCode'];
    let page = obj.hasOwnProperty('page') ? obj['page'] : 1;
    let per_page = obj.hasOwnProperty('per_page') ? obj['per_page'] : 5;
    return this.http.get<any[]>(`${this.engineUrl}menu_mapping/List?tenantId=${tenantId}&itemCode=${itemCode}&page=${page}&per_page=${per_page}&export=${exp}&restaurantId=${restaurantId}`)
  }

  createPartyOrder(obj){
    return this.http.post(`${this.baseUrl}createPartyOrder/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getPartyOrder(tenantId:string) {
    return this.http.get<any[]>(`${this.baseUrl}/getPartyOrder?tenantId=${tenantId}`)
  }

  getPartyNames(tenantId: string) {
    return this.http.get<any[]>(`${this.baseUrl}/getPartyNames?tenantId=${tenantId}`)
  }

  setPartyDraft(obj){
    return this.http.post(`${this.baseUrl}setPartyDraft/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getPartyDraft(tenantId: string){
    return this.http.get<any[]>(`${this.baseUrl}/getPartyDraft?tenantId=${tenantId}`)
  }

  deletePartyDraft(obj){
    return this.http.post(`${this.baseUrl}deletePartyDraft/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  deleteParty(obj){
    return this.http.post(`${this.baseUrl}deleteParty/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  deleteAllPartyDraft(obj){
    return this.http.post(`${this.baseUrl}deleteAllPartyDraft/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updateMenuMapping(obj) {
    return this.http.post(`${this.engineUrl}menu_mapping/${obj['id']}`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  printInvoice(obj) {
    return this.http.post(`${this.engineUrl}print_data/generate-invoice`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  printPartyInvoice(obj) {
    return this.http.post(`${this.engineUrl}party_print/generate-party-invoice`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  globalPrintPdf(data){
    var pdfData = atob(data);
    var arrayBuffer = new ArrayBuffer(pdfData.length);
    var uint8Array = new Uint8Array(arrayBuffer);
    for (var i = 0; i < pdfData.length; i++) {
      uint8Array[i] = pdfData.charCodeAt(i);
    }
    var blob = new Blob([arrayBuffer], { type: 'application/pdf' });
    var url = URL.createObjectURL(blob);
    window.open(url, '_blank');
  }

  createMenuMapping(obj) {
    return this.http.post(`${this.engineUrl}menu_mapping/Create`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  importData(obj) {
    return this.http.post(`${this.engineUrl}menu_mapping/Import`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  recipeInsight(obj) {
    return this.http.post(`${this.engineUrl}llm/recipe-insight`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updateConfigAccess(obj: any) {
    return this.http.post(`${this.baseUrl}updateConfigAccess/`, obj).pipe(map((res: any) => res));
  }

  updateReport(obj: any) {
    return this.http.post(`${this.baseUrl}updateReport/`, obj).pipe(map((res: any) => res));
  }

  updateRole(obj: any) {
    return this.http.post(`${this.baseUrl}updateRole/`, obj).pipe(map((res: any) => res));
  }

  getReportData(tenantId: string) {
    return this.http.get<any[]>(`${this.baseUrl}/getReportData?tenantId=${tenantId}`)

  }

  getRoloposConfig(obj: any) {
    return this.http.get(`${this.baseUrl}getRoloposConfig/`, obj).pipe(map((res: any) => res));
  }

  saveAccount(obj: any) {
    return this.http.post(`${this.baseUrl}accountSetUp`, obj).pipe(map((res: any) => res));
  }

  getAccountById(tenantId: string) {
    return this.getRoloposConfig({ tenantId: this.getCurrentUser().tenantId }).pipe(
      map((res: any) => {
        if (res.success && res.data && Array.isArray(res.data)) {
          const account = res.data.find(acc => acc.tenantId === tenantId);
          return { success: !!account, data: account };
        }
        return { success: false, data: null };
      })
    );
  }

  getCurrentUser() {
    const userStr = sessionStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }
  private apiUrl = `${this.baseUrl}update-closing-dates`;  //


  updateClosingDates(data: any): Observable<any> {
    return this.http.post<any>(this.apiUrl, data);
  }

  startProcessing(tenantId: string) {
    return this.http.post(`${this.engineUrl}llm/start_processing`, { tenantId });
  }

  getStatus(tenantId: string) {
    return this.http.get(`${this.engineUrl}llm/get_status`, { params: { tenantId } });
  }

  downloadData(type: string, tenantId: string) {
    return this.http.get(`${this.engineUrl}llm/download`, {
      params: { type , tenantId},
      responseType: 'text'
    });
  }

  }

