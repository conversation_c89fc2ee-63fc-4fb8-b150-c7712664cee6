import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { GlobalsService } from './globals.service';
import { User } from '../models/user.model';
import { ShareDataService } from 'src/app/services/share-data.service';
import { TimeOutService } from './time-out.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private loggedIn$ = new BehaviorSubject<boolean>(false);

  private currentUserSubject = new BehaviorSubject<any>(this.getParsedItem(GlobalsService.user));
  private roleSubject = new BehaviorSubject<any>(this.getParsedItem(GlobalsService.role));
  private branchesSubject: any;
  private restaurantSubject = new BehaviorSubject<any>(null);
  baseUrl: string = environment.baseUrl;
  constructor(private http: HttpClient,
    private sharedData: ShareDataService,
    private sessionTimeoutService: TimeOutService,
    ) {
  }

  private getParsedItem(key: string): any {
    const storedItem = sessionStorage.getItem(key);
    try {
        return storedItem ? JSON.parse(storedItem) : null;
    } catch (error) {
        return null;
    }
}

  getCurrentUser() {
    return this.currentUserSubject.value;
  }

  getBranchesValue() {
    return this.branchesSubject.value;
  }

  getCurrRest() {
    return this.restaurantSubject.value;
  }

  setCurrRest(val: any) {
    this.restaurantSubject.next(val)
  }
  setCurrRole(val: any) {
    this.roleSubject.next(val)
  }

  getCurrRole() {
    return this.roleSubject.value;
  }

  login(reqObj: any) {
    // this.authService.login();
    // this.sessionTimeoutService.start();
    return this.http.post(`${this.baseUrl}authenticate/`, reqObj).pipe(
      switchMap((res: any) => {
        if (res.result === GlobalsService.success) {
          let user = new User();
          user = res.uRecord;
          return this.getRolesList({ "tenantId": user.tenantId }).pipe(
            map((roles: any) => {
              if(roles){
                sessionStorage.setItem(GlobalsService.accessData, JSON.stringify(roles['access']));
              }
              if (res.auth_token) {
                user.token = res.auth_token;
              }
              sessionStorage.setItem(GlobalsService.user, JSON.stringify(user));
              sessionStorage.setItem('tanetId', user.tenantId);
              this.currentUserSubject.next(user);
              const userRole = roles.rolesList.find((role) =>
                role.role.includes(user.role)
              );
              sessionStorage.setItem(GlobalsService.role, JSON.stringify(userRole));
              this.roleSubject.next(userRole);
              this.loggedIn$.next(true);
              return true;
            })
          );
        }
        return of(false);
      })
    );
  }
  

  getBranches(obj) {
    return this.http.post(`${this.baseUrl}getBranches/`, obj).pipe(map((res: any) => {
      if (res.result && res.result === 'success') {
        sessionStorage.setItem(GlobalsService.branches, JSON.stringify(res.branches));
        return res.branches.sort((a, b) => a.branchName > b.branchName)
      }
      return false;
    }));
  }

  getDbRange(obj) {
    return this.http.get(`${this.baseUrl}getDbDateRange/${obj.tId}`).pipe(map(res => {
      return res;
    }))
  }

  logout() {
    sessionStorage.removeItem(GlobalsService.user);
    this.currentUserSubject.next(null);
    this.loggedIn$.next(false);
  }

  get isLoggedIn(): Observable<boolean> {
    return this.loggedIn$.asObservable();
  }

  dummyLogin(reqObj) {
    if (reqObj.email === GlobalsService.purchaseController) {
      let user = new User();
      user = { "name": "PurchaseController", "tenantId": "100001", "mobile": null, "restaurantId": null, "company": null, "companyId": null, "email": "<EMAIL>", "mId": "5ca5c6d2e4c04c216311a722", "role": ["purchaseController"], "type": null, "restaurantAccess": [{ "branchName": "indiranagar", "branchLocation": "Indiranagar", "restaurantIdOld": "100001@indiranagar", "restaurantId": "900001" }], "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjp7IiRvaWQiOiI1Y2E1YzZkMmU0YzA0YzIxNjMxMWE3MjIifSwiZXhwIjoxNTU1NDExNDY2fQ.Fh96WAaFXTjt0AgSl5ZN5OHbQP2i7lzUQthwFPk6w2o" };
      sessionStorage.setItem(GlobalsService.user, JSON.stringify(user));
      this.currentUserSubject.next(user);
    }
    else {
      let user = new User();
      user = { "name": "Vendor", "tenantId": "100001", "mobile": null, "restaurantId": null, "company": null, "companyId": null, "email": "<EMAIL>", "mId": "5ca5c6d2e4c04c216311a722", "role": ["vendor"], "type": null, "restaurantAccess": [{ "branchName": "indiranagar", "branchLocation": "Indiranagar", "restaurantIdOld": "100001@indiranagar", "restaurantId": "900001" }], "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjp7IiRvaWQiOiI1Y2E1YzZkMmU0YzA0YzIxNjMxMWE3MjIifSwiZXhwIjoxNTU1NDExNDY2fQ.Fh96WAaFXTjt0AgSl5ZN5OHbQP2i7lzUQthwFPk6w2o" };
      sessionStorage.setItem(GlobalsService.user, JSON.stringify(user));
      this.currentUserSubject.next(user);
    }
    return true;

  }

  getUsersList(obj) {
    return this.http.post(`${this.baseUrl}getUsersList/`, obj).pipe(map((res: any) => {
      return res
    }));
  }

  getBranchesForDirectIndent(obj) {
    return this.http.post(`${this.baseUrl}getBranchesforDirectIndent/`, obj).pipe(map((res: any) => {
      return res
    }));
  }

  createNewUser(obj) {
    return this.http.post(`${this.baseUrl}createNewUser/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  createNewTenantId(obj) {
    return this.http.post(`${this.baseUrl}createNewTenantId/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getRolesList(obj) {
    return this.http.post(`${this.baseUrl}getRolesList/`, obj).pipe(map((res: any) => {
      return res
    }));
  }

  createRole(obj) {
    return this.http.post(`${this.baseUrl}createRole/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updateUser(obj) {
    return this.http.post(`${this.baseUrl}updateUser/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updateRole(obj) {
    return this.http.post(`${this.baseUrl}updateRole/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getUserNewTenantId(obj) {
    return this.http.post(`${this.baseUrl}getUserNewTenantId/`, obj).pipe(map((res: any) => {
      return res
    }))
  }
  updateUserDetails(obj) {
    return this.http.post(`${this.baseUrl}updateUserDetails/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getTenantUserDetails(obj) {
    return this.http.post(`${this.baseUrl}getTenantUserDetails/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

}
