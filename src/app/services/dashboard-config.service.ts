import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '../../environments/environment';
import {
  SEMANTIC_COLORS,
  CHART_COLOR_PALETTE,
  ColorThemeUtils,
  SemanticColors
} from '../config/color-theme.config';

// ===== INTERFACES =====
export interface DashboardType {
  value: string;
  label: string;
}

export interface BaseDateOption {
  value: string;
  label: string;
}

export interface DashboardConfig {
  chart_colors: string[];
  chart_types: Record<string, string>;
  currency: {
    code: string;
    symbol: string;
  };
  dashboard_types: DashboardType[];
  base_date_options: BaseDateOption[];
  default_chart_options: Record<string, any>;
  summary_card_config: {
    colors: Record<string, string>;
    icons: Record<string, string>;
  };
  ui_config: {
    default_date_range_days: number;
    default_dashboard_type: string;
    default_base_date: string;
  };
}

export interface DashboardConfigResponse {
  status: string;
  data: DashboardConfig;
}

// ===== SERVICE =====
@Injectable({
  providedIn: 'root'
})
export class DashboardConfigService {
  private readonly baseUrl = environment.engineUrl;
  private readonly configSubject = new BehaviorSubject<DashboardConfig | null>(null);
  public readonly config$ = this.configSubject.asObservable();

  constructor(private readonly http: HttpClient) {}

  // ===== CONFIGURATION MANAGEMENT =====
  /**
   * Load dashboard configuration from backend
   */
  loadConfig(): Observable<DashboardConfigResponse> {
    return this.http.get<DashboardConfigResponse>(`${this.baseUrl}api/smart-dashboard/config`);
  }

  /**
   * Set configuration and notify subscribers
   */
  setConfig(config: DashboardConfig): void {
    this.configSubject.next(config);
  }

  /**
   * Get current configuration
   */
  getCurrentConfig(): DashboardConfig | null {
    return this.configSubject.value;
  }

  // ===== CHART CONFIGURATION =====
  /**
   * Get chart colors from config with fallback to theme colors
   */
  getChartColors(): string[] {
    return this.getCurrentConfig()?.chart_colors || CHART_COLOR_PALETTE.main;
  }

  /**
   * Get default chart options from config
   */
  getDefaultChartOptions(): Record<string, any> {
    return this.getCurrentConfig()?.default_chart_options || {
      responsive: true,
      maintainAspectRatio: false
    };
  }

  /**
   * Merge chart options with defaults
   */
  mergeChartOptions(chartOptions: Record<string, any>): Record<string, any> {
    const defaultOptions = this.getDefaultChartOptions();
    return this.deepMerge(defaultOptions, chartOptions || {});
  }

  // ===== DASHBOARD OPTIONS =====
  /**
   * Get available dashboard types
   */
  getDashboardTypes(): DashboardType[] {
    return this.getCurrentConfig()?.dashboard_types || [];
  }

  /**
   * Get available base date options
   */
  getBaseDateOptions(): BaseDateOption[] {
    return this.getCurrentConfig()?.base_date_options || [];
  }

  /**
   * Get UI configuration defaults
   */
  getUIConfig(): Record<string, any> {
    return this.getCurrentConfig()?.ui_config || {
      default_date_range_days: 30,
      default_dashboard_type: 'inventory',
      default_base_date: 'deliveryDate'
    };
  }

  /**
   * Get chart type display name
   */
  getChartTypeDisplayName(type: string): string {
    return this.getCurrentConfig()?.chart_types?.[type] || type;
  }

  // ===== CURRENCY UTILITIES =====
  /**
   * Get currency symbol from config
   */
  getCurrencySymbol(): string {
    return this.getCurrentConfig()?.currency?.symbol || '₹';
  }

  // ===== UTILITY METHODS =====
  /**
   * Deep merge utility function
   */
  private deepMerge(target: Record<string, any>, source: Record<string, any>): Record<string, any> {
    const result = { ...target };

    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }

    return result;
  }

  // ===== COLOR THEME UTILITIES =====
  /**
   * Get semantic colors for different data types
   */
  getSemanticColors(): SemanticColors {
    return SEMANTIC_COLORS;
  }

  /**
   * Get contextual color based on value and context
   */
  getContextualColor(value: number, context = 'default'): string {
    return ColorThemeUtils.getContextualColor(value, context);
  }

  /**
   * Generate chart colors for datasets
   */
  generateChartColors(count: number): { backgroundColor: string[]; borderColor: string[] } {
    return ColorThemeUtils.generateChartColors(count);
  }

  /**
   * Get chart color by index
   */
  getChartColorByIndex(index: number): string {
    return ColorThemeUtils.getChartColor(index);
  }
}
