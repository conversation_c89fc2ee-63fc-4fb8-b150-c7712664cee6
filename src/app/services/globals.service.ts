import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class GlobalsService {
  static events: string = "Events";
  static activeuser: string = "activeuser";
  static QRGenerator: string = "QR Generator";
  static PriceList: string = "Price List";
  static readonly detailedPriceList: string = "detailedPriceList";
  static masterDataUpdate: string = "Master Data Update";
  static inventoryMaster: string = "Inventory Master";
  static packagingMaster: string = "Packaging Master";
  static subrecipeMaster: string = "SubRecipe Master";
  static menuMaster: string = "Menu Master";
  static menuRecipes: string = "Menu Recipes";
  static subreciperecipes: string = "Subrecipe Recipes";
  static roles: string = "Roles";
  static userData: string = "Users";
  static accessData: string = "access";
  static menuToWorkArea: string = "Menu To Workarea";
  static vendorMaster: string = "Vendor Master";
  static servingConversion: string = "Serving Size Conversion";
  static accountDetails: string = "AccountDetails";
  static categoryData: string = "categoryData";
  static history: string = "Audit Log";
  static templateStatus: string = "Template Status";

  static workareaMaster: string = "WorkAreas";
  static accountSetupTable: string = "Account Setup";
  static accountSetup: string = "Account Setup";
  
  static branchesData: string = "Branches"
  static subCategoryMaster: string = "SubCategory"
  static readonly user: string = "user";
  static readonly role: string = "role";
  static logout: string = "logout";
  static superAdmin: string = "superAdmin";
  static indentsList: string = "Indents List";
  static workAreaRts: string = "Rts CheckList"
  static rtsList: string = "Rts List"
  static indentRequests: string = "Indent Requests";
  static indentsdetail: string = "Indents Detail";
  static editUser: string = "Edit User";
  static editRole: string = "Edit Role";
  static productionPlanning: string = "Forecast";
  static restaurantManager: string = "restaurantManager";
  static kitchenOrder: string = "Kitchen Order";
  static kitchenManager: string = "kitchenManager";
  static forecastReport: string = "Reports";
  static printKitchen: string = "printKitchen";
  static branches: string = "branches";
  static dateRange: string = "dateRange";
  static readonly userAdmin = "User Admin";
  static readonly createRole = "Create Role";
  static admin: string = "admin";
  static readonly success = "success";
  static readonly purchaseList = "Purchase List";
  static readonly purchaseOrders = "Purchase Orders";
  static readonly purchaseOrdersList = "Purchase Order List";
  static readonly purchaseInvoice = "Purchase Invoice";
  static readonly specialPurchase = "Special Purchase";
  static readonly stockReceive = "Stock Receive";
  static readonly listPurchases = "Purchase List";
  static readonly purchaseController = "purchaseController";
  static readonly vendor = "vendor";
  static readonly vendorAdmin = "vendorAdmin";
  static readonly purchasheRequests = "Purchase Requests";
  static readonly purchasheRequestsList = "Purchase Requests List";
  static readonly detailedPr = "detailedPr";
  static readonly vendorPurchaseOrders = "vendorPurchaseOrders";
  static readonly createPurchaseOrder = "Special Order";
  static readonly createPurchaseOrderUpdated = "Create Purchase Request";
  static readonly sentQuotes = "sentQuotes";
  static readonly receivedQuotes = "receivedQuotes";
  static readonly restaurant = "restaurant";
  static readonly tenantId = "tenantId";
  static readonly category = "category"
  static readonly startDate = "startDate";
  static readonly endDate = "endDate";

  static readonly ibtType = "ibtType";
  static readonly ibtStatus = "ibtStatus";
  static readonly ibtProcessType = "ibtProcessType";

  static readonly grnDate = "grnDate";
  static readonly vendorIdData : any = "vendorIdData";
  static readonly grnType = "grnType";
  static readonly piScreen = "piScreen";
  static readonly stockConversion = "stockConversion";
  

  static readonly prVendorId : any = "vendorId";
  // static readonly prApprovalStatus : any = "approvalStatus";
  

  static readonly poVendorId : any = "vendorId";
  static readonly postatus : any = "status";
  // static readonly poApprovalStatus : any = "approvalStatus";

  // static readonly indentApprovalStatus  = "approvalStatus";
  static readonly indentWorkArea  = "workArea";
  
  static readonly postGrnVendorId  = "vendorId"; 
  // static readonly postGrnStatus  = "approvalStatus";
  
  static readonly GrnApprovalVendorId  = "vendorId"; 
  // static readonly GrnApprovalStatus  = "approvalStatus";
  static readonly GrnStatus  = "status";
  
  // static readonly csiIndentApprovalStatus  = "approvalStatus"; 
  static readonly csiIndentOrderStatus  = "orderStatus";
  
  static readonly dCRprId : any = "prId";

  static readonly itemCode  = "itemCode";
  static readonly packageName  = "packageName";
  static readonly userEmail : any = "userEmail";  
  static readonly uType = "uType";
  static readonly ibt = "Create Ibt";
  static readonly storeIndentList = "Store Indent List";
  static readonly getIbts = "Ibts";
  static readonly CentralIndentList = "Central Indent List";
  static readonly directIbts = "directIbts";
  static readonly ibtTypes = ['All', 'Incoming', 'Outgoing'];

  static forecastReportColumns: any[] = ['index', 'name', 'predicted', 'actual', 'estimated', 'aeDiff', 'aeDiffP', 'apDiff', 'apDiffP'];
  static prodPlanColumns: any[] = ['index', 'name', 'servingSize','predicted', 'estimated', 'difference', 'differencePercent'];
  static tableStatusData: any[] = ['index','restaurantId','event','createTs', 'status'];
  static kitchenOrderColumns: any[] = ['index', 'name', 'value', 'uom'];
  static purchaseListColumns: any[] = ['select', 'itemName', 'reqQty', 'orderQty', 'unitPrice','totalValue', 'unit', 'leadTime', 'openOrders', 'onHand', 'itemCode', 'vendorType'];
  static purchaseListLessColumns: any[] = ['select','category','subCategory', 'itemName', 'pkgName', 'vendorList', 'reqQty', 'orderQty', 'totalValue', 'unitPrice'];
  static vendorPurchaseReqColumns: any[] = ['select', 'category','subCategory', 'itemName', 'customerName', 'supplyDate', 'orderQty', 'deliverableQty', 'totalValue', 'unitPrice', 'unit', 'itemCode'];
  static receivedQuotesReqColumns: any[] = ['select', 'itemName', 'vendorName', 'orderQty', 'deliverableQty', 'supplyDate', 'totalValue', 'quotedUnitPrice', 'unit', 'itemCode'];
  static purchaseOrdersReqColumns: any[] = ['itemName', 'vendorName', 'orderQty', 'deliverableQty', 'supplyDate', 'totalValue', 'unitPrice', 'unit', 'itemCode', 'status'];
  static purchasheRequestsColumns: any[] = ['itemName', 'customerName', 'orderQty', 'deliverableQty', 'supplyDate', 'totalValue', 'unitPrice', 'unit', 'itemCode'];
  static purchaseOrdersColumns: any[] = ['poId', 'vendorName', 'eta', 'status','totalAmount','poTerms','approvalStatus','actionBtns',];
  static readonly vendorPurchaseOrdersColumns: any[] = ['poId', 'customerName', 'eta', 'status', 'actionBtns']
  static purchaseReqColumns: any[] = ['prId', 'vendorName', 'eta', 'totalAmount','approvalStatus','poTerms','status','actionBtns'];
  static vendorPrColumns: any[] = ['prId', 'restaurantName', 'status'];
  static readonly receivePurchaseOrder = 'receivePurchaseOrder';
  static readonly grns = 'Grns';
  static readonly grnList = 'Grn List';
  static createPoColumns: any[] = ['index', 'itemCode', 'itemName', 'pkgName', 'unitPerPkg', 'orderQty', 'unitPrice', 'totalValueExcTax','rate', 'taxAmt', 'totalValue', 'actionBtns'];
  static createPoColumns1: any[] = ['index','itemName', 'pkgName', 'unitPerPkg', 'orderQty', 'unitPrice', 'totalValueExcTax','rate', 'taxAmt', 'totalValue', 'actionBtns'];
  static receivePoColumns: any[] = ['select', 'index', 'itemName','itemCode', 'pkgName', 'orderQty', 'pendingQty', 'receivedQty','unitPrice', 'subTotal','taxbleValue','taxRate','taxAmt','totalValue', 'itemStatus'];
  static readonly approvePoDtlColumns: any[] = ['index', 'itemName', 'pkgName', 'orderQty', 'unitPrice', 'subTotal','taxAmt', 'totalValue'];
  static readonly approveIndentDtlColumns: any[] = ['index', 'itemName', 'pkgName', 'orderQty', 'unitPrice', 'subTotal'];
  static detailedRtvGrnColumns: any[] = ['select', 'itemName', 'pkgName', 'receivedQty', 'inStock', 'returnQty', 'unitPrice', 'unitPriceTax', 'returnRate','totalReturnAmt', 'adequate'];
  static rtvInfoColumns: any[] = ['index', 'itemName', 'pkgName', 'returnQty', 'grnUnitPriceWithTax', 'returnRate','totalReturnAmt'];
  static detailedIbtGrnColumns: any[] = ['index','itemCode', 'itemName', 'quantity', 'entryType', 'pkgName', 'receivedQty', 'unitPrice', 'totalValue'];
  static detailedPiColumns: any[] = ['select','index', 'itemName', 'quantity', 'entryType', 'pkgName', 'receivedQty', 'unitPrice', 'totalValue'];
  static detailedPoGrnColumns:any[] = ['index', 'itemName', 'pkgName', 'unitPerPkg', 'quantity', 'receivedQty', 'unitPrice', 'subTotal','taxRate', 'taxAmt', 'totalValue'];
  static detailedIbtColumns: any[] = ['index', 'itemName','pkgName','totalOutletStock','forcastQnty','expectedConsumption','inStock','quantity', 'pendingQty', 'receivedQty'];
  static detailedManualIbtColumns: any[] = ['index', 'itemName', 'entryType', 'pkgName', 'quantity', 'pendingQty', 'inStock', 'receivedQty', 'unitPrice', 'totalValue'];
  static VendorpurchaseOrdersReqColumns: any[] = ['poId', 'customerName', 'status'];
  static readonly grnColumns: any[] = ['grnId', 'poId', 'invId', 'vendorName','totalAmount', 'date','invoiceDate', 'action','delete'];
  static readonly piColumns: any[] = ['grnId', 'poId', 'invId', 'vendorName','total', 'date', 'status','action'];
  static readonly rtvListColumns: any[] = ['rtvId', 'grnId', 'invId', 'vendorName', 'date'];
  static readonly prTmpListColumns: String[] = ['tmpName', 'vendorName', 'amount', 'createDate','actionBtns']
  static readonly ibtColumns: any[] = ['ibtId', 'fromBranch', 'toBranch', 'createTs','status','recStatus','deliveryType','type','approvalStatus'];
  static readonly inventoryListColumns: any[] = ['index', 'itemName', 'entryType','pkgName', 'inStore','stockConversion'];
  static readonly intraBranchInventoryListColumns: any[] = ['index', 'itemName', 'entryType','pkgName'];
  static readonly indentAreaListColumns: any[] = ['index', 'itemName', 'inStore', 'inKitchen','Bakery','Staff Kitchen','Kitchen','Admin','Service','House Keeping','Bar', 'uom'];
  static readonly ktchenInventoryListColumns: any[] = ['index', 'itemName', 'inKitchen', 'uom'];
  static readonly issueIndentColumns: any[] = ['index', 'itemName', 'inStock', 'projectedSales', 'moq', 'issueQty', 'uom'];
  static readonly specialIndentColumns: any[] = ['index', 'itemName', 'entryType', 'pkgName', 'unitPrice', 'inStock', 'issueQty','totalPrice'];
  static readonly initiateRtsColoumn: any[] = ['itemName', 'pkgName', 'unitPrice', 'workAreaStock', 'returnQty', 'totalPrice'];
  static readonly indentListColumns: any[] = ['indentId', 'recipientArea', 'date', 'status'];

  static readonly rtsListColumns: any[] = ['rtsId', 'senderArea', 'date', 'status'];
  static readonly adjustInvListColoumns: any[] = ['adjId', 'workArea', 'date'];
  static readonly closeIndentColoumns: any[] = ['index', 'itemName', 'entryType', 'pkgName', 'unitPrice', 'inStore', 'reqQty','penQty',  'issueQty', 'totalPrice','adequate'];
  static readonly indentReviewColoumns: any[] = ['index', 'itemName', 'entryType', 'pkgName', 'unitPrice', 'reqQty', 'issueQty', 'totalPrice','adequate']
  static readonly indentReqPendingColoumns: any[] = ['index', 'itemName', 'entryType', 'pkgName', 'unitPrice', 'reqQty','penQty' ,'totalPrice']
  static readonly indentReqCompleteColoumns: any[] = ['index', 'itemName', 'entryType', 'pkgName', 'reqQty','unitPrice', 'issueQty','totalPrice']

  static readonly closeIndentColoumnsExclPrice: any[] = ['index', 'itemName', 'entryType', 'pkgName', 'inStore', 'reqQty',  'issueQty','adequate'];
  static readonly indentReviewColoumnsExclPrice: any[] = ['index', 'itemName', 'entryType', 'pkgName',  'reqQty', 'issueQty', 'adequate']
  static readonly indentReqPendingColoumnsExclPrice: any[] = ['index', 'itemName', 'entryType', 'pkgName',  'reqQty']
  static readonly indentReqCompleteColoumnsExclPrice: any[] = ['index', 'itemName', 'entryType', 'pkgName', 'reqQty', 'issueQty']

  static readonly workAreaRtsPendingColoumns: any[] = ['index', 'itemName', 'pkgName', 'unitPrice', 'returnQty', 'totalPrice']
  static readonly workAreaRtsCompleteColoumns: any[] = ['index', 'itemName', 'pkgName', 'unitPrice', 'returnQty', 'receivedQty', 'totalPrice']
  static readonly storeRtsColoumns: any[] = ['index', 'itemName', 'pkgName', 'unitPrice', 'returnQty', 'receivedQty', 'totalPrice']
  static readonly adjustInvDetailCol: any[] = ['index','itemName','entryType','pkgName','unitPrice', 'adjustType', 'adjustQty', 'totalPrice', 'reason']

  static readonly detailedGrn = "detailedGrn";
  static readonly detailedPi = "detailedPi";
  static readonly detailedIbt = "detailedIbt";
  static readonly vendorId = "vendorId";
  static readonly restaurantId = "restaurantId";
  static readonly destinationId = "destinationId";
  static readonly prId : any = "prId";
  static readonly specialFlag = "specialFlag";
  static readonly store = "store";
  static readonly getInventory = "Inventory List";
  static readonly kitchenStock = "WorkArea Stock";
  static readonly intraBranchTransfer = "Intra Branch Transfer";
  static readonly workAreaTransfer = "Workarea Transfer";
  static readonly emailStatus = "Approval Status";
  static readonly qrGenerator = "QR Generator";
  static readonly jobmonitor = "jobmonitor";
  static readonly StockConversion = "Stock Conversion";
  static readonly StockConversionList = "Stock Conversion List";
  static readonly DetailedStockConversionList = "Detailed Stock Conversion List";
  static readonly contract ="Contract";
  static readonly grnApproval = "grn approval";
  static readonly settings = "settings";
  static readonly partyOrder ="Party Order";
  static readonly scheduler ="scheduler";
  static readonly postGrnApproval ="Post Grn Approval";
  static readonly postGrnApprovalDetail ="Grn Approval Detail";
  static readonly IndentApproval ="Indent Approval";
  static readonly IndentApprovalDetail ="Indent Approval Detail";
  static readonly csiApproval ="CSI Approval";
  static readonly csiApprovalDetail ="csi Approval Detail";

  static readonly CreateRequisition ="Create Requisition";
  static readonly CreateRequisitionList ="Purchase Indent List";
  static readonly DetailedCreateRequisition ="Detailed Create Requisition";
  static readonly CreatePurchaseRequisition ="Create Purchase Indent";
  static readonly PurchaseStatus ="Purchase Status";
  static readonly kitchenIndent = "Kitchen Indents";
  static readonly indents = "indents";
  static readonly closingStock = "Stock Closing";
  static readonly storeclosing = "Store Closing";
  static readonly closing = "Closing";
  static readonly transferClosing = "Transfer Closing";
  static readonly stockClosure = "Stock Closure";
  static readonly workareaClosing = "Workarea Closing";
  static readonly specialIndents = "Special Indents";
  static readonly storeIndents = "Create Indent";
  static readonly CreateStoreIndent = "Create Central Store Indent";
  static readonly adjustInventory = "Adjust Inventory";
  static readonly adjustWorkAreaInventory = "Adjust WorkArea Inv";
  static readonly purchaseSetting = "Approval Setting";
  static readonly poApproval = "Purchase Order Approval";
  static readonly purchaseApproval = "Purchase request Approval";
  static readonly purchaseIndentApproval = "Purchase Indent Approval"; //duplicate route for pur req approval
  static readonly purchaseApprovalDtl = "poApprovalDetail";
  static readonly purchaseTemplate = "Purchase Template";
  static readonly purchaseTemplateDetail = "purchaseTemplateDetail";
  static readonly kitchenClosingColumns = ['select', 'index', 'itemName', 'closingStock', 'uom','Action']
  static readonly adjustInventoryColumns = ['index', 'itemName', 'pkgName', 'entryType', 'unitPrice', 'workAreaStock', 'adjustType', 'adjustQty', 'totalPrice', 'reason']
  static readonly storeClosingColumns = ['index', 'itemName', 'inStock', 'uom']
  static readonly adjustInventoryReasons = [{name : 'Decrease'}, {name : 'Increase' , increment : true},{name : 'Expired'}, {name :'Wastage'}, {name :'Previous Stock Entry'}, {name : 'Manually Added', increment : true}]
  static readonly excludeFromInventory = []
  static indentPredictions: string = "Indent Predictions";
  static initiateRtv: string = "initiateRtv"
  static rtvList: string = "Rtvs"
  static detailedRtv: string = "detailedRtv"
  static rtsDetail: string = "rtsDetail"
  static initiateRts: string = "Return To Store"
  static readonly adjustInventoryList: string = "AdjustInv List"
  static readonly adjustInventoryDetail: string = "AdjustInvDetail"
  static readonly adjustInvRequests: string = "AdjustInv Requests"
  static readonly indentPredictionsColumns: any[] = ['index', 'itemName', 'pkgName', 'inStock','PredictedQty' ,'workAreaStock', 'estimatedQty', 'reqQty'];
  constructor() { }

}
