import { ChangeDetectionStrategy, Component, inject, ElementRef, ViewChild, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormWrapperComponent } from 'src/app/components/form-wrapper/form-wrapper.component';
import { TitleCardComponent } from 'src/app/components/title-card/title-card.component';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { ReactiveFormsModule } from '@angular/forms';
import { SigninFormService } from './SigninForm.service';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-signup',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    FormWrapperComponent,
    TitleCardComponent,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    ReactiveFormsModule,
  ],
  templateUrl: './signin.component.html',
  styleUrls: [
    '../../../styles/login-signup-pages.scss',
    './signin.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [SigninFormService],
})
export class SigninComponent implements OnInit {
  @ViewChild('submitbtn', { static: true }) submitbtn!: ElementRef;
  signinService = inject(SigninFormService);
  form = this.signinService.form;
  passwordControl = this.signinService.passwordControl;
  emailControl = this.signinService.emailControl;
  tenantControl = this.signinService.tenantControl;
  isInvalid = this.signinService.isInvalid;
  load : any = false;
  isPasswordVisible: boolean = false;
    constructor(private cd: ChangeDetectorRef,) {
  }

  ngOnInit(): void {
    sessionStorage.clear();
  }
  togglePasswordVisibility() {
    this.isPasswordVisible = !this.isPasswordVisible;
  }

  resetForm() {
    this.signinService.resetForm();

  }

  submit() {
    this.load = true;
    this.signinService.handleSubmit();
    this.signinService.check(this.load);
    this.signinService.isChecked.subscribe(isChecked => {
      this.load = isChecked;
      this.cd.detectChanges();
    });
  }


}
