import { Injectable, inject } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { BehaviorSubject, map } from 'rxjs';
import { AuthService } from 'src/app/services/auth.service';
import { ToasterService } from 'src/app/services/toaster.service';
@Injectable()
export class SigninFormService {
  private isCheckedSubject = new BehaviorSubject<boolean>(false);
  isChecked = this.isCheckedSubject.asObservable();

  formBuilder = inject(FormBuilder);
  form: FormGroup<{
    tenantId: FormControl<string | null>;
    email: FormControl<string | null>;
    password: FormControl<string | null>;
    uType: FormControl<string | null>;
  }>;
  logInKey: boolean;

  constructor(
    private toaster: ToasterService,
    private router: Router,
    private auth: AuthService
  ) {
    this.form = this.createFormGroup();
  }

  get formErrors$() {
    return this.form.valueChanges.pipe(map(() => this.form.errors));
  }

  private createFormGroup() {
    return this.formBuilder.group({
      tenantId: this.formBuilder.control('', {
        validators: [Validators.required],
      }),
      email: this.formBuilder.control('', {
        validators: [Validators.email, Validators.required],
      }),
      password: this.formBuilder.control('', {
        validators: [Validators.required],
      }),
      uType: this.formBuilder.control('restaurant', {
        validators: [Validators.required],
      })
    });
  }

  get emailControl() {
    return this.form.get('email') as FormControl;
  }
  get tenantControl() {
    return this.form.get('tenantId') as FormControl;
  }
  get passwordControl() {
    return this.form.get('password') as FormControl;
  }

  resetForm() {
    Object.values(this.form.controls).forEach((c) => {
      c.reset('');
      c.setErrors(null)
    });
  }

  isInvalid() {
    return this.form.invalid;
  }

  handleSubmit() {
    Object.values(this.form.controls).forEach((c) => {
      c.updateValueAndValidity();
    });
  
    this.auth.login(this.form.value).subscribe({
      next: (res: any) => {
        if (res) {
          this.router.navigate(['dashboard/home']);
        } else {
          this.toaster.showError("Login failed! Invalid Credentials", 5000);
          this.check(false);
        }
      },
      error: (err: any) => {
        console.error("Login error: ", err);
  
        const errorMsg = err?.message || 'Login failed!';
        if (errorMsg.includes('No internet')) {
          this.toaster.showError("No internet. Please check your connection and try again.", 5000);
        } else if (errorMsg.includes('Invalid credentials')) {
          this.toaster.showError("Login failed! Invalid Credentials", 5000);
        } else {
          this.toaster.showError(errorMsg, 5000);
        }
  
        this.check(false);
      }
    });
  }

  check(isChecked){
    this.isCheckedSubject.next(isChecked);
  }
}
