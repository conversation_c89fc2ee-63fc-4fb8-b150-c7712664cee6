<div class="restaurant-signin-container">

  <!-- Main Content -->
  <div class="signin-card">
    <!-- Restaurant Header -->
    <div class="restaurant-header">
      <div class="logo-section">
        <div class="restaurant-logo">
          <img src="assets/images/digitory-logo.png" alt="Digitory Logo" class="logo-image">
          <div class="logo-subtitle">Inventory Data Management</div>
        </div>
      </div>

      <div class="welcome-section">
        <h2 class="welcome-title">Welcome Back</h2>
        <p class="welcome-subtitle">Streamline your inventory and data operations</p>
        <div class="decorative-line"></div>
      </div>
    </div>

    <!-- Sign In Form -->
    <div class="form-container">
      <form class="restaurant-form" [formGroup]="form">
        <div class="form-group">
          <mat-form-field class="custom-form-field" appearance="outline">
            <mat-label>Tenant ID</mat-label>
            <input matInput formControlName="tenantId" placeholder="Enter your tenant ID" />
            <mat-icon matPrefix>business</mat-icon>
          </mat-form-field>
        </div>

        <div class="form-group">
          <mat-form-field class="custom-form-field" appearance="outline">
            <mat-label>Email Address</mat-label>
            <input matInput formControlName="email" placeholder="<EMAIL>" />
            <mat-icon matPrefix>email</mat-icon>
          </mat-form-field>
        </div>

        <div class="form-group">
          <mat-form-field class="custom-form-field" appearance="outline">
            <mat-label>Password</mat-label>
            <input matInput formControlName="password" #passwordInput
                   [type]="isPasswordVisible ? 'text' : 'password'"
                   placeholder="Enter your password" />
            <mat-icon matPrefix>lock</mat-icon>
            <mat-icon matSuffix (click)="togglePasswordVisibility()" class="password-toggle">
              {{ isPasswordVisible ? 'visibility_off' : 'visibility' }}
            </mat-icon>
          </mat-form-field>
        </div>

        <div class="form-actions">
          <button mat-stroked-button class="cancel-btn" (click)="resetForm()" type="button">
            Reset
          </button>
          <button #submitbtn mat-raised-button class="signin-btn"
                  [disabled]="form.invalid" type="submit" (click)="submit()">
            <div *ngIf="load" class="loading-spinner">
              <mat-icon class="spinning">hourglass_empty</mat-icon>
            </div>
            <span *ngIf="!load">
              Sign In
            </span>
          </button>
        </div>
      </form>
    </div>

    <!-- Footer -->
    <div class="restaurant-footer">
      <div class="footer-content">
        <p class="copyright">© 2020-2024 Digitory Solutions Pvt Ltd</p>
      </div>
    </div>
  </div>
</div>