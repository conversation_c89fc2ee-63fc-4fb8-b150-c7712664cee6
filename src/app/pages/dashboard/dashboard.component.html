<div class="dashboard-container">
  <app-dashboard-toolbar
    [menuItems]="menuItems"
    [logoUrl]="logoUrl">
  </app-dashboard-toolbar>

  <!-- Main content area -->
  <div class="content mat-elevation-z8" *ngIf="!showBanner && isReady">
    <router-outlet></router-outlet>
  </div>

  <!-- Loading indicator when navigation is not ready -->
  <div class="content mat-elevation-z8 loading-container" *ngIf="!showBanner && !isReady">
    <div class="loading-spinner">
      <mat-icon class="spin">refresh</mat-icon>
      <p>Loading application...</p>
    </div>
  </div>

  <!-- Update banner -->
  <div class="closingContainer" *ngIf="showBanner">
    <div class="closingContainerDatas">
      <mat-card>
        <div class="closeMsg">
          {{message}}
        </div>
        <div class="text-align-center text-center m-3">
          <button mat-button mat-raised-button (click)="refreshPage()">
            click to update
          </button>
        </div>
      </mat-card>
    </div>
  </div>
</div>