import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, Input, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterOutlet } from '@angular/router';
import { DashboardToolbarComponent } from 'src/app/components/dashboard-toolbar/dashboard-toolbar.component';
import { FixedFabButtonComponent } from 'src/app/components/fixed-fab-button/fixed-fab-button.component';
import { DashboardMenuComponent } from 'src/app/components/dashboard-menu/dashboard-menu.component';
import { ResponsiveService } from 'src/app/services/responsive-service.service';
import { DashboardMenuService } from 'src/app/services/dashboard-menu.service';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from 'src/app/services/auth.service';
import { ShareDataService } from 'src/app/services/share-data.service';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { TimeOutService } from 'src/app/services/time-out.service';
import { InventoryService } from 'src/app/services/inventory.service';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { AccessDeniedDialogComponent } from 'src/app/components/access-denied-dialog/access-denied-dialog.component';
@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    MatDividerModule,
    MatIconModule,
    DashboardMenuComponent,
    FixedFabButtonComponent,
    DashboardToolbarComponent,
    MatCardModule,
    MatButtonModule,
    MatDialogModule
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: DashboardMenuService,
      useFactory: (responsiveService: ResponsiveService) => {
        return new DashboardMenuService(responsiveService);
      },
      deps: [ResponsiveService],
    },
  ],
})
export class DashboardComponent implements OnInit {
  private dashboardMenuService = inject(DashboardMenuService);
  public isReady = false;
  public dashboardPanel: {
    icon: string;
    title: string;
    links: { label: string; routerLink: string }[];
  }[] = [];
  public menuItems: any[] = null;
  public user: any;
  public userRole: any;
  public access: any;
  @Input() showBanner: boolean = false;
  @Input() message: string = 'A new version of our software is now available!';
  versionNumber: string = 'v2.17.1';
  logoUrl: string
  constructor(
    private auth: AuthService,
    private sharedData: ShareDataService,
    private cd: ChangeDetectorRef,
    private sessionTimeoutService: TimeOutService,
    private api: InventoryService,
    private router: Router,
    private dialog: MatDialog

 ) {
    this.user = this.auth.getCurrentUser();
    this.userRole = this.auth.getCurrRole();
    this.sharedData.sendVersionNumber(this.versionNumber)
    this.auth.getRolesList({ tenantId: this.user.tenantId }).subscribe((data) => {
      this.sharedData.checkMapping(data['bulkMapping'])
      this.logoUrl = data.tenantDetails.logo;
      if (this.versionNumber !== data['versionUI']){
        this.showBanner = true
        this.sharedData.sendVersionNumber(this.versionNumber)
      }else{
        this.showBanner = false
      }
      if(data['result'] === 'success'){
        this.sharedData.sendTimeOutData(600)
        this.sessionTimeoutService.start();
      }
      this.cd.detectChanges()
    });

  }

  get showSidenav$() {
    return this.dashboardMenuService.showSidenav$;
  }

  get sidenavType$() {
    return this.dashboardMenuService.sidenavType$;
  }

  get showSmallDeviceMenuButton$() {
    return this.dashboardMenuService.showSmallDeviceMenuButton$;
  }

  ngOnInit(): void {
    // Set initial loading state
    this.isReady = false;

    // Ensure menuItems is null to show placeholders
    this.menuItems = null;

    try {
      // Safely parse access data from session storage
      let data = {};
      try {
        const accessData = sessionStorage.getItem('access');
        if (accessData) {
          data = JSON.parse(accessData);
        }
      } catch (error) {
        console.error('Error parsing access data:', error);
      }

      // Handle settings access
      if(data['settings'] || (this.access && this.access['settings'])){
        const lowercasedRoles = data['settings'] ?? this.access['settings'].map((role: string) => role.toLowerCase());
        const lowercasedData = this.user.role.toLowerCase();
        this.sharedData.checkSetting(lowercasedRoles.includes(lowercasedData));
      } else {
        this.sharedData.checkSetting(false);
      }

      // Handle bulk excel upload access
      if((data && data['bulkExcel']) || (this.access && this.access['bulkExcel'])){
        const lowercasedUpload = data['bulkExcel'] ?? this.access['bulkExcel'].map((role: string) => role.toLowerCase());
        const lowercasedUploadData = this.user.role.toLowerCase();
        this.sharedData.checkUploads(lowercasedUpload.includes(lowercasedUploadData));
      } else {
        this.sharedData.checkUploads(false);
      }

      // Simply load navigation items - no caching needed
      this.getNavigationItems();

    } catch (error) {
      console.error('Error in dashboard initialization:', error);

      // Ensure we still try to load navigation items even if there's an error
      this.getNavigationItems();
    }
  }

  getNavigationItems(retryCount = 0, maxRetries = 3) {
    const ROUTES = [
      { path: '/dashboard/inventory', title: 'Inventory Management', icon: 'table_chart', class: '', dbAccess: "inventory" },
      { path: '/dashboard/user', title: 'User & Branch Management', icon: 'person', class: '', dbAccess: "user" },
      { path: '/dashboard/recipe', title: 'Recipe Management', icon: 'fastfood', class: '', dbAccess: "recipe" },
      { path: '/dashboard/party', title: 'Party Management', icon: 'event_note', class: '', dbAccess: "party" },
      { path: '/dashboard/smart-dashboard', title: 'Smart Dashboard', icon: 'analytics', class: '', dbAccess: "dashboard" },
      { path: '/dashboard/account', title: 'Account Setup', icon: 'add_to_photos', class: '', dbAccess: "accountSetup" },
    ];

    // Set menuItems to null to show placeholders during loading
    this.menuItems = null;

    if (this.user.tenantId != '100000') {
      this.api.getUIAccess(this.user.tenantId).subscribe({
        next: (res) => {
          if(res['success']) {
            this.access = res['access'];

            // Initialize menuItems as an array before pushing items
            this.menuItems = [];

            ROUTES.forEach((el) => {
              if(this.access.hasOwnProperty(el['dbAccess'])) {
                (this.access[el['dbAccess']]['status'] === true &&
                 this.access[el['dbAccess']]['access'].map((access: string) => access.toLowerCase()).includes(this.user.role.toLowerCase()))
                  ? this.menuItems.push(el)
                  : undefined;
              }
            });

            // Check if no menu items were added after processing all routes
            if (this.menuItems.length === 0) {
              // Show custom access denied dialog and initiate immediate logout
              const dialogRef = this.dialog.open(AccessDeniedDialogComponent, {
                autoFocus: false,
                disableClose: true,
                panelClass: 'access-denied-dialog',
                data: {
                  message: "You don't have access to perform actions in data management, please get required access from Admin",
                  title: 'Access Denied',
                },
              });

              dialogRef.afterClosed().subscribe(() => {
                this.logout();
              });
              return; // Exit early to prevent further processing
            }
          } else {
            this.access = {};
          }

          // Set ready state and trigger change detection
          this.isReady = true;
          this.cd.markForCheck();

          // If no menu items were loaded and we haven't exceeded max retries, try again
          if ((!this.menuItems || this.menuItems.length === 0) && retryCount < maxRetries) {
            console.log(`Retrying navigation items load, attempt ${retryCount + 1}`);
            setTimeout(() => {
              this.getNavigationItems(retryCount + 1, maxRetries);
            }, 1000); // Wait 1 second before retrying
          }
        },
        error: (err) => {
          console.error('Error loading navigation items:', err);

          // On error, if we haven't exceeded max retries, try again
          if (retryCount < maxRetries) {
            console.log(`Retrying navigation items load after error, attempt ${retryCount + 1}`);
            setTimeout(() => {
              this.getNavigationItems(retryCount + 1, maxRetries);
            }, 1000); // Wait 1 second before retrying
          } else {
            // If we've exceeded max retries, set default navigation
            console.log('Max retries exceeded, setting default navigation');
            this.setDefaultNavigation();

            // Set ready state and trigger change detection
            this.isReady = true;
            this.cd.markForCheck();
          }
        }
      });
    } else {
      this.menuItems = [{ path: '/dashboard/account', title: 'Account Setup', icon: 'add_to_photos', class: '', dbAccess: "accountSetup" }];

      // Set ready state and trigger change detection
      this.isReady = true;
      this.cd.markForCheck();
    }
  }

  // Set default navigation items if API fails
  setDefaultNavigation() {
    this.menuItems = [
      // { path: '/dashboard/home', title: 'Dashboard', icon: 'dashboard', class: '' }
    ];

    // Add account setup for admin users
    if (this.user.tenantId === '100000') {
      this.menuItems.push({ path: '/dashboard/account', title: 'Account Setup', icon: 'add_to_photos', class: '' });
    }
  }

  toggleMenu() {
    this.dashboardMenuService.toggleMenu();
  }

  generateLinks(module: string[]) {
    const links = module.map((label: string) => {
      return { label, routerLink: `/dashboard/${label}` };
    });
    return links;
  }

  @HostListener('document:keydown.control.shift.r', ['$event'])
  onCtrlShiftR(event: KeyboardEvent) {
    event.preventDefault();
    window.location.reload();
  }

  refreshPage() {
    const event = new KeyboardEvent('keydown', {
      key: 'r',
      code: 'KeyR',
      ctrlKey: true,
      shiftKey: true
    });
    document.dispatchEvent(event);
  }

  logout() {
    // Clear all stored data
    localStorage.clear();
    sessionStorage.clear();

    // Navigate to signin page
    this.router.navigate(['/signin']);
  }
}


