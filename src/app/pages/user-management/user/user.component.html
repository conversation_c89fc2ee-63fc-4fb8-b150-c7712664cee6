<div class="closeBtn" *ngIf="isDuplicate == true">
  <mat-icon (click)="close()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
</div>
<div class="registration-form py-2 px-3">
  <div class="mt-3 smallDialog" *ngIf="isDuplicate == true">
    <div class="col-md-12">
      <div class="text-center my-2 p-2 bottomTitles">
        <span>User Form</span>
      </div>
      <mat-form-field appearance="outline">
        <mat-label>Search Email</mat-label>
        <input matInput placeholder="Email" aria-label="email" [matAutocomplete]="auto1" [formControl]="emailControl"
          (keyup.enter)="addOption('users')">
        <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelected('users', $event.option)">
          <mat-option *ngFor="let item of searchOptions | async" [value]="item">
            <span>{{ item }}</span>
          </mat-option>
        </mat-autocomplete>
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
      <div class="text-end">
        <button (click)="addOption('users')" mat-raised-button color="accent" [disabled]="emailControl.invalid">
          <div *ngIf="loadBtn" class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <mat-icon *ngIf="!updateBtnActive">library_add</mat-icon>
          <mat-icon *ngIf="updateBtnActive">update</mat-icon>
          <span *ngIf="updateBtnActive">Update</span>
          <span *ngIf="!updateBtnActive">Add</span>
        </button>
      </div>
    </div>
  </div>

  <div *ngIf="isDuplicate == false">
    <!-- <div class="text-center my-2 p-2 bottomTitles"> User Form </div> -->
    <div class="mb-2 topCreateAndUpdateBtn"  style="float: right; padding-top: 0.5rem;">
      <button class="stepperBtns" (click)="submitUser()" style="margin-right: 5px;" mat-raised-button *ngIf="!showUpdateBtn" 
      color="accent" matTooltip="create" [disabled]="isCreateButtonDisabled">
        <mat-icon>add_circle</mat-icon>Create
      </button>
      <button mat-raised-button *ngIf="showUpdateBtn" color="accent" style="margin-right: 5px;" (click)="updateUser()" matTooltip="update"
      [disabled]="userFormBtn || isUpdateButtonDisabled">
        <div *ngIf="isLoad" class="spinner-border" role="status">
          <span class="sr-only">Loading...</span>
        </div>
        Update
      </button>
      <button *ngIf="isDuplicate == false" mat-raised-button color="warn" style="margin-right: 5px;" (click)="close()" matTooltip="Close">
        <mat-icon>close</mat-icon>
        Close
      </button>
    </div>

    <div class="my-2 p-3 bottomTitles" *ngIf="!isDuplicate">
      User Form
    </div>

    <div class="my-3">
      <form [formGroup]="userForm">
        <div class="row">
          <div class="col-md-6">
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
              <mat-label>Email</mat-label>
              <input formControlName="email" matInput placeholder="Email">
              <mat-icon matSuffix>email</mat-icon>
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Password</mat-label>
              <input formControlName="password" matInput placeholder="Password">
              <mat-icon matSuffix>remove_red_eye</mat-icon>
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>First Name</mat-label>
              <input formControlName="firstName" matInput placeholder="First Name">
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Last Name</mat-label>
              <input formControlName="lastName" matInput placeholder="Last Name">
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Mobile</mat-label>
              <input formControlName="mobile" matInput placeholder="Mobile" autocomplete="off" maxlength="10">
              <mat-icon matSuffix>drag_indicator</mat-icon>
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label> Role </mat-label>
              <mat-select formControlName="roles" matInput placeholder="Selected Role">
                <mat-option *ngFor="let option of roles" [value]="option">
                  {{option}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Permitted Branches </mat-label>
              <mat-select formControlName="branchId" multiple matInput placeholder="Permitted Branches"
                (selectionChange)="getWorkAreas($event.value)">
                <mat-option>
                  <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                    [formControl]="branchIdFilterCtrl"></ngx-mat-select-search>
                </mat-option>
                <mat-option class="hide-checkbox" (click)="branchIdsToggleSelectAll()">
                  <mat-icon matSuffix>check_circle</mat-icon>
                  Select All / Deselect All
                </mat-option>
                <mat-option *ngFor="let option of branchId | async" [value]="option">
                  {{option}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Permitted WorkAreas</mat-label>
              <mat-select formControlName="workAreas" multiple>
                <mat-option>
                  <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                    [formControl]="workAreaFilterCtrl"></ngx-mat-select-search>
                </mat-option>
                <mat-option class="hide-checkbox" (click)="toggleSelectAll()">
                  <mat-icon matSuffix>check_circle</mat-icon>
                  Select All / Deselect All
                </mat-option>
                <mat-optgroup *ngFor="let group of workAreas | async" [label]="group.restaurantIdOld.split('@')[1]"
                  [disabled]="group.disabled">
                  <mat-option *ngFor="let data of group.workAreas" [value]="data">
                    {{data | uppercase}}
                  </mat-option>
                </mat-optgroup>
              </mat-select>
            </mat-form-field>

            <mat-error class="formError" *ngIf="showWorkAreaError">
              * select at least one workarea in every branch
            </mat-error>

          </div>
          <div>
            <div class="col">
              <label>Do you want to discontinue?</label>
              <mat-radio-group formControlName="Discontinued" aria-labelledby="example-radio-group-label">
                <mat-radio-button value="yes">Yes</mat-radio-button>
                <mat-radio-button value="no">No</mat-radio-button>
              </mat-radio-group>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>