import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { MatCardModule } from '@angular/material/card';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { InventoryService } from 'src/app/services/inventory.service';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { ShareDataService } from 'src/app/services/share-data.service';
import { AuthService } from 'src/app/services/auth.service';
import { NotificationService } from 'src/app/services/notification.service';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSliderModule } from '@angular/material/slider';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDividerModule } from '@angular/material/divider';
import { AutocompleteComponent } from 'src/app/components/autocomplete/autocomplete.component';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatStepperModule } from '@angular/material/stepper';
import { Observable, ReplaySubject, Subject, first, map, startWith, takeUntil } from 'rxjs';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MasterDataService } from 'src/app/services/master-data.service';
import { Router } from '@angular/router';
@Component({
  selector: 'app-role',
  standalone: true,
  imports: [
    FormsModule,
    MatDialogModule,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatInputModule,
    MatSliderModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSelectModule,
    MatRadioModule,
    MatAutocompleteModule,
    MatDividerModule,
    AutocompleteComponent,
    MatTableModule,
    NgxMatSelectSearchModule,
    MatCheckboxModule,
    MatTooltipModule,
    MatToolbarModule,
    MatSnackBarModule,
    NgxSkeletonLoaderModule,
    MatStepperModule
  ],
  templateUrl: './role.component.html',
  styleUrls: ['./role.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RoleComponent {
  roleForm!: FormGroup;
  user: any;
  emailControl = new FormControl('', [Validators.required, Validators.email]);
  isUpdateActive: boolean = false;
  searchOptions: Observable<string[]>;
  question = 'Would you like to add "';
  isDuplicate: boolean;
  baseData: any;
  updateBtnActive: boolean = false;
  loadBtn: boolean = false;
  showUpdateBtn: boolean = false;
  isLoad: boolean = false;
  branchIds: any;
  public workAreaBank: any[] = [];
  public workAreaFilterCtrl: FormControl = new FormControl();
  public workAreas: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public pagesBank: any[] = [];
  public PageFilterCtrl: FormControl = new FormControl();
  public pages: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  screens: any;
  roleFromBtn : boolean = true;
  modules : any[];
  modulesBank: Observable<string[]>;
  isCreateButtonDisabled = false;
  isUpdateButtonDisabled = false;
  filteredPages: any;
  filteredModule: any;
  disableModule: any;
  constructor(
    private fb: FormBuilder,
    private api: InventoryService,
    public dialog: MatDialog,
    private router: Router,
    private masterDataService: MasterDataService,
    private sharedData: ShareDataService,
    private auth: AuthService,
    private notify: NotificationService,
    private cd: ChangeDetectorRef,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
  ) {
    this.user = this.auth.getCurrentUser();
    this.isDuplicate = this.dialogData.key;
    this.baseData = this.sharedData.getBaseData().value;
    // this.getScreens();
    // this.getPages();
    this.sharedData.getRole.pipe(first()).subscribe(obj =>{
      this.searchOptions = this.emailControl.valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), obj)));
    });

    this.roleForm = this.fb.group({
      role: new FormControl<string>('', Validators.required),
      module: new FormControl<string>('', Validators.required),
      Page: new FormControl<string>('', Validators.required),
      tenantId: new FormControl<string>(''),
      modified: new FormControl<string>(''),
      row_uuid: new FormControl<string>(''),
      Discontinued: new FormControl<string>('no'),
    }) as FormGroup;
    
    this.sharedData.getItemNames.pipe(first()).subscribe(obj => {  
          this.pagesBank = this.dialogData.page;
          this.pages.next(this.pagesBank.slice());
          this.PageFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.itemFilter(this.pagesBank, this.PageFilterCtrl, this.pages);
          });
      

      if (this.dialogData.key == false) {
        this.showUpdateBtn = true;
        this.isUpdateActive = true;
        this.setValuesForForm(this.dialogData.elements);
      }else if(this.dialogData.key == null){
        this.setValuesForForm(this.dialogData.elements);
      }
      this.filteredPages = this.dialogData.filteredPages
      this.filteredModule = this.dialogData.filteredModule
      this.disableModule = this.dialogData.disableModule
    })

    this.sharedData.getRolesModule.pipe(first()).subscribe(data =>{
      this.modules = data
      this.modulesBank = this.roleForm.get('module').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.modules)));
    });
  }

  close() {
    this.dialog.closeAll();
    this.masterDataService.setNavigation('Roles');
    this.router.navigate(['/dashboard/home']);
    this.dialog.closeAll();
  }

  setValuesForForm(form){
    this.roleForm.setValue({
      role: form.role,
      module: form.module,
      Page: form.Page,
      tenantId: form.tenantId,
      modified: form.modified,
      row_uuid: form.row_uuid,
      Discontinued: ['no','NO' ,'No', 'N', null,''].includes(form['Discontinued']) ? 'no' : 'yes'
    });
    this.roleFromBtn = false;
  }

  _filter(value: string, input: string[]): string[] {
    const filteredArray = input.filter(value => value !== null && value !== undefined);
    let filterValue = value.toLowerCase();
    let filtered = filteredArray.filter(option => option.toLowerCase().includes(filterValue));
    if (filtered.length == 0) {
      filtered = [this.question + value + '"'];
    }
    return filtered
  }


  convertRolesKeys() {
    const keyData = [
      ['role' , 'role' ], 
      ['module' , 'module' ], 
      ['Page' , 'Page' ], 
      ['tenantId' , 'tenantId' ],
      ['modified' , 'modified' ], 
      ['row_uuid' , 'row_uuid' ],
      ['Discontinued' , 'Discontinued' ]
    ];
    this.convertRolesDataType(this.roleForm.value)
    const temp = {};
    keyData.forEach((key) => {
      let value = this.roleForm.value[key[1]];
      temp[key[0]] = value || '';
    });
    return temp
  }

  convertRolesDataType(jsonData){
    this.roleForm.setValue({
      role: jsonData.role,
      module: jsonData.module,
      Page: jsonData.Page,
      tenantId: jsonData.tenantId,
      modified: jsonData.modified,
      row_uuid: jsonData.row_uuid,
      Discontinued: jsonData.Discontinued
  });
  }

  submitRole(){
    this.isCreateButtonDisabled = true;
    this.isLoad = true;
    if(this.roleForm.invalid) {
      this.roleForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields')
      this.isLoad = false;
      this.isCreateButtonDisabled = false;
      this.cd.detectChanges();
    }else {
      let updatedData = this.convertRolesKeys();
      updatedData['modified'] = "yes";
      updatedData['row_uuid'] = this.findId();
      updatedData['tenantId'] = this.user.tenantId.toString();
      if (Object.keys(this.baseData).length > 0) {
        let temp = {}
        temp['Roles'] = this.baseData['Roles'];
        temp['Roles'].unshift(updatedData);
        temp['Roles'] = temp['Roles'].filter(item => item.modified === "yes");
        this.api.updateData({
          'tenantId' :  this.user.tenantId,
          'userEmail' : this.user.email,
          'data' : temp,
          'type' : 'user'
        }).pipe(first()).subscribe({
          next: (res) => {
            if (res['success']) {
              this.isLoad = false;
              this.cd.detectChanges();
              this.notify.snackBarShowSuccess('Created successfully');
              this.close();
            }
          },
          error: (err) => {
            console.log(err);
          }
        });
      } else {
        this.isCreateButtonDisabled = false;
        this.isLoad = false;
      }
    }
  }

  findId(): any {
    const highestRowUuid = Math.max(...this.baseData.Roles.map(item => parseInt(item.row_uuid) || 0));
    return (highestRowUuid + 1).toString();
  }

  // getPages(){
  //   this.api.getPages(this.user.tenantId).pipe(first()).subscribe({
  //     next: (res) => {
  //       if (res['success']) {
  //         this.pagesBank = res['pages'];
  //         this.pages.next(this.pagesBank.slice());
  //         this.PageFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
  //           this.itemFilter(this.pagesBank, this.PageFilterCtrl, this.pages);
  //         });
  //       }
  //     },
  //     error: (err) => { console.log(err) }
  //   });
  // }

  protected itemFilter(bank:any, form:any, data:any) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(
      bank.filter(data => data.toLowerCase().indexOf(search) > -1)
    );
  }

  updateRole(){
    this.isUpdateButtonDisabled = true;
    this.isLoad = true;
    if(this.roleForm.invalid) {
      this.roleForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields')
      this.isLoad = false;
      this.isUpdateButtonDisabled = false;
      this.cd.detectChanges();
    }else {
      this.roleForm.get('modified').setValue( "yes")
      let updatedData = this.convertRolesKeys();
      updatedData['tenantId'] = this.user.tenantId.toString()

      if (Object.keys(this.baseData).length > 0) {
        let temp = {}
        temp['Roles'] = this.baseData['Roles']
        let required = temp['Roles'].find((el) => el.row_uuid == updatedData['row_uuid'])
        let index = temp['Roles'].indexOf(required)
        temp['Roles'][index] = updatedData;

        this.api.updateData({
          'tenantId' :  this.user.tenantId,
          'userEmail' : this.user.email,
          'data' : temp,
          'type' : 'user'
        }).pipe(first()).subscribe({
          next: (res) => {
            if (res['success']) {
              this.isLoad = false;
              this.cd.detectChanges();
              this.notify.snackBarShowSuccess('Updated successfully');
            }
          },
          error: (err) => { console.log(err) }
        });
      } else {
        this.isUpdateButtonDisabled = false;
        this.isLoad = false;
      }
    }
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  Filter(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    const filteredBank = bank.map(item => {
      const filteredWorkAreas = item.workAreas.filter(workArea => workArea.toLowerCase().indexOf(search) > -1);
      return { ...item, workAreas: filteredWorkAreas };
    });
    data.next(filteredBank);
  }

  toggleSelectAll() {
    const control = this.roleForm.controls['workAreas'];
    let data = [...this.workAreaBank.map(location => location.workAreas)];
    const flattenedArray = [].concat(...data);
    if (control.value.length - 1 === flattenedArray.length) {
      control.setValue([]);
    } else {
      control.setValue(flattenedArray);
    }
  }

  getScreens(){
    this.api.getScreens(this.user.tenantId).pipe(first()).subscribe(
      {next: (res) => {
        if (res['success']) {
          this.screens = res['screens']
        }
      },
        error: (err) => { console.log(err) }
    });
  }

  optionSelected(option: any) {
    if (option.value.indexOf(this.question) === 0) {
      this.addOption();
    }
    this.modulesBank = this.roleForm.get('module').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.modules)));
  }

  addOption() {
    this.roleForm.controls['module'].patchValue(this.removePromptFromOption(this.roleForm.value.module));
  }

  removePromptFromOption(option) {
    if (option.startsWith(this.question)) {
      option = option.substring(this.question.length, option.length - 1);
    }
    return option;
  }

  pageOptionDisabled(option: string): boolean {
    if(this.filteredPages && this.filteredPages.length > 0){
      return this.filteredPages.includes(option);
    }else{
      return false
    }
  }

  moduleOptionDisabled(option: string): boolean {
    if(this.filteredModule && this.filteredModule.length > 0){
      return this.filteredModule.includes(option);
    }else{
      return false
    }
  }
  
  checkRole(filterValue){
    const isItemAvailable = this.baseData['Roles'].some(item =>
      item.role.toLowerCase() === this.roleForm.value.role.toLowerCase() && item.role.toLowerCase() === filterValue.target.value.toLowerCase()
    );
    if (isItemAvailable) {
      this.roleForm.get('role').setErrors({ 'roleExists': true });
    }else {
      this.roleForm.get('role').setErrors(null);
    }
  }

}

