import { ChangeDetectionStrategy, ChangeDetectorRef, Component, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule } from '@angular/forms';
import { GradientCardComponent } from 'src/app/components/shared/gradient-card/gradient-card.component';
import { MatTabsModule } from '@angular/material/tabs';
import { BackgroundImageCardComponent } from 'src/app/components/background-image-card/background-image-card.component';
import { BackgroundImageCardHeaderComponent } from 'src/app/components/background-image-card-header/background-image-card-header.component';
import { HttpTableComponent } from 'src/app/components/http-table/http-table.component';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { MatCardModule } from '@angular/material/card';
import { MatBottomSheet, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { InventoryService } from 'src/app/services/inventory.service';
import { ShareDataService } from 'src/app/services/share-data.service';
import { AuthService } from 'src/app/services/auth.service';
import { MasterDataService } from 'src/app/services/master-data.service';
import { BottomSheetComponent } from '../../bottom-sheet/bottom-sheet.component';
import { first } from 'rxjs';
import { NotificationService } from 'src/app/services/notification.service';
import { Router } from '@angular/router';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-parent',
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    GradientCardComponent,
    MatTabsModule,
    BackgroundImageCardComponent,
    BackgroundImageCardHeaderComponent,
    HttpTableComponent,
    MatButtonModule,
    MatIconModule,
    NgxSkeletonLoaderModule,
    MatIconModule,
    MatCardModule,
    MatBottomSheetModule
  ],
  templateUrl: './parent.component.html',
  styleUrls: ['./parent.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ParentComponent {
  public selectedTabIndex: number = -1;
  public selectedTabPage: string = '';
  public baseData: any;
  public tabs: { label: string; page: string; index: number; icon: string }[] = [
    { label: 'User', page: 'users', index: 0, icon: "inventory" },
    { label: 'Role', page: 'Roles', index: 1, icon: "store" },
    { label: 'Branch', page: 'branches', index: 2, icon: "store" },
  ];
  user: any;
  isDataReady: boolean = false;
  entireData: any;
  @ViewChild('openResetDialog') openResetDialog: TemplateRef<any>;
  dialogRef: MatDialogRef<any>;
  constructor(
    private api: InventoryService,
    private sharedData: ShareDataService,
    private cd: ChangeDetectorRef,
    private _bottomSheet: MatBottomSheet,
    private auth: AuthService,
    private masterDataService: MasterDataService,
    private notify: NotificationService,
    private router: Router,
    public dialog: MatDialog,
  ) {
    this.user = this.auth.getCurrentUser();
  }

  ngOnInit(): void {
    this.getBaseData();

  }

  getBaseData() {
    this.baseData = this.sharedData.getBaseData().value;
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['userEmail'] = this.user.email
    obj['type'] = 'user'
    this.masterDataService.route$.pipe(first()).subscribe(tab => {
      if (tab && tab === 'user') {
        if ('user' in this.sharedData.getBaseData().value) {
          obj['specific'] = 'user'
        }
        this.selectedTabIndex = 0;
      } else if (tab && tab === 'Roles') {
        if ('Roles' in this.sharedData.getBaseData().value) {
          obj['specific'] = 'Roles'
        }
        this.selectedTabIndex = 1;
      } else if (tab && tab === 'branches') {
        if ('branches' in this.sharedData.getBaseData().value) {
          obj['specific'] = 'branches'
        }
        this.selectedTabIndex = 2;
      } else {
        this.selectedTabIndex = 0;
      }
      this.api.getPresentData(obj).pipe(first()).subscribe({
        next: (res) => {
          if (res['success'] && (res['data'].length > 0 || Object.keys(res['data']).length > 0)) {
            this.entireData = res
            if (obj['specific'] == 'user') {
              let previousBaseData = this.sharedData.getBaseData().value['user'];
              let currentBaseData = res['data'][0] ?? res['data']['user'];

              currentBaseData.forEach(item => {
                const exist = previousBaseData.findIndex(el => el.email == item['email']);
                if (exist !== -1) {
                  previousBaseData[exist] = item;
                } else {
                  previousBaseData.push(item);
                }
              });
              this.baseData['user'] = previousBaseData;
            } else if (obj['specific'] == 'Roles') {
              this.baseData['Roles'] = res['data'][0] ?? res['data']['Roles'];

            } else if (obj['specific'] == 'branches') {
              this.baseData['branches'] = res['data'][0] ?? res['data']['branches'];

            } else {
              this.baseData = res['data'][0] ?? res['data'];
            }
            this.cd.detectChanges();
            this.sharedData.setUserData(res['data']);
            const uniqueRoles = [...new Set(this.baseData.Roles.map(item => item.role))];
            this.sharedData.setRoles(uniqueRoles);
            this.sharedData.setItemNames(obj, this.baseData)
            this.isDataReady = true;
            this.cd.detectChanges();

          }
        },
        error: (err) => { console.log(err) }
      })
    });
  }


  tabClick(tab: any) {
    this.selectedTabIndex = tab.index;
    this.selectedTabPage = this.tabs[tab.index].page;

    // Force change detection to ensure tabs render properly
    setTimeout(() => {
      this.cd.detectChanges();
    }, 0);
  }

  openBottomSheet(): void {
    this._bottomSheet.open(BottomSheetComponent);
  }

  resetData(){
    this.dialogRef = this.dialog.open(this.openResetDialog, {
      width: '500px',
    });

    this.dialogRef.afterClosed().subscribe(result => {
    });
  }

  resetUI(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['type'] = 'user'
    obj['sessionId'] = this.entireData.sessionId
    this.api.resetSession(obj).pipe(first()).subscribe({
      next: (res) => {
        if (res['success']) {
          this.notify.snackBarShowSuccess('The session was successfully reset.');
          this.closeResetDialog();
          this.masterDataService.setNavigation('');
          this.router.navigate(['/dashboard/home']);
          setTimeout(() => {
            this.router.navigate(['/dashboard/user']);
          }, 1000);
        }
      },
      error: (err) => { console.log(err) }
    })
  }

  closeResetDialog(){
    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

}
