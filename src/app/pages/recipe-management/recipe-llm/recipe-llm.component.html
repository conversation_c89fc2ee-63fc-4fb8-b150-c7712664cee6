<div class="recipe-insights-container" *ngIf="isLoading">
    <div class="loading-container">
        <div class="loading-content">
            <svg class="cooking-pot" viewBox="0 0 100 100" width="100" height="100">
                <path d="M20,50 Q50,20 80,50 L80,80 Q50,100 20,80 Z" fill="#d4d4d4" />
                <path d="M35,40 L65,40 L65,50 Q50,60 35,50 Z" fill="#ffffff" />
                <circle cx="50" cy="60" r="5" fill="#ff6b6b">
                    <animate attributeName="cy" values="60;55;60" dur="1s" repeatCount="indefinite" />
                </circle>
            </svg>
            <p class="mt-2">Loading insights...</p>
        </div>
    </div>
</div>


<div *ngIf="!isLoading" [innerHTML]="renderedMDHtml" class="markdown-content"></div>