.markdown-content {
  font-size: 1rem;
  line-height: 1.2;
}

.recipe-insights-container {
  position: relative;
  width: 100%;
  height: 40vh;
}
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
}
.loading-content {
  text-align: center;
}
.cooking-pot {
  width: 100px;
  height: 100px;
  animation: steam 2s ease-out infinite;
}
@keyframes steam {
  0% { transform: translateY(0) scale(1); opacity: 0.5; }
  50% { transform: translateY(-10px) scale(1.2); opacity: 0.7; }
  100% { transform: translateY(0) scale(1); opacity: 0.5; }
}
.content-container {
  margin: 0 auto;
  padding: 20px;
}
.list-group-item {
  transition: background-color 0.3s;
}
.list-group-item:hover {
  background-color: #f8f9fa;
}