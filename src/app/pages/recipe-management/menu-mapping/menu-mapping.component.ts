import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Inject,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { InventoryService } from 'src/app/services/inventory.service';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatNativeDateModule, MatOption } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSliderModule } from '@angular/material/slider';
import { MatSelectModule } from '@angular/material/select';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';

import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
} from '@angular/material/dialog';
import { AuthService } from 'src/app/services/auth.service';
import { MasterDataService } from 'src/app/services/master-data.service';
import { MatDialogRef } from '@angular/material/dialog';
import { NotificationService } from 'src/app/services/notification.service';
import { Observable, ReplaySubject, Subject, first, takeUntil } from 'rxjs';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ShareDataService } from 'src/app/services/share-data.service';
import { MatChipsModule } from '@angular/material/chips';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import * as XLSX from 'xlsx';
import { MatExpansionModule } from '@angular/material/expansion';

@Component({
  selector: 'app-menu-mapping',
  standalone: true,
  imports: [
    FormsModule,
    MatDialogModule,
    MatProgressSpinnerModule,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatExpansionModule,
    MatInputModule,
    NgxSkeletonLoaderModule,
    MatSliderModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSelectModule,
    MatAutocompleteModule,
    MatTableModule,
    MatChipsModule,
    MatTooltipModule,
    NgxMatSelectSearchModule,
    MatPaginatorModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './menu-mapping.component.html',
  styleUrls: ['./menu-mapping.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MenuMappingComponent implements OnInit{
  isChecked: boolean = false;
  menuMappingForm!: FormGroup;
  baseData: any;
  action: string ;
  dataSource = new MatTableDataSource<any>([]);
  user: any;
  displayedColumns: string[];
  workAreas: any[] = [];
  filteredData: any;
  public workAreaBank: any[] = [];
  public workAreaFilterCtrl: FormControl = new FormControl();
  public workAreaNames: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public locationFilterCtrl: FormControl = new FormControl();
  public locBank: any[] = [];
  public locationData: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  searchControl = new FormControl();
  filteredFloors: Observable<any[]>;
  isReadOnly: boolean = true;
  BranchData: any[] = [];
  floorList: any[] = [];
  currentRowUUID: any;
  loadMapBtn : boolean = true;
  isLoading : boolean = false;
  mappingDialog : any
  count: number = 0;
  currentPage: number = 0;
  excelData: XLSX.WorkBook;
  isDone: boolean = false ;
  isImportDone: boolean = false;
  isCreateButtonDisabled = false;
  isUpdateButtonDisabled = false;
  filteredFloorList = [];
  floorForm: FormGroup;
  initialData: {};
  mappingData: any[] = [];
  spinner: boolean;
  @ViewChild('openActionDialog') openActionDialog: TemplateRef<any>;
  floorValue: any;
  skip: boolean = false;
  dialogInfoRef: MatDialogRef<any>;
  formattedDate: any;
  formattedTime: any;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private api: InventoryService,
    private cd: ChangeDetectorRef,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog,
    private auth: AuthService,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private masterDataService: MasterDataService,
    public dialogRef: MatDialogRef<MenuMappingComponent>,
    private notify: NotificationService,
    private sharedData: ShareDataService
  ) {
    this.BranchData = this.sharedData.getLocation().value;

    this.locBank = this.BranchData;
    this.locationData.next(this.locBank.slice());
    this.locationFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.FilterLocation(
          this.locBank,
          this.locationFilterCtrl,
          this.locationData
        );
      });

    this.user = this.auth.getCurrentUser();
    this.menuMappingForm = this.fb.group({
      menuItemCode: ['', Validators.required],
      menuItemName: ['', Validators.required],
      floorNo: ['', Validators.required],
      restaurantId: ['', Validators.required],
      section: ['', Validators.required],
      storeId: ['', Validators.required],
      workArea: ['', Validators.required],
      row_uuid: [''],
      modified: [''],
      Changed: [''],
      _id: [''],
    });
  }

  protected FilterLocation(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(bank.filter((data) => data['branchName'].toLowerCase().indexOf(search) > -1));
  }

  ngOnInit(): void {
    this.displayedColumns = [
      'action',
      'restaurantId',
      'storeId',
      'menuItemCode',
      'menuItemName',
      'floorNo',
      'section',
      'workArea',
    ];
    this.baseData = this.sharedData.getBaseData().value;
    this.menuMappingForm.get('menuItemName').setValue(this.data.parentData['recipeName']);
    this.menuMappingForm.get('menuItemCode').setValue(this.data.parentData['recipeCode']);
    const currentDate = new Date();
    const options = { timeZone: 'Asia/Kolkata' };
    this.formattedDate = currentDate.toLocaleDateString('en-US', options);
    this.formattedTime = currentDate.toLocaleTimeString('en-US', { ...options, hour: '2-digit', minute: '2-digit', hour12: true });
  }

  applyFilter1(value) {
    if (!value.target.value) {
      this.filteredFloorList = this.floorList;
    } else {
      this.filteredFloorList = this.floorList.filter(floor =>
        floor.section.toLowerCase().includes(value.target.value.trim().toLowerCase()));
    }
  }

  close() {
    // this.skip = false;
    this.dialogRef.close();
  }

  selectRestaurant(event) {
    this.isDone = true ;
    this.getMappingData(1,5)
    let requiredBranch = this.BranchData.find(
      (el) => el.restaurantIdOld == event
    );
    this.workAreas = requiredBranch ? requiredBranch['workAreas'] : [];
    requiredBranch
      ? this.menuMappingForm.get('storeId').setValue(requiredBranch['storeId'])
      : undefined;
    this.workAreaBank = this.workAreas;
    this.workAreaNames.next(this.workAreaBank.slice());
    this.workAreaFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.Filter(
          this.workAreaBank,
          this.workAreaFilterCtrl,
          this.workAreaNames
        );
      });
  }

  getSections() {
    this.spinner = true ;
    let obj = {};
    obj['tenantId'] = this.user.tenantId;
    obj['storeId'] = this.menuMappingForm.get('storeId').value;
    this.api.getSections(obj).subscribe({
      next: (res) => {
        this.floorList = res.map((item) => {
          return {
            floorNo: item.id,
            section: item.name,
            workArea: this.workAreas,
          };
        });
        const rId = this.menuMappingForm.value['restaurantId'];
        const matchId = this.user.restaurantAccess.find(
          (r) => r.restaurantIdOld === rId
        );
        const floorNo = matchId?.defaultFloorNo || '';        
        if (floorNo){
          this.floorList.push({
          floorNo: floorNo,
          section: 'default',
          workArea: this.workAreas,
        }); 
        }
        this.filteredFloorList = this.floorList;
        this.spinner = false ;
        const controls = this.floorList.reduce((acc, floor) => {
          let requiredWA = this.mappingData.find(el => el['section'] === floor['section'])
          let currentWa = requiredWA ? requiredWA['workArea'] : undefined
          // Create a FormControl for each floor section
          acc[floor.section] = new FormControl(currentWa);
          return acc;
        }, {});
        this.floorForm = new FormGroup(controls);
        this.cd.detectChanges();
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  protected Filter(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(bank.filter((data) => data.toLowerCase().indexOf(search) > -1));
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  applyFilter(filterValue: any) {
    filterValue = filterValue.target.value;
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  create() {
    let menu = this.menuMappingForm.value ;
    let sessionMapping = this.floorForm.value ;
    let data = [] ;
    this.floorList.forEach((el) => {
      let obj = {
          "itemCode": menu['menuItemCode'],
          "itemName": menu['menuItemName'],
          "restaurantId": menu['restaurantId'],
          "tenantId": this.user.tenantId,
          "storeId": menu['storeId'],
      }                                                  
      obj['floorNo'] = el['floorNo'].toString()
      obj['section'] = el['section']
      obj['workArea'] = sessionMapping[el['section']]
      obj['workArea'] ? data.push(obj) : undefined 
  })
  if (data.length > 0) {
    let mapping = {
      "restaurantId" : this.menuMappingForm.value['restaurantId'],
      "tenantId" : this.user.tenantId,
      "itemCode" : menu['menuItemCode'],
      "mappings" : data,
    }
    this.api.createMenuMapping(mapping).pipe(first()).subscribe({
      next: (res) => {
        if (res['status']) {
          this.notify.snackBarShowSuccess('Menu-Mapping Added Successfully') ;
          this.isDone = true ;
        } else {
          this.notify.snackBarShowError('Something Went Wrong!');
        }
        this.cd.detectChanges();
      },
      error: (err) => { console.log(err); }
    });
  } else {
    this.notify.snackBarShowError('Please add required mappings');
  }
}

  getMappingData(pageIndex: number, pageSize: number) {
    this.isLoading = true ;
    let obj = {
      "tenantId" : this.user.tenantId,
      "itemCode" : this.data.parentData.recipeCode,
      "restaurantId" : this.menuMappingForm.value['restaurantId'],
      "export" : false,
    }
    this.api.getMenuMappingList(obj).pipe(first()).subscribe({
      next: (res) => {
        if (res['status']) {
          this.mappingData = res['data']
          this.getSections();
        } else {
          this.notify.snackBarShowError('Something Went Wrong!');
        }
        this.isLoading = false ;
        this.cd.detectChanges();
      },
      error: (err) => { console.log(err); }
    });
  }

  selectedFloor(value , floor){
    // if(this.skip == false){
      this.floorValue = value
      this.dialogInfoRef = this.dialog.open(this.openActionDialog, {
        maxHeight: '90vh',
        panelClass : 'smallCustomDialog'
      });
    // }
    // this.openActionDialog
  }

  proceed(){
      Object.keys(this.floorForm.controls).forEach(key => {
        this.floorForm.get(key)?.setValue(this.floorValue);
      });
    this.closeInfoDialog()
  }

  skipProcess(){
    // this.skip = true;
    this.closeInfoDialog()
  }

  closeInfoDialog() {
    if (this.dialogInfoRef) {
      this.dialogInfoRef.close();
    }
  }

}
