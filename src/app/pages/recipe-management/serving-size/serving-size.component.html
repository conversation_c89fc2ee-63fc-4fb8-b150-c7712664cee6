<div class="closeBtn" *ngIf="isDuplicate == true">
  <mat-icon (click)="close()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
</div>

<div class="py-2 px-3">
  <div *ngIf="isDuplicate == true" class="mt-3 smallDialog">
    <div class="bottomTitles text-center p-3 mb-2">
      <!-- d-flex flex-wrap align-items-center justify-content-center -->
      <!-- <span *ngIf="action == 'create' || action == 'update'">Serving Form</span> *ngIf="action == 'view'" -->
      <span>Serving Size</span>
    </div>
    <div class="col-md-12">
      <mat-form-field appearance="outline">
        <mat-label>Search Serving Size ..</mat-label>
        <input matInput placeholder="inventory master Name" aria-label="inventory master" [matAutocomplete]="auto1"
          [formControl]="itemNameControl" (keyup.enter)="addOption('inventory master')" (keyup)="checkItem($event)"
          oninput="this.value = this.value">
        <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelected('inventory master', $event.option)">
          <ng-container *ngFor="let category of itemNameOptions | async">
            <ng-container *ngIf="category.items.length > 0">
              <mat-accordion [multi]="multi">
                <mat-expansion-panel>
                  <mat-expansion-panel-header>
                    {{ category.category  }}
                  </mat-expansion-panel-header>
                  <mat-option *ngFor="let item of category.items" [value]="item">
                    <span>{{ item }}</span>
                  </mat-option>
                </mat-expansion-panel>
              </mat-accordion>
            </ng-container>
            <mat-option *ngIf="category.items.length === 0" [value]="category.category">
              <span>{{ category.category}}</span>
            </mat-option>
          </ng-container>
        </mat-autocomplete>
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>

      <div class="text-end">
        <button (click)="addOption('servingsize conversion')" mat-raised-button color="accent"
          [disabled]="!itemNameControl.value">
          <mat-icon *ngIf="!updateBtnActive">library_add</mat-icon>
          <mat-icon *ngIf="updateBtnActive">update</mat-icon>
          <span *ngIf="updateBtnActive">Update</span>
          <span *ngIf="!updateBtnActive">Add</span>
        </button>
      </div>
    </div>
  </div>

  <div class="registration-form" *ngIf="isDuplicate == false">
    <div class="mb-2 topCreateAndUpdateBtn"  style="float: right; padding-top: 0.5rem;">
      <button *ngIf="!isUpdateActive" (click)="create()" mat-raised-button style="margin-right: 5px;"  color="accent"
        matTooltip="create" [disabled]="isCreateButtonDisabled">
        <mat-icon>add_circle</mat-icon>Create
      </button>
      <button *ngIf="isUpdateActive" (click)="update()" mat-raised-button style="margin-right: 5px;" color="accent"
        matTooltip="update" [disabled]="loadServingBtn || isUpdateButtonDisabled">
        Update
      </button>
      <button *ngIf="isDuplicate == false" mat-raised-button color="warn" style="margin-right: 5px;" (click)="close()" matTooltip="Close">
        <mat-icon>close</mat-icon>
        Close
      </button>
    </div>

    <div class="my-2 p-3 bottomTitles" *ngIf="!isDuplicate">
      Serving Size
    </div>

    <form [formGroup]="registrationForm" style="margin-top: 25px;">
      <div class="row">
        <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
          <mat-label>Serving Size</mat-label>
          <input formControlName="servingSize" matInput placeholder="Serving Size" [readonly]="isReadOnly" />
          <mat-icon matSuffix>room_service</mat-icon>
        </mat-form-field>
        <mat-form-field appearance="outline">
          <mat-label>Ratio</mat-label>
          <input formControlName="ratio" matInput placeholder="Ratio" />
        </mat-form-field>
        <mat-form-field appearance="outline">
          <mat-label>Quantity</mat-label>
          <input formControlName="quantity" matInput placeholder="Quantity" />
        </mat-form-field>
        <mat-form-field appearance="outline">
          <mat-label>Quantity Unit</mat-label>
          <mat-select formControlName="quantityUnit">
            <mat-option *ngFor="let unit of units" [value]="unit">
              {{ unit }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </form>
  </div>

</div>