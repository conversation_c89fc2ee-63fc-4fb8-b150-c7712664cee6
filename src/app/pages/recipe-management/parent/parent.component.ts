import {
  ChangeDetectionStrategy,
  Component,
  ViewChild,
  ElementRef,
  ChangeDetectorRef,
  OnInit,
  AfterViewInit,
  TemplateRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { BackgroundImageCardComponent } from 'src/app/components/background-image-card/background-image-card.component';
import { ShareDataService } from 'src/app/services/share-data.service';
import { Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatGridListModule } from '@angular/material/grid-list';
import { InventoryService } from 'src/app/services/inventory.service';
import { AuthService } from 'src/app/services/auth.service';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { SyncDataComponent } from '../sync-data/sync-data.component';
import {
  MatBottomSheet,
  MatBottomSheetModule,
  MatBottomSheetRef,
} from '@angular/material/bottom-sheet';
import { BottomSheetComponent } from '../../bottom-sheet/bottom-sheet.component';
import { BackgroundImageCardHeaderComponent } from 'src/app/components/background-image-card-header/background-image-card-header.component';
import { MatTabsModule } from '@angular/material/tabs';
import { HttpTableComponent } from 'src/app/components/http-table/http-table.component';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ValidationErrors,
  Validators,
  ValidatorFn,
  AbstractControl,
  FormArray,
} from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { NotificationService } from 'src/app/services/notification.service';
import { MasterDataService } from 'src/app/services/master-data.service';
import {
  Observable,
  ReplaySubject,
  Subject,
  first,
  map,
  of,
  startWith,
  takeUntil,
} from 'rxjs';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-index',
  standalone: true,
  imports: [
    MatGridListModule,
    CommonModule,
    MatCardModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatFormFieldModule,
    MatInputModule,
    BackgroundImageCardComponent,
    MatBottomSheetModule,
    BackgroundImageCardHeaderComponent,
    MatTabsModule,
    HttpTableComponent,
    MatAutocompleteModule,
    MatTooltipModule,
    NgxSkeletonLoaderModule,
    ReactiveFormsModule,
  ],
  templateUrl: './parent.component.html',
  styleUrls: ['./parent.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ParentComponent {
  public selectedTabIndex: number = 0;
  public selectedTabPage: string = '';
  public httpTableBaseData: any;
  public tabs: { label: string; page: string; index: number; icon: string }[] =
    [
      { label: 'Recipe', page: 'menu master', index: 0, icon: 'menu_book' },
      {
        label: 'Subrecipe',
        page: 'Subrecipe Master',
        index: 1,
        icon: 'archive',
      },
      {
        label: 'Serving Size',
        page: 'servingsize conversion',
        index: 2,
        icon: 'room_service',
      },
    ];
  [x: string]: any;
  @ViewChild('nameButton') nameButton: ElementRef;
  user: any;
  baseData: any;
  isDataReady = false;
  showMessage: any;
  priceList: any;
  locationData: any[];
  entireData: any;
  @ViewChild('openResetDialog') openResetDialog: TemplateRef<any>;
  dialogRef: MatDialogRef<any>;

  constructor(
    private sharedData: ShareDataService,
    public dialog: MatDialog,
    private api: InventoryService,
    private auth: AuthService,
    private cd: ChangeDetectorRef,
    private _bottomSheet: MatBottomSheet,
    private masterDataService: MasterDataService,
    private notify: NotificationService,
    private router: Router
  ) {
    this.user = this.auth.getCurrentUser();
    this.getBaseData();
    // this.getConfigData();
    this.getModifiers();
    this.getPartyNames();
  }

  getBaseData() {
    this.baseData = this.sharedData.getBaseData().value;
    let obj = {};
    obj['tenantId'] = this.user.tenantId;
    obj['userEmail'] = this.user.email;
    obj['type'] = 'recipe';
    this.masterDataService.route$.pipe(first()).subscribe((tab) => {
      if (tab && tab === 'menu master') {
        if ('menu master' in this.sharedData.getBaseData().value) {
          obj['specific'] = 'menu master';
        }
        this.selectedTabIndex = 0;
      } else if (tab && tab === 'Subrecipe Master') {
        if ('Subrecipe Master' in this.sharedData.getBaseData().value) {
          obj['specific'] = 'Subrecipe Master';
        }
        this.selectedTabIndex = 1;
      } else if (tab && tab === 'servingsize conversion') {
        if ('servingsize conversion' in this.sharedData.getBaseData().value) {
          obj['specific'] = 'servingsize conversion';
        }
        this.selectedTabIndex = 2;
      } else {
        this.selectedTabIndex = 0;
      }
      this.api
        .getPresentData(obj)
        .pipe(first())
        .subscribe({
          next: (res) => {
            if (
              res['success'] &&
              (res['data'].length > 0 || Object.keys(res['data']).length > 0)
            ) {
              this.entireData = res;
              if (obj['specific'] == 'menu master') {
                let previousBaseData =
                  this.sharedData.getBaseData().value['menu master'];
                let currentBaseData =
                  res['data'][0] ?? res['data']['menu master'];
                currentBaseData.forEach((item) => {
                  const exist = previousBaseData.findIndex(
                    (el) => el.menuItemCode == item['menuItemCode']
                  );
                  if (exist !== -1) {
                    previousBaseData[exist] = item;
                  } else {
                    previousBaseData.push(item);
                  }
                });
                this.baseData['menu master'] = previousBaseData;
              } else if (obj['specific'] == 'Subrecipe Master') {
                this.baseData['Subrecipe Master'] =
                  res['data'][0] ?? res['data']['Subrecipe Master'];
              } else if (obj['specific'] == 'servingsize conversion') {
                this.baseData['servingsize conversion'] =
                  res['data'][0] ?? res['data']['servingsize conversion'];
              } else {
                this.baseData = res['data'][0] ?? res['data'];
              }
              let servingNames = this.baseData['servingsize conversion'].map(
                (item) => item['Serving Size']
              );
              let servingSizes =
                res['data'][0]?.['servingsize conversion'] ??
                res['data']['servingsize conversion'] ??
                [];
              this.sharedData.setServingSizes(servingSizes, this.baseData);
              this.sharedData.setServingSizeNames(servingNames, this.baseData);
              this.getLocationCall();
              this.getItemList();
              this.getCategories();
              // this.isDataReady = true;
              this.cd.detectChanges();
            } else {
              this.baseData = [];
              this.isDataReady = true;
              this.cd.detectChanges();
            }
          },
          error: (err) => {
            console.log(err);
          },
        });
    });
  }

  getItemList() {
    if (this.sharedData.getItemType() === true) {
    if (this.sharedData.getPOSItems().value.length === 0 ) {
      this.api.getPOS_MenuItems(this.user.tenantId).subscribe({
        next: (res) => {
          let items = [];
          this.sharedData.setCurrentPosList(res);
          res.forEach((pos) => {
            let obj = {};
            let requiredData = this.baseData['menu master'].find(
              (el) => el.menuItemCode === pos.pluCode
            );
            obj['category'] = requiredData ? 'BOTH' : 'POS ONLY';
            obj['itemName'] = requiredData
              ? requiredData['menuItemName']
              : pos['name'];
            obj['itemCode'] = requiredData
              ? requiredData['menuItemCode']
              : pos['pluCode'];
            obj['menuId'] = requiredData ? undefined : pos['id'];
            items.push(obj);
          });
          this.baseData['menu master'].forEach((element) => {
            let requiredData = items.find(
              (el) => element.menuItemCode === el.itemCode
            );
            if (!requiredData) {
              let obj = {};
              obj['category'] = 'INVENTORY ONLY';
              obj['itemName'] = element['menuItemName'];
              obj['itemCode'] = element['menuItemCode'];
              items.push(obj);
            }
          });
          let obj = {
            res: res,
            Items: items,
            invCount: this.baseData['menu master'].length,
          };
          this.sharedData.setPOSItems(obj);
          this.sharedData.setRecipeNames(items, this.baseData);
          this.isDataReady = true;
          this.cd.detectChanges();
        },
        error: (err) => {
          console.log(err);
        },
      });
    } else {
      let items = [];
      const currentPosItems = this.sharedData.getCurrentPosList().value
      currentPosItems.forEach((pos) => {
        let obj = {};
        let requiredData = this.baseData['menu master'].find(
          (el) => el.menuItemCode === pos.pluCode
        );
        obj['category'] = requiredData ? 'BOTH' : 'POS ONLY';
        obj['itemName'] = requiredData
          ? requiredData['menuItemName']
          : pos['name'];
        obj['itemCode'] = requiredData
          ? requiredData['menuItemCode']
          : pos['pluCode'];
        obj['menuId'] = requiredData ? undefined : pos['id'];
        items.push(obj);
      });
      this.baseData['menu master'].forEach((element) => {
        let requiredData = items.find(
          (el) => element.menuItemCode === el.itemCode
        );
        if (!requiredData) {
          let obj = {};
          obj['category'] = 'INVENTORY ONLY';
          obj['itemName'] = element['menuItemName'];
          obj['itemCode'] = element['menuItemCode'];
          items.push(obj);
        }
      });
      let obj = {
        res: currentPosItems,
        Items: items,
        invCount: this.baseData['menu master'].length,
      };
      this.sharedData.setPOSItems(obj);
      this.sharedData.setRecipeNames(items, this.baseData);
      this.isDataReady = true;
    }
    } else {
      this.isDataReady = true;
    }
  }

  getLocationCall() {
    if (this.sharedData.getLocation().value.length === 0) {
      this.api.getLocations(this.user.tenantId).subscribe({
        next: (res) => {
          if (res['result'] == 'success') {
            this.locationData = res['branches'][0];
            this.sharedData.setLocations(res['branches']);
            this.filterDatas();
          } else {
            res = [];
          }
        },
        error: (err) => {
          console.log(err);
        },
      });
    }
  }

  getCategories() {
    if (this.sharedData.getCategories().value.length === 0) {
      this.api
        .getCategories({ tenantId: this.user.tenantId, type: 'menu' })
        .pipe(first())
        .subscribe({
          next: (res) => {
            if (res['success']) {
              this.sharedData.setCategories(res['categories']);
            }
          },
        });
    }
  }

  tabClick(tab: any) {
    this.selectedTabIndex = tab.index;
    this.selectedTabPage = this.tabs[tab.index].page;

    // Force change detection to ensure tabs render properly
    setTimeout(() => {
      this.cd.detectChanges();
    }, 0);
  }

  openBottomSheet(): void {
    const bottomSheetRef = this._bottomSheet.open(BottomSheetComponent);
  }

  filterDatas() {
    if (this.baseData) {
      const menuItemName = Array.from(
        new Set(
          this.baseData['Subrecipe Master'].map((item) => item.menuItemName)
        )
      );
      const menuMasterItemName = Array.from(
        new Set(this.baseData['menu master'].map((item) => item.menuItemName))
      );
      const locations = this.locationData['abbreviatedRestaurantId'];
      const workAreas = this.locationData['workAreas'];
      let obj = {
        menuItemName: menuItemName,
        menuMasterItemName: menuMasterItemName,
        locations: [locations],
        workAreas: workAreas,
      };
      this.sharedData.setItemNames(obj, this.baseData);
    }
  }

  syncData() {
    this.dialog.open(SyncDataComponent, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '95vh',
      data: this.baseData,
    });
  }

  // this code for feature :: getting color from the image  DON'T REMOVE-------------------------------

  checkDuplicate(event) {
    // const found = this.recipes.some(item => item.menuItemName === event.target.value);
    // if (found) {
    //   this.showMessage =  "Recipe already available"
    // }else{
    //   this.showMessage = ''
    // }
  }

  loadAndApplyColors() {
    // for (const card of this.recipes) {
    //   if (card['image']) {
    //     this.sampleImageColors(card['image']).then((backgroundColor) => {
    //       card['backgroundColor'] = backgroundColor;
    //     });
    //   }
    // }
  }

  async sampleImageColors(
    imageUrl: string,
    brightnessThreshold: number = 130
  ): Promise<string> {
    return new Promise<string>((resolve) => {
      const image = new Image();
      image.crossOrigin = 'Anonymous';
      image.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = image.width;
        canvas.height = image.height;
        ctx.drawImage(image, 0, 0, image.width, image.height);

        const imageData = ctx.getImageData(
          0,
          0,
          image.width,
          image.height
        ).data;

        // Initialize variables for sum of R, G, and B values
        let totalR = 0;
        let totalG = 0;
        let totalB = 0;
        let pixelCount = 0;

        for (let i = 0; i < imageData.length; i += 4) {
          const r = imageData[i];
          const g = imageData[i + 1];
          const b = imageData[i + 2];

          // Calculate brightness as an average of R, G, and B values
          const brightness = (r + g + b) / 3;

          if (brightness >= brightnessThreshold) {
            // Include this pixel in the calculation
            totalR += r;
            totalG += g;
            totalB += b;
            pixelCount++;
          }
        }

        // Calculate the average color of pixels above the brightness threshold
        const avgR = totalR / pixelCount;
        const avgG = totalG / pixelCount;
        const avgB = totalB / pixelCount;

        const backgroundColor = `rgb(${avgR}, ${avgG}, ${avgB})`;
        resolve(backgroundColor);
      };

      image.src = imageUrl;
    });
  }

  // getConfigData() {
  //   if (this.sharedData.getDefaultPriceTier() === 0) {
  //     this.api.readIPConfig(this.user.tenantId).subscribe({
  //       next: (res) => {
  //         if (res['success']) {
  //           if (res['data'].hasOwnProperty('defaultPriceTier')) {
  //             this.getDetailedPriceList(res['data']['defaultPriceTier']);
  //             this.sharedData.setDefaultPriceTier(
  //               res['data']['defaultPriceTier']
  //             );
  //           }
  //         }
  //       },
  //       error: (err) => {
  //         console.log(err);
  //       },
  //     });
  //   }
  // }

  // getDetailedPriceList(val) {
  //   let obj = {
  //     tenantId: this.user.tenantId,
  //     priceId: val,
  //   };
  //   this.api.getDetailedPriceList(obj).subscribe({
  //     next: (res) => {
  //       let priceList = [];
  //       if (Array.isArray(res)) {
  //         priceList = res;
  //       }
  //       this.sharedData.setPriceList(priceList);
  //     },
  //     error: (err) => {
  //       console.log(err);
  //     },
  //   });
  // }

  getModifiers() {
    if (this.sharedData.getModifiers().value.length === 0){
      this.api.getModifiers(this.user.tenantId).subscribe({
        next: (res) => {
          let modifiers = [];
          if (Array.isArray(res)) {
            modifiers = res;
          }
          this.sharedData.setModifiers(modifiers);
        },
        error: (err) => {
          console.log(err);
        },
      });
    }
  }

  resetData() {
    this.dialogRef = this.dialog.open(this.openResetDialog, {
      width: '500px',
    });
    this.dialogRef.afterClosed().subscribe((result) => {});
  }

  resetUI() {
    let obj = {};
    obj['tenantId'] = this.user.tenantId;
    obj['type'] = 'recipe';
    obj['sessionId'] = this.entireData.sessionId;
    this.api
      .resetSession(obj)
      .pipe(first())
      .subscribe({
        next: (res) => {
          if (res['success']) {
            this.notify.snackBarShowSuccess(
              'The session was successfully reset.'
            );
            this.closeResetDialog();
            this.masterDataService.setNavigation('');
            this.router.navigate(['/dashboard/home']);
            setTimeout(() => {
              this.router.navigate(['/dashboard/recipe']);
            }, 1000);
          }
        },
        error: (err) => {
          console.log(err);
        },
      });
  }

  closeResetDialog() {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

  getPartyNames(){
    this.api.getPartyNames(this.user.tenantId).subscribe({
      next: (res) => {
        this.sharedData.setPartyNames(res['data'].map(name => name.eventName));
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

}
