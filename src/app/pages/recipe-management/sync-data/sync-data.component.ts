import { ChangeDetectionStrategy, Component, OnInit, Inject, ViewChild, HostListener, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { InventoryService } from 'src/app/services/inventory.service';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatNativeDateModule, MatOption } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSliderModule } from '@angular/material/slider';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDividerModule } from '@angular/material/divider';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { AutocompleteComponent } from '../../../components/autocomplete/autocomplete.component';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatExpansionModule } from '@angular/material/expansion';
import { AuthService } from 'src/app/services/auth.service';
import { NotificationService } from 'src/app/services/notification.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { Router } from '@angular/router';
import { Subscription, interval, timer } from 'rxjs';
import { ShareDataService } from 'src/app/services/share-data.service';
import { EmptyStateComponent } from 'src/app/components/empty-state/empty-state.component';
@Component({
  selector: 'app-sync-data',
  standalone: true,
  imports: [
    FormsModule,
    NgxSkeletonLoaderModule,
    MatDialogModule,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatInputModule,
    MatSliderModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSelectModule,
    MatRadioModule,
    MatAutocompleteModule,
    MatDividerModule,
    AutocompleteComponent,
    MatTableModule,
    NgxMatSelectSearchModule,
    MatCheckboxModule,
    MatExpansionModule,
    MatExpansionModule, MatTooltipModule, MatPaginatorModule, MatSortModule,
    EmptyStateComponent
  ],
  templateUrl: './sync-data.component.html',
  styleUrls: ['./sync-data.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SyncDataComponent implements OnInit {
  displayedColumns: string[];
  dataSource = new MatTableDataSource<any>([]);
  user: any;
  tenantDetails: any;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  stages = [
    { title: "Provision", status: true },
    { title: "Quality", status: false },
    { title: "Deployment", status: false },
  ];
  retrieveData: any;
  statusData: any;
  activeRowIndex: number;
  dataReady: boolean = false;
  isSmallScreen: boolean;
  syncType: string;
  syncTypeName: string;
  sessionType: string;
  navigateData: string;
  private timerSubscription: Subscription | undefined;
  private intervalMs = 5000;
  ids: any[];
  interval: Subscription;
  hasPendingOrCompleted: boolean = true;

  constructor(
    private api: InventoryService,
    private cd: ChangeDetectorRef,
    private router: Router,
    private auth: AuthService,
    public dialog: MatDialog,
    private notify: NotificationService,
    @Inject(MAT_DIALOG_DATA) public baseData: any,
    private sharedData: ShareDataService,
  ) {
    this.user = this.auth.getCurrentUser();
    this.getTenantData();
    this.isSmallScreen = window.innerWidth < 740;

    const currentUrl = window.location.href.split('/');
    const currentPage = currentUrl[currentUrl.length - 1];
    const pageType = currentPage.includes('?') ? currentPage.split('?')[0] : currentPage;
    switch (pageType) {
      case 'recipe':
      case 'Subrecipe Master':
        this.syncType = 'recipeManagement';
        this.syncTypeName = "Recipe"
        this.sessionType = "recipe"
        this.navigateData = 'menu master'
        break;
      case 'user':
        this.syncType = 'userManagement';
        this.syncTypeName = "User"
        this.sessionType = "user"
        this.navigateData = 'user'
        break;
      default:
        this.syncType = 'inventoryManagement';
        this.syncTypeName = "Inventory"
        this.sessionType = "inventory"
        this.navigateData = 'inventoryList'
        break;
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any): void {
    this.isSmallScreen = window.innerWidth < 768;
  }

  ngOnInit(): void {
    this.displayedColumns = [
      'id',
      'userName',
      'syncEmail',
      'createdDate',
      'status',
      'errorLog',
    ];
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  return() {
    this.dialog.closeAll();
    window.location.reload();
  }

  getTenantData() {
    this.api.getTenantConfigDetails(this.user.tenantId).subscribe({
      next: (res) => {
        if (res['success']) {
          this.tenantDetails = res['data'];
          this.getHistory(this.tenantDetails['full']);
        } else {
          this.notify.snackBarShowInfo(res['message']);
          this.tenantDetails = undefined;
        }
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  getHistory(client) {
    let obj = this.user;
    obj['client'] = client;
    obj['type'] = this.syncType;
    this.api.retrieveHistory(obj).subscribe({
      next: (res) => {
        if (res['success'] && res['data'].length > 0) {
          this.retrieveData = res['data'];
          this.statusData = res['data'][0].history;
          this.activeRowIndex = res['data'][0];
          this.dataSource.data = this.retrieveData
          this.dataReady = true;
          var counter = 0;
          this.interval= interval(3000).subscribe((_x =>{
            this.refreshApiCall(client);
            counter += 1;
            if (counter > 24 || !this.hasPendingOrCompleted) this.interval.unsubscribe();
          }));
          this.cd.detectChanges();
        } else {
          counter = 24
          this.retrieveData = [];
          this.dataSource.data = [];
          this.dataReady = true;
          this.cd.detectChanges();
        }
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  refreshApiCall(client){
    this.ids = this.dataSource.data.filter(item => item.status === 'pending').map(item => item._id['$oid']);
      let obj = this.user;
      obj['type'] = this.syncType;
      obj['client'] = client;
      obj['ids'] = this.ids[0];
      if(this.ids.length > 0){
        this.api.refreshApiCall(obj).subscribe({
          next: (res) => {
            if (res['success']) {
              this.hasPendingOrCompleted = res['data'].some(item => item.status === 'pending');
              this.retrieveData = res['data'];
              this.statusData = res['data'][0].history;
              this.activeRowIndex = res['data'][0];
              this.cd.detectChanges();
              if(!this.hasPendingOrCompleted){
                let obj = this.user;
                obj['client'] = client;
                obj['type'] = this.syncType;
                this.api.retrieveHistory(obj).subscribe({
                  next: (res) => {
                    if (res['success'] && res['data'].length > 0) {
                      this.retrieveData = res['data'];
                      this.statusData = res['data'][0].history;
                      this.activeRowIndex = res['data'][0];
                      this.dataSource.data = this.retrieveData
                      this.dataReady = true;
                      this.cd.detectChanges();
                    }
                  },
                  error: (err) => {
                    console.log(err);
                  },
                });
              }
            }else{
              this.hasPendingOrCompleted = false
            }
          },
          error: (err) => {
            console.log(err);
          },
        });
      }
  }

  ngOnDestroy() {
    if (this.interval) {
      this.interval.unsubscribe();
    }
  }

  getErrorlog(event) {
    let obj = {};
    obj['masterDataUpdateId'] = event.id;
    this.api.getErrorlog(obj).subscribe({
      next: (res) => {
        if (res['success']) {
          let encodedData = res.eFile;
          let textData = atob(encodedData);
          let rows = textData.split('\n');
          for (let i = 1; i < rows.length; i++) {
            let row = rows[i].split('|');
            if (row.length > 1) {
              let rowNumber = parseInt(row[1], 10);
              if (!isNaN(rowNumber)) {
                rowNumber++;
                row[1] = rowNumber.toString();
              }
              rows[i] = row.join('|');
            }
          }
          let modifiedTextData = rows.join('\n');
          const newWindow = window.open('', '_blank');
          if (newWindow) {
            newWindow.document.open();
            newWindow.document.write('<pre>' + modifiedTextData + '</pre>');
            newWindow.document.close();
          }
        } else {
          this.notify.snackBarShowInfo(res['message']);
        }
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  isLastStage(index: number): boolean {
    return index === this.stages.length - 1;
  }

  viewStatus(element) {
    this.retrieveData = [element];
    this.statusData = [element][0].history;
    this.activeRowIndex = element;
  }

}
