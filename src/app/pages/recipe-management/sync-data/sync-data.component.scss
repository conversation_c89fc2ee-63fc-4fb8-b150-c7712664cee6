.section {
  width: 100%;
  max-height: 435px;
  overflow: auto;
}

.check_circleIcon {
  color: #4caf50;
}
.error_circleIcon {
  color: red;
}

.evaluation-container {
  .candidate-evaluation {
    width: 100%;
    height: 5rem;

    span {
      font-size: 40px;
      color: green;
    }

    .progress_bar {
      font-family: Arial;
      margin-top: 50px;
      display: contents;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .stepper-item {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      margin: -2px;

      .step-name {
        font-size: 0.9rem;
      }
    }

    .stepper-item::before,
    .stepper-item::after {
      position: absolute;
      content: "";
      border-bottom: 2px solid #ccc !important;
      width: 100%;
      top: 20px;
      z-index: 2;
    }

    .stepper-item::before {
      left: -50%;
    }

    .stepper-item::after {
      left: 50%;
    }

    .stepper-item .step-counter {
      position: relative;
      z-index: 5;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 37px;
      height: 37px;
      border-radius: 50%;
      background: #ccc;
      margin-bottom: 6px;
    }

    .stepper-item.active {
      font-weight: bold;
    }

    .stepper-item.completed .step-counter {
      background-color: green;
      color: #ffffff;
    }

    .stepper-item.error .step-counter {
      background-color: red;
      color: #ffffff;
    }

    .stepper-item .final_step {
      outline: 2px solid green;
      border: 2px solid #fff;
    }

    .stepper-item.completed::after {
      position: absolute;
      content: "";
      border-bottom: 2px solid #ccc !important;
      width: 100%;
      top: 20px;
      left: 50%;
      z-index: 3;
    }

    .stepper-item:first-child::before {
      content: none;
    }

    .stepper-item:last-child::after {
      content: none;
    }

    .stepper-item .date_time {
      height: 0.75rem;
      font-size: 0.7rem;
      color: #767070;

      .time {
        font-size: 0.7rem;
        color: #767070;
      }
    }
  }
}

.timerIcon {
  color: #6a6a6a;
}

.viewBtn {
  background-color: #008bbd;
}

.editIconBtnSync {
  padding-right: 4px;
  padding-left: 3px;
}



.spinner-border1 {
  height: 2.5rem !important;
  width: 2.5rem !important;
}

.banner {
  background-color: #fafafa;
  padding-top: 9px;
  padding-bottom: 1px;
  font-size: large;
  text-align: center;
  color: crimson;
  font-weight: bold;
}

.parentCornerCancelBtn {
  position: relative;
  top: -18px;
  margin-right: 10px;
  text-align: end;
}

.loader{
  height: 300px;
  width: 650px;
  padding-left: 10px;
  padding-right: 10px;
}

.loader span {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 20px;
  font-size: 25px;
}
