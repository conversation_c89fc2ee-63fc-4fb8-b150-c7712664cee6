<div class="p-2">

  <!-- <div class="registration-form smallDialog" *ngIf="isDuplicate == true">
    <div class="col-md-12">
      <div class="closeBtn">
        <mat-icon (click)="close()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
      </div>
      <div class="text-center my-2 p-1 bottomTitles">
        <span>Party Name</span>
      </div>

      <mat-form-field appearance="outline" class="smallDialogInput">
        <input matInput placeholder="Party Name" aria-label="Party" [formControl]="partyNameControl"
          (keyup.enter)="addOption()" (keyup)="checkItem($event); validatePartyName($event)"
          oninput="this.value = this.value.toUpperCase()" maxlength="50">
      </mat-form-field>
      <mat-error class="formError" *ngIf="partyNameControl.hasError('invalidFormat')">
        Please enter at least 3 words without spaces!
      </mat-error>
      <mat-error class="formError" *ngIf="this.showDuplicate">
        This party name already exists!
      </mat-error>
      <mat-error class="formError" *ngIf="partyNameControl.hasError('emptySpaces')">
        Input cannot be empty or contain only spaces!
      </mat-error>

      <div class="text-end mt-2">
        <button (click)="addOption()" mat-raised-button color="accent"
          [disabled]="!partyNameControl.value || this.showDuplicate || partyNameControl.hasError('invalidFormat') || partyNameControl.hasError('emptySpaces')">
          <span>Add</span>
        </button>

        <button (click)="addOption()" mat-raised-button color="accent" *ngIf="checkCopiedParty()"
          style="margin-left: 10px !important;">
          <span>Create Clone Party</span>
        </button>
      </div>
    </div>
  </div> -->

  <!-- *ngIf="isDuplicate == false" -->
  <div class="sticky-header">
    <div class="header-container">
      <div class="topTitleName">
        <mat-icon class="restIcons "> celebration</mat-icon>
        <span class="topNameClass ml-2">{{ this.createPartyForm.value.partyName || 'Party Details'}}
          <span class="badge hi-badge ml-2" style="font-weight: bold; font-size: medium;">
            Rs {{ this.createPartyForm.value.price || '0'}}
          </span>
        </span>
        <span class="partyClosedText" *ngIf="this.checkPartyStatus"> Party Closed </span>
      </div>

      <div class="button-group">
        <!-- <button *ngIf="updateActive" matTooltip="Clone" mat-raised-button (click)="cloneParty()">
          <mat-icon>file_copy</mat-icon> Clone
        </button> -->

        <button *ngIf="updateActive" matTooltip="Print" mat-raised-button (click)="printOption()">
          <mat-icon>print</mat-icon> Print
        </button>

        <button *ngIf="updateActive && !checkPartyStatus" class="stepperBtns" (click)="submit()" mat-raised-button
          color="accent" matTooltip="Update" [disabled]="checkItemsGroup()">
          {{ addWastagePressed ? 'Close Party' : 'Update' }}
        </button>

        <button *ngIf="!updateActive" class="stepperBtns" (click)="submit()" mat-raised-button color="accent"
          matTooltip="create" [disabled]="this.checkItemsGroup()">
          <!-- [disabled]="this.createPartyForm.invalid || this.checkItemsGroup()" -->
          <mat-icon>add_circle</mat-icon>Create
        </button>

        <button mat-raised-button color="warn" matTooltip="Close" (click)="close()">
          <mat-icon>close</mat-icon>
          Close
        </button>
      </div>
    </div>
  </div>
  <!-- *ngIf="isDuplicate == false" -->
  <div class="registration-form">
    <div class="topContents">
      <form [formGroup]="createPartyForm" cdkTrapFocus>
        <!-- <div>
          <mat-accordion>
            <mat-expansion-panel hideToggle [expanded]="isPanelOpen" [expanded]="true">
              <mat-expansion-panel-header>
                <mat-panel-title><b>SUMMARY</b></mat-panel-title>
              </mat-expansion-panel-header>
              <div class="mt-3">
                <div class="extraSuppliesClass m-1">
                  <table class="table">
                    <thead>
                      <tr>
                        <th scope="col"><b></b></th>
                        <th scope="col"><b>Total Amount (WO/D)</b></th>
                        <th scope="col"><b>Discount</b></th>
                        <th scope="col"><b>Overall Discount</b>(include items and party)</th>
                        <th scope="col"><b>Total Amount</b></th>
                      </tr>
                    </thead>
                    <tbody>

                      <tr>
                        <td><b>SUPPLIES</b></td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>{{ this.getSuppliesTotal('totalPrice') }}</td>
                      </tr>

                      <tr>
                        <td><b>PARTY ITEMS</b></td>
                        <td>{{ this.getMenuItemsTotal('actualPrice') }}</td>
                        <td>{{ (this.getGroupDiscount() + this.getItemsDiscount() )}}</td>
                        <td>-</td>
                        <td>{{ this.getMenuItemsTotal('totalPrice') }}</td>
                      </tr>

                      <tr>
                        <td><b>PARTY</b></td>
                        <td>{{ this.createPartyForm.value.actualPrice }}</td>
                        <td>
                          <div class="col-md-3">
                            <div class="form-group customHeightfield" style="width: 200px !important; position: relative;">
                              <div class="mb-2">
                                <mat-slide-toggle [(ngModel)]="partyTaxDiscount" [ngModelOptions]="{standalone: true}"
                                  (change)="onToggleChange($event)">Adjust Percentage</mat-slide-toggle>
                              </div>
                              <input formControlName="partyDiscount" type="number" class="highlighted-input form-control"
                                placeholder="Party Discount" autocomplete="off" step="1"
                                (keyup)="calculatePartyDiscount($event)"
                                (keydown)="restrictSpecialCharacters($event); restrictToIntegers($event)"
                                (focus)="focusFunction('partyDiscount')" (focusout)="focusOutFunction('partyDiscount')">
                              <i *ngIf="partyTaxDiscount" class="input-icon fa fa-percent inputIcon"></i>
                              <span *ngIf="!partyTaxDiscount" class="input-icon inputIcon">₹</span>
                            </div>
                          </div>
                        </td>
                        <td *ngIf="partyTaxDiscount">{{this.getGroupDiscount() + this.getItemsDiscount() }}</td>
                        <td *ngIf="!partyTaxDiscount">{{ this.createPartyForm.value.partyDiscount +
                          (this.getGroupDiscount() + this.getItemsDiscount() ) }}</td>
                        <td>{{ this.createPartyForm.value.price }}</td>
                      </tr>

                      <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td><b>AMOUNT WITH RETURNS</b></td>
                        <td>{{ this.getTotalReturns() }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <mat-divider></mat-divider>
            </mat-expansion-panel>
          </mat-accordion>
        </div> -->

        <div *ngIf="this.createPartyForm.value.price > 0">
          <div class="m-1">
            <div class="my-2 partyTitles">
              SUMMARY
            </div>
            <mat-divider></mat-divider>
            <div class="mt-2">
              <table class="table table-bordered">
                <thead>
                  <tr>
                    <th scope="col"><b>Expense</b></th>
                    <th scope="col"><b>Actual Amount</b></th>
                    <th scope="col"><b>
                        <div class="d-flex gap-2">
                          <div>Item Discount </div>
                          <mat-icon style="font-size: 15px; margin-top: 2px;"
                            matTooltip="converted both % and ₹ into amount">info</mat-icon>
                        </div>
                      </b></th>
                    <!-- <th scope="col"><b>Percentage Discount</b></th> -->
                    <th scope="col"><b>
                        <div class="d-flex gap-2">
                          <div>Group Discount</div>
                          <mat-icon style="font-size: 15px; margin-top: 2px;"
                            matTooltip="converted both % and ₹ into amount">info</mat-icon>
                        </div>
                      </b>
                    </th>
                    <th scope="col"><b>Final Amount</b></th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td scope="row" class="subHeading"><b>Extras</b></td>
                    <td>{{ this.getSuppliesTotal('totalPrice') }}</td>
                    <td colspan="2" style="text-align: center; font-weight: bold;">There is no discount available for
                      the Extras</td>
                    <td>{{ this.getSuppliesTotal('totalPrice') }}</td>
                  </tr>
                  <tr>
                    <td scope="row" class="subHeading"><b>Party Items</b></td>
                    <td>{{ getSafeMenuItemsTotal('totalPriceWithReturns') }}</td>
                    <!-- <td>{{ getSafeValue(getGroupDiscount() + getItemsDiscount()) }}</td> -->
                    <td>{{ getSafeTotalItemDiscount() }}</td>
                    <td>{{ getSafeTotalGroupDiscount() }}</td>
                    <td>{{ getSafeMenuItemsTotal('totalPriceWithReturns') }}</td>
                  </tr>

                  <tr>
                    <td scope="row" class="subHeading"><b>Profit for party items (in %)</b></td>
                    <td colspan="3"></td>
                    <td>{{ getSafeProfitPercentage() | number:'1.2-2' }} %</td>
                  </tr>

                  <tr>
                    <td scope="row" class="subHeading"><b>Partys</b></td>
                    <td>{{ getSafeSuppliesAndMenuTotal() }}</td>
                    <td>
                      <!-- <div class="d-flex flex-wrap justify-content-evenly align-items-center"> -->
                      <div class="partyDiscountClass">
                        <div class="form-group">
                          <label>Enter Party Discount</label>
                          <!-- <div class="mb-2">
                              <mat-slide-toggle [(ngModel)]="partyTaxDiscount" [ngModelOptions]="{standalone: true}"
                                (change)="onToggleChange($event)">Adjust Percentage</mat-slide-toggle>
                            </div> -->
                          <input formControlName="partyDiscount" type="number"
                            class="highlighted-input form-control input" placeholder="Party Discount" autocomplete="off"
                            step="1" (keyup)="calculatePartyDiscount($event)"
                            (keydown)="restrictSpecialCharacters($event); restrictToIntegers($event)"
                            (focus)="focusFunction('partyDiscount')" (focusout)="focusOutFunction('partyDiscount')">
                          <!-- <i *ngIf="partyTaxDiscount" class="input-icon fa fa-percent perIcon"></i>
                            <span *ngIf="!partyTaxDiscount" class="input-icon amountIcon">₹</span> -->
                          <span class="input-icon radio">
                            <!-- <mat-radio-button 
                                [checked]="partyTaxDiscount" 
                                (click)="onToggleChange($event)"
                                matTooltip="{{ partyTaxDiscount ? 'Switch to Amount Discount' : 'Switch to Percentage Discount' }}"
                                >
                              </mat-radio-button> -->
                            <mat-button-toggle-group name="fontStyle" [value]="partyTaxDiscount" aria-label="Font Style"
                              (change)="onToggleChange($event.value)">
                              <mat-button-toggle [value]="false">₹</mat-button-toggle>
                              <mat-button-toggle [value]="true">%</mat-button-toggle>
                            </mat-button-toggle-group>
                          </span>
                        </div>
                      </div>
                      <!-- <div> -->
                      <!-- Discount in Amount : ₹ {{ this.getTotalPartDiscount(this.createPartyForm.value,this.createPartyForm.value.partyDiscount) }} -->
                      <!--class="d-flex gap-2 align-items-start" <mat-icon style="margin-top: 2px; font-size: 15px;">info</mat-icon> -->
                      <!-- </div> -->
                      <!-- </div> -->

                    </td>
                    <td>{{ getSafeTotalPartDiscount() }}</td>
                    <td>{{ getSafePriceWithReturns() }}</td>
                  </tr>

                  <tr>
                    <td scope="row" class="subHeading"><b>Amount Returns</b></td>
                    <td colspan="3"></td>
                    <td>{{ getSafeTotalReturns() }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 
        <div class="topContents mt-3">
        <form [formGroup]="createPartyForm" cdkTrapFocus> -->
        <div class="my-2 partyTitles">
          BASIC DETAILS
        </div>
        <mat-divider></mat-divider>
        <div class="mt-2" cdkTrapFocus>
          <div class="row">
            <div class="col-md-3">
              <div class="form-group customHeightfield customSelect">
                <label>Restaurant</label>
                <mat-select class="form-select" id="uomSelect" formControlName="restaurantId"
                  (selectionChange)="selectedRestaurant($event.value)" placeholder="Restaurant">
                  <mat-option *ngFor="let val of this.user.restaurantAccess" [value]="val.restaurantIdOld"
                    [disabled]="updateActive || extraSupplies.length > 0">
                    {{ val.branchName | uppercase }}
                  </mat-option>
                </mat-select>
                <small
                  *ngIf="createPartyForm.get('restaurantId')?.invalid && createPartyForm.get('restaurantId')?.touched"
                  class="text-danger">
                  Restaurant is required.
                </small>
              </div>
            </div>

            <div class="col-md-3">
              <div class="form-group customHeightfield customSelect">
                <label>Price Tiers</label>
                <mat-select class="form-select" id="uomSelect" formControlName="priceTier" placeholder="Price Tier"
                  (selectionChange)="selectedPriceTier($event.value)">
                  <mat-option>
                    <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                      [formControl]="priceTierFilterCtrl"></ngx-mat-select-search>
                  </mat-option>
                  <mat-option *ngFor="let option of priceTiersData | async" [value]="option.id"
                    [disabled]="updateActive">
                    {{option['name'] | titlecase}}
                  </mat-option>
                </mat-select>
                <small *ngIf="createPartyForm.get('priceTier')?.invalid && createPartyForm.get('priceTier')?.touched"
                  class="text-danger">
                  Price Tier is required.
                </small>
              </div>
            </div>

            <div class="col-md-3">
              <div class="form-group customHeightfield">
                <label>Party Name</label>
                <input formControlName="partyName" class="highlighted-input form-control" (keyup)="checkItem($event)"
                  placeholder="Party Name" autocomplete="off" maxlength="20"
                  oninput="this.value = this.value.toUpperCase()" (keydown)="restrictNumbers($event)"
                  [readonly]="updateActive && !checkPartyStatus">

                <small *ngIf="createPartyForm.get('partyName')?.invalid && createPartyForm.get('partyName')?.touched"
                  class="text-danger">
                  Party Name is required and cannot exceed 50 characters.
                </small>
              </div>
            </div>

            <div class="col-md-3">
              <div class="form-group customHeightfield">
                <label>Party Code</label>
                <input formControlName="partyCode" class="highlighted-input form-control" placeholder="Party Code"
                  autocomplete="off" readonly>
              </div>
            </div>

            <div class="col-md-3">
              <div class="form-group customHeightfield">
                <label>Customer Name</label>
                <input formControlName="partyCreator" class="highlighted-input form-control" maxlength="20"
                  placeholder="Customer Name" autocomplete="off" oninput="this.value = this.value.toUpperCase()"
                  (keypress)="restrictNumbers($event)">
                <small
                  *ngIf="createPartyForm.get('partyCreator')?.invalid && createPartyForm.get('partyCreator')?.touched"
                  class="text-danger">
                  Creator Name is required.
                </small>
              </div>
            </div>

            <div class="col-md-3">
              <div class="form-group customHeightfield">
                <label>Customer Phone Number</label>
                <!-- <input formControlName="phoneNumber" type="tel" maxlength="10" class="highlighted-input form-control"
                  placeholder="Phone Number" autocomplete="off"> -->
                <input formControlName="phoneNumber" type="tel" maxlength="10" class="highlighted-input form-control"
                  placeholder="Customer Phone Number" autocomplete="off" pattern="[0-9]*"
                  oninput="this.value = this.value.replace(/[^0-9]/g, '')">
                <!-- <small *ngIf="createPartyForm.get('phoneNumber')?.invalid && createPartyForm.get('phoneNumber')?.touched"
                  class="text-danger">
                  Phone Number must be a valid 10-digit number.
                </small> -->
                <small
                  *ngIf="createPartyForm.get('phoneNumber')?.hasError('invalidPhoneNumber') && createPartyForm.get('phoneNumber')?.touched"
                  class="text-danger">
                  Please enter a valid phone number.
                </small>
                <small
                  *ngIf="createPartyForm.get('phoneNumber')?.hasError('required') && createPartyForm.get('phoneNumber')?.touched"
                  class="text-danger">
                  Phone number is required.
                </small>
              </div>
            </div>

            <div class="col-md-3">
              <div class="form-group customHeightfield">
                <label>Customer Address</label>
                <textarea class="highlighted-input form-control" type="text" placeholder="Customer Address"
                  autocomplete="off" formControlName="address" style="height: 39px !important;"></textarea>
                <small
                  *ngIf="createPartyForm.get('address')?.errors?.['required'] && createPartyForm.get('address')?.touched"
                  class="text-danger">
                  Address is required.
                </small>
                <small
                  *ngIf="createPartyForm.get('address')?.errors?.['maxlength'] && createPartyForm.get('address')?.touched"
                  class="text-danger">
                  Address cannot exceed 200 characters.
                </small>
              </div>
            </div>


            <div class="col-md-3">
              <div class="form-group customHeightfield">
                <label>Customer Email</label>
                <input formControlName="email" type="email" class="highlighted-input form-control"
                  placeholder="Customer Email" autocomplete="off">

                <small *ngIf="createPartyForm.get('email')?.invalid && createPartyForm.get('email')?.touched"
                  class="text-danger">
                  {{ getEmailErrorMessage() }}
                </small>

              </div>
            </div>


            <div class="col-md-3">
              <div class="form-group customHeightfield">
                <label>Start Date</label>
                <div class="date-input-container">
                  <input formControlName="startDate" class="highlighted-input form-control with-icon"
                    placeholder="dd/MM/yy" [matDatepicker]="picker1" autocomplete="off" [min]="minDate"
                    (dateChange)="onStartDateChange()">
                  <mat-datepicker #picker1></mat-datepicker>
                  <button mat-icon-button (click)="picker1.open()" class="date-picker-icon">
                    <mat-icon>calendar_today</mat-icon>
                  </button>
                </div>
                <small *ngIf="createPartyForm.get('startDate')?.invalid && createPartyForm.get('startDate')?.touched"
                  class="text-danger">
                  Start Date is required.
                </small>
              </div>
            </div>

            <div class="col-md-3">
              <div class="form-group customHeightfield">
                <label>End Date</label>
                <div class="date-input-container">
                  <input formControlName="endDate" class="highlighted-input form-control with-icon"
                    placeholder="dd/MM/yy" [matDatepicker]="picker2" autocomplete="off"
                    [min]="createPartyForm.get('startDate')?.value">
                  <mat-datepicker #picker2></mat-datepicker>
                  <button mat-icon-button (click)="picker2.open()" class="date-picker-icon">
                    <mat-icon>calendar_today</mat-icon>
                  </button>
                </div>
              </div>
              <small *ngIf="createPartyForm.get('endDate')?.invalid && createPartyForm.get('endDate')?.touched"
                class="text-danger">
                End Date is required.
              </small>
            </div>

            <!-- <div class="col-md-3">
              <div class="form-group customHeightfield">
                <label>Time</label>
                <div class="time-input-container">
                  <input formControlName="time" class="highlighted-input form-control with-icon" placeholder="HH:mm"
                    [ngxTimepicker]="picker" readonly autocomplete="off">
                  <ngx-material-timepicker #picker></ngx-material-timepicker>
                  <button mat-icon-button (click)="picker.open()" class="time-picker-icon">
                    <mat-icon>access_time</mat-icon>
                  </button>
                </div>
              </div>
            </div> -->

            <div class="col-md-3">
              <div class="form-group customHeightfield">
                <label>Venue</label>
                <input formControlName="venue" class="highlighted-input form-control" placeholder="Venue"
                  autocomplete="off">
                <small *ngIf="createPartyForm.get('venue')?.invalid && createPartyForm.get('venue')?.touched"
                  class="text-danger">
                  Venue is required.
                </small>
              </div>
            </div>

            <div class="col-md-3">
              <div class="form-group customHeightfield">
                <label>Minimum Pax</label>
                <input formControlName="minPax" type="number" class="highlighted-input form-control"
                  placeholder="Minimum Pax" autocomplete="off" step="1" maxlength="4"
                  (keydown)="restrictSpecialCharacters($event); restrictToIntegers($event)"
                  (input)="limitInputLength($event, 'minPax')" (change)="validatePax()"
                  (focus)="focusFunction('minPax')" (focusout)="focusOutFunction('minPax')">
                <span class="material-icons" style="
                        position: absolute;
                        right: 29px;
                        color: #000;
                        top: 20;
                        margin-top: -32px;">
                  person
                </span>
                <small *ngIf="createPartyForm.get('minPax')?.invalid && createPartyForm.get('minPax')?.touched"
                  class="text-danger">
                  Minimum Pax is required and must be at least 1.
                </small>
              </div>
            </div>

            <div class="col-md-3">
              <div class="form-group customHeightfield">
                <label>Maximum Pax</label>
                <input formControlName="maxPax" type="number" class="highlighted-input form-control"
                  placeholder="Maximum Pax" autocomplete="off" step="1" maxlength="4"
                  (keydown)="restrictSpecialCharacters($event); restrictToIntegers($event);"
                  (input)="limitInputLength($event, 'maxPax')" (change)="validatePax()"
                  (focus)="focusFunction('maxPax')" (focusout)="focusOutFunction('maxPax')">
                <span class="material-icons" style="
                      position: relative;
                      margin-top: -33px;
                      float: right;
                      margin-right: 11px;">
                  person
                </span>
                <small *ngIf="isMaxPaxInvalid" class="text-danger">
                  Maximum Pax must be greater than Minimum Pax.
                </small>
              </div>
            </div>

            <div class="col-md-3">
              <div class="form-group customHeightfield customSelect sessionSelect">
                <label>Session</label>
                <mat-select class="form-select" id="uomSelect" formControlName="session" placeholder="Session" multiple
                  (selectionChange)="onSessionChange($event)" disableClose (openedChange)="onOpenedChange($event)">
                  <div>
                    <input type="text" #sessionInput placeholder="ADD SESSION" class="sessionInput"
                      (keydown)="handleKeyDown($event)" (keyup)="checkDuplicateSession($event)"
                      oninput="this.value = this.value.toUpperCase()">
                    <mat-icon class="addSessionIcon" (click)="addNewSession(sessionInput)"
                      style="margin-right: 10px !important;"
                      [ngClass]="{'disabled-icon': duplicateSession || this.checkSession()}"
                      [attr.aria-disabled]="duplicateSession || this.checkSession()">add</mat-icon>
                  </div>
                  <mat-option *ngFor="let val of sessions" [value]="val.name">
                    {{ val.name | uppercase }}
                    <div class="time-input-container" (click)="$event.stopPropagation()" style="display: flex;">
                      <span *ngIf="getSessionTime(val.name)" class="time-label"
                        style="margin-right: 10px !important;">Starts:</span>
                      <input placeholder="HH:mm" [ngxTimepicker]="picker" readonly autocomplete="off"
                        (ngModelChange)="onTimeChange(val.name,$event)" [ngModel]="getSessionTime(val.name)"
                        [ngModelOptions]="{standalone: true}">
                      <ngx-material-timepicker #picker></ngx-material-timepicker>
                      <button mat-icon-button (click)="picker.open()" class="time-picker-icon">
                        <i class="input-icon far fa-clock" style="font-size: 20px !important;"></i>
                      </button>
                    </div>
                  </mat-option>
                </mat-select>
                <small *ngIf="createPartyForm.get('session')?.invalid && createPartyForm.get('session')?.touched"
                  class="text-danger">
                  Session is required.
                </small>
                <small *ngIf="this.timeError" class="text-danger">
                  Session Time is required.
                </small>
              </div>
            </div>
          </div>
        </div>

        <!-- feature party recipe options -->
        <!-- <div class="search-container mt-2">
              <input type="text" class="search-input" placeholder="Search..." (keyup)="filterParties($event)">
              <mat-icon matSuffix class="search-icon">search</mat-icon>
              </div>

            <div class="themes">
              <button class="scroll-btn left" (click)="scrollLeft()"><mat-icon>arrow_back_ios</mat-icon></button>
              <div class="scroll-container" #scrollContainer>
                <mat-card *ngFor="let card of partyGroupData; let i = index" (click)="showCard(card)"
                class="themeCard" [ngStyle]="{'background-color': getCardColor(i)}">
                <div>
                  <div>
                    {{card.partyName}}
                  </div>
                  <div>
                    {{card.createTs || '-'}}
                  </div>
                </div>
                </mat-card>
              </div>
              <button class="scroll-btn right" (click)="scrollRight()"><mat-icon>arrow_forward_ios</mat-icon></button>
            </div>
            <mat-divider></mat-divider> -->
        <!-- feature party recipe options -->

        <div class="bottomContents">
          <div class="row my-3">
            <div class="col">
              <div class="mb-2 topCreateAndUpdateBtn" style="float: right; margin-top: -21px;;">
                <button mat-raised-button matTooltip="close Party" (click)="closeParty()"
                  *ngIf="updateActive && !this.checkPartyStatus">
                  Add Wastage
                </button>

                <button class="stepperBtns ms-2" mat-raised-button color="accent" (click)="openGroup(openGroupDialog)"
                  matTooltip="add group" *ngIf="!this.checkPartyStatus">
                  Add Group
                </button>
              </div>
              <div class="pb-2">
                <div class="partyTitles">
                  PARTY ITEMS
                </div>
              </div>
              <mat-divider></mat-divider>
              <div class="mt-2">
                <mat-accordion multi="true">
                  <mat-expansion-panel *ngFor="let value of itemGroups.slice().reverse(); let i = index"
                    [(expanded)]="expandedPanels[i]">
                    <mat-expansion-panel-header>
                      <mat-panel-title>
                        <span><strong>{{ value.groupName | uppercase }} - ₹{{ getTotalAmount(value) || '0'
                            }}</strong></span>
                      </mat-panel-title>

                      <mat-icon *ngIf="!this.checkPartyStatus" (click)="deleteGroup(i, value.groupName, deleteDialog)"
                        matTooltip="Delete group" style="cursor: grab; margin-left: 10px;">
                        delete
                      </mat-icon>
                      <mat-icon style="margin-left: 10px;">
                        {{ expandedPanels[i] ? 'expand_less' : 'expand_more' }}
                      </mat-icon>
                    </mat-expansion-panel-header>


                    <div *ngIf="!this.checkPartyStatus">
                      <form [formGroup]="getGroupForm(value.groupName)" cdkTrapFocus class="groupInputs">
                        <div class="form-group customHeightfield">
                          <label for="modifierSelect">Item Name</label>
                          <input matInput placeholder="Item Name" [matAutocomplete]="autoPack"
                            class="form-control itemNameClass" formControlName="itemNames"
                            oninput="this.value = this.value.toUpperCase()" [readonly]="isEditing">
                          <mat-autocomplete #autoPack="matAutocomplete"
                            (optionSelected)="itemOptionSelected($event.option.value , value)">
                            <mat-option *ngFor="let item of modifierOptions | async" [value]="item.menuItemName"
                              [disabled]="item.menuItemName == 'No Item Found' || checkDuplicates(item, value)">
                              <span>{{ item.menuItemName | uppercase }}</span>
                            </mat-option>
                          </mat-autocomplete>
                        </div>

                        <div class="form-group customHeightfield">
                          <label for="modifierSelect">Serving Size</label>
                          <input matInput placeholder="Serving Size" [matAutocomplete]="autoPack1"
                            class="form-control servingSizeClass" formControlName="servingSize">
                          <!-- [readonly]="isEditing" -->
                          <mat-autocomplete #autoPack1="matAutocomplete"
                            (optionSelected)="servingOptionSelected($event.option.value , value)">
                            <mat-option *ngFor="let size of servingSizeOptions | async" [value]="size"
                              [disabled]="size == 'No Item Found' || this.groupServingSize.includes(size)">
                              <!-- || this.groupServingSize.includes(size) -->
                              <span>{{ size | uppercase }}</span>
                            </mat-option>
                          </mat-autocomplete>
                        </div>

                        <div class="form-group customHeightfield">
                          <label for="modifierSelect">Quantity</label>
                          <input class="highlighted-input form-control qtyClass" type="number" min="1"
                            formControlName="quantity" placeholder="Quantity" autocomplete="off"
                            (keydown)="restrictSpecialCharacters($event); restrictToIntegers($event);"
                            (keyup)="calQuantity($event, value)" (input)="enforceMaxLength($event, 5)"
                            (focus)="dynamicFocusFunction('quantity', value)" (focusout)="validateQuantity()">
                          <small
                            *ngIf="groupForms['groupName'].get('quantity')?.hasError('min') && groupForms['groupName'].get('quantity')?.touched"
                            class="text-danger">
                            Quantity must be at least 1.
                          </small>
                        </div>


                        <div class="form-group customHeightfield">
                          <label for="modifierSelect">Cost</label>
                          <input class="highlighted-input form-control copClass" type="number" placeholder="cost"
                            autocomplete="off" formControlName="costOfProduction" readonly>
                          <!-- <mat-icon class="infoIcon">info</mat-icon> -->
                        </div>

                        <div class="form-group customHeightfield">
                          <label for="modifierSelect">Selling Price</label>
                          <input class="highlighted-input form-control sellingPriceClass" type="number"
                            placeholder="S.Price" autocomplete="off" formControlName="sellingPrice" readonly>
                          <!-- <mat-icon class="infoIcon">info</mat-icon> -->
                        </div>

                        <div class="form-group customHeightfield infoClass">
                          <label for="modifierSelect">Amount</label>
                          <input class="highlighted-input form-control costClass" type="number" placeholder="Amount"
                            autocomplete="off" formControlName="cost" readonly>
                          <mat-icon class="infoIcon" matTooltip="Quantity * Selling Price = Amount">info</mat-icon>
                        </div>

                        <div class="form-group customHeightfield itemDiscountClass">
                          <label for="modifierSelect">Discount</label>

                          <!-- <div class="mb-2" style="margin-top: -9px;">
                          <mat-slide-toggle [(ngModel)]="itemTaxDiscount" [ngModelOptions]="{standalone: true}"
                            (change)="onToggleItemDis($event, value)">Percentage</mat-slide-toggle> -->
                          <!-- </div> -->
                          <input class="highlighted-input form-control discountClass input" type="number"
                            placeholder="Disc" autocomplete="off" formControlName="itemDiscount"
                            (keyup)="calculateItemDiscount($event, value)" (keydown)="restrictSpecialCharacters($event)"
                            (focus)="dynamicFocusFunction('itemDiscount', value)"
                            (focusout)="dynamicFocusOutFunction('itemDiscount', value)"
                            oninput="if (this.value.length > 10) this.value = this.value.slice(0, 10);">
                          <!-- <i *ngIf="itemTaxDiscount" class="input-icon fa fa-percent perIcon "
                          ></i>
                        <span *ngIf="!itemTaxDiscount" class="input-icon amountIcon"
                          >₹</span> -->
                          <span class="input-icon radio">
                            <!-- <mat-radio-button 
                            [checked]="itemTaxDiscount" 
                            (click)="onToggleItemDis($event, value)"
                            matTooltip="{{ itemTaxDiscount ? 'Switch to Amount Discount' : 'Switch to Percentage Discount' }}"
                            >
                          </mat-radio-button> -->
                            <mat-button-toggle-group name="fontStyle" [value]="itemTaxDiscount" aria-label="Font Style"
                              (change)="onToggleItemDis($event.value,value)">
                              <mat-button-toggle [value]="false">₹</mat-button-toggle>
                              <mat-button-toggle [value]="true">%</mat-button-toggle>
                            </mat-button-toggle-group>
                          </span>
                        </div>

                        <div class="form-group customHeightfield">
                          <label for="modifierSelect">Total Price</label>
                          <input class="highlighted-input form-control tolPriceClass" type="number"
                            placeholder="Total Price" autocomplete="off" formControlName="totalPrice" readonly>
                        </div>

                        <button *ngIf="!isEditing" mat-raised-button color="accent" style="margin-top: 7px;"
                          (click)="addToGroup(value.groupName)" [disabled]="this.groupForms[value.groupName].invalid || 
                          !this.filtered.includes(groupForms[value.groupName].value.servingSize) || 
                          !groupForms[value.groupName].value.quantity || 
                          groupForms[value.groupName].value.quantity <= 0">Add</button>

                        <!-- || !this.filtered.includes(groupForms[value.groupName].value.servingSize) -->
                        <button *ngIf="isEditing" mat-raised-button color="accent" (click)="updateItem(value.groupName)"
                          style="margin-top: 7px;" [disabled]="this.groupForms[value.groupName].invalid || 
                          !this.groupForms[value.groupName].value.quantity || 
                          this.groupForms[value.groupName].value.quantity <= 0">
                              Update
                        </button>

                        <!-- || !this.filtered.includes(this.menuItemsForm.value.servingSize) -->
                      </form>
                    </div>

                    <div style="overflow-x: auto;" class="partTable">
                      <table #table mat-table [dataSource]="dataSources[value.groupName]"
                        style="border-left: 1px solid lightgray !important;">
                        <ng-container matColumnDef="position">
                          <th mat-header-cell *matHeaderCellDef class="custom-tableSmallWidth"> S.No </th>
                          <td mat-cell *matCellDef="let element; let i = index;" class="custom-tableSmallWidth">{{i+1}}
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="custom-tableSmallWidth"></td>
                        </ng-container>

                        <ng-container matColumnDef="itemName">
                          <th mat-header-cell *matHeaderCellDef class="custom-tableLargeWidth"> Item Name </th>
                          <td mat-cell *matCellDef="let element" class="custom-tableLargeWidth">
                            <div class="d-flex align-items-center">
                              <div style="width: 200px; margin-right: 5px;">
                                {{ element.menuItemName }}
                              </div>
                              <button *ngIf="!this.checkPartyStatus"
                                (click)="editItem(value.groupName , element.menuItemName, element.servingSize)"
                                backgroundColor="primary" class="editIconBtn" matTooltip="edit"><mat-icon
                                  class="mt-1">edit</mat-icon></button>
                              <button *ngIf="!this.checkPartyStatus"
                                (click)="deleteItem(value.groupName , element.menuItemName, element.servingSize)"
                                backgroundColor="primary" class="editIconBtn" style="margin-left: 5px;"
                                matTooltip="delete"><mat-icon class="mt-1">delete</mat-icon></button>
                            </div>
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="custom-tableLargeWidth">Total</td>
                        </ng-container>

                        <ng-container matColumnDef="servingSize">
                          <th mat-header-cell *matHeaderCellDef class="custom-header">Serving Size</th>
                          <td mat-cell *matCellDef="let element" class="custom-cell"> {{ element.servingSize }} </td>
                          <td mat-footer-cell *matFooterCellDef class="custom-footer"></td>
                        </ng-container>

                        <ng-container matColumnDef="quantity">
                          <th mat-header-cell *matHeaderCellDef class="custom-tableSmallWidth">Qty</th>
                          <td mat-cell *matCellDef="let element" class="custom-tableSmallWidth"> {{ element.quantity }}
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="custom-tableSmallWidth"> {{
                            this.getTotalValues(value
                            ,'quantity') }} </td>
                        </ng-container>

                        <ng-container matColumnDef="costOfProduction">
                          <th mat-header-cell *matHeaderCellDef class="custom-header">Cost</th>
                          <td mat-cell *matCellDef="let element" class="custom-cell">{{ element.costOfProduction }}</td>
                          <td mat-footer-cell *matFooterCellDef class="custom-footer">{{ this.getTotalValues(value
                            ,'costOfProduction') }}</td>
                        </ng-container>

                        <ng-container matColumnDef="overAllCostOfProduction">
                          <th mat-header-cell *matHeaderCellDef class="custom-header">
                            <div class="d-flex flex-wrap align-items-center">
                              <div>OverAll Cost</div>
                              <mat-icon class="tableInfoIcon" matTooltip="Qty * Cost = OverAll Cost">info</mat-icon>
                            </div>
                          </th>
                          <td mat-cell *matCellDef="let element" class="custom-cell">{{
                            this.notify.truncateAndFloor(element.overAllCostOfProduction) }}
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="custom-footer">{{ this.getTotalValues(value
                            ,'overAllCostOfProduction') }}</td>
                        </ng-container>

                        <ng-container matColumnDef="sellingPrice">
                          <th mat-header-cell *matHeaderCellDef class="custom-header">Selling Price</th>
                          <td mat-cell *matCellDef="let element" class="custom-cell">{{ element.sellingPrice }}</td>
                          <td mat-footer-cell *matFooterCellDef class="custom-footer">{{ this.getTotalValues(value
                            ,'sellingPrice') }}</td>
                        </ng-container>

                        <ng-container matColumnDef="wastage">
                          <th mat-header-cell *matHeaderCellDef class="custom-header">Wastage</th>
                          <td mat-cell *matCellDef="let element" class="custom-cell">
                            <div class="form-group customHeightfield">
                              <input class="highlighted-input form-control" type="number" [(ngModel)]="element.wastage"
                                [ngModelOptions]="{standalone: true}" (keyup)="calculateWastageQnty(element, $event)"
                                style="max-width: 100px;" autocomplete="off" [disabled]="this.checkPartyStatus"
                                (focus)="focusFunctionModel(element ,'wastage')"
                                (focusout)="focusOutFunctionModel(element ,'wastage')">

                            </div>
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="custom-footer">{{ this.getTotalValues(value
                            ,'wastage')
                            }}</td>
                        </ng-container>

                        <ng-container matColumnDef="return">
                          <th mat-header-cell *matHeaderCellDef class="menuMasterCustomTable">Return</th>
                          <td mat-cell *matCellDef="let element" class="menuMasterCustomTable">
                            <div class="form-group customHeightfield">
                              <input class="highlighted-input form-control" type="number" [(ngModel)]="element.return"
                                [ngModelOptions]="{standalone: true}"
                                (keyup)="calculateReturn(value, element, $event); calculateReturnQnty(element, $event)"
                                style="max-width: 100px;" autocomplete="off" [disabled]="this.checkPartyStatus"
                                (focus)="focusFunctionModel(element ,'return')"
                                (focusout)="focusOutFunctionModel(element ,'return')">
                            </div>
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="menuMasterCustomTable">{{
                            this.getTotalValues(value
                            ,'return') }}</td>
                        </ng-container>

                        <ng-container matColumnDef="cost">
                          <th mat-header-cell *matHeaderCellDef class="custom-header">
                            <div class="d-flex flex-wrap align-items-center">
                              <div>Amount</div>
                              <mat-icon class="tableInfoIcon" matTooltip="Qty * Selling Price = Amount">info</mat-icon>
                            </div>
                          </th>
                          <td mat-cell *matCellDef="let element" class="custom-cell"> {{ element.cost || '0' }} </td>
                          <td mat-footer-cell *matFooterCellDef class="custom-footer">{{ this.getTotalValues(value
                            ,'cost') }}</td>
                        </ng-container>

                        <ng-container matColumnDef="itemsDiscount">
                          <th mat-header-cell *matHeaderCellDef class="custom-header">Discount</th>
                          <td mat-cell *matCellDef="let element" class="custom-cell"> {{ element.itemDiscount || '0' }}
                            <span *ngIf="element.itemDiscount > 0 && element.itemTaxDiscount == false">₹</span>
                            <span *ngIf="element.itemDiscount > 0 && element.itemTaxDiscount == true">%</span>
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="custom-footer">
                            <!-- {{ this.getTotalValues(value ,'itemDiscount') }} -->
                            -
                          </td>
                        </ng-container>

                        <ng-container matColumnDef="totalPrice">
                          <th mat-header-cell *matHeaderCellDef class="custom-header">Price</th>
                          <td mat-cell *matCellDef="let element" class="custom-cell"> {{ element.totalPrice }} </td>
                          <td mat-footer-cell *matFooterCellDef class="custom-footer">{{
                            this.getTotalValues(value,'totalPrice') }}</td>
                        </ng-container>

                        <ng-container matColumnDef="totalClosePrice">
                          <th mat-header-cell *matHeaderCellDef class="custom-header">Total Price</th>
                          <td mat-cell *matCellDef="let element" class="custom-cell"> {{ element.totalClosingPrice }}
                          </td>
                          <td mat-footer-cell *matFooterCellDef class="custom-footer">{{ this.getTotalValues(value
                            ,'totalClosingPrice') }}</td>
                        </ng-container>

                        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                        <tr mat-footer-row *matFooterRowDef="displayedColumns" class="border-top border-bottom"
                          style="font-weight: bold;">
                        </tr>
                      </table>
                    </div>

                    <!-- <div class="d-flex flex-wrap justify-content-between"> -->
                    <div class="row mt-2">
                      <!-- app.component.html -->
                      <!-- <div style="width: 230px; margin-left: 70px;"> -->
                      <!-- <div style="width: 480px;margin-left: 20px; margin-top: 10px;"> -->
                      <div class="col-md-6">
                        <!-- <canvas baseChart
                              [data]="doughnutChartData"
                              [options]="doughnutChartOptions"
                              [type]="'doughnut'">
                      </canvas> -->
                        <!-- Bar Chart -->
                        <canvas baseChart [data]="barChartData(value)" [options]="barChartOptions"
                          [type]="barChartType">
                        </canvas>
                      </div>

                      <!-- <div class="mt-2" style="float: right;"> -->
                      <div class="col-md-6 mt-3">
                        <div class="mb-2 float-right">
                          <span class="groupDataLabel">Cost of Production</span> :
                          <span class="groupDataValue"><b>₹ {{ this.getTotalValues(value ,'overAllCostOfProduction')
                              }}</b></span>
                        </div>
                        <div class="mb-2 float-right">
                          <span class="groupDataLabel">Initial Amount</span> :
                          <span class="groupDataValue"><b>₹ {{ this.getTotalValues(value ,'cost') }}</b></span>
                        </div>
                        <div class="mb-2 float-right">
                          <span class="groupDataLabel">Item Amount Discount</span> :
                          <span class="groupDataValue">₹ {{ this.getAmountDiscount(value,false) }}</span>
                        </div>
                        <div class="mb-2 float-right">
                          <span class="groupDataLabel">Item percentage Discount in Amount</span> :
                          <span class="groupDataValue">₹ {{ this.getPercentageDiscount(value,true) }}</span>
                        </div>
                        <div class="mb-2 float-right">
                          <span class="groupDataLabel">Total Amount</span> :
                          <span class="groupDataValue"><b>₹ {{ this.getTotalValues(value,'totalPrice') }}</b></span>
                        </div>
                        <div class="mb-2 float-right">
                          <span class="groupDataLabel">Group Discounts</span> :
                          <span class="groupDataValue">
                            <input class="highlighted-input form-control input disInput" type="number"
                              placeholder="Group Discount" autocomplete="off" [(ngModel)]="value.groupDiscount"
                              [ngModelOptions]="{standalone: true}" (keyup)="calculateGroupDiscount($event, value)"
                              (focus)="focusFunctionModel(value ,'groupDiscount')"
                              (focusout)="focusOutFunctionModel(value ,'groupDiscount')">
                            <span class="input-icon" style="margin-left: -72px !important;">
                              <mat-button-toggle-group name="fontStyle" [value]="value.groupTaxDiscount"
                                aria-label="Font Style" (change)="onToggleGroupDis($event.value,value)">
                                <mat-button-toggle [value]="false">₹</mat-button-toggle>
                                <mat-button-toggle [value]="true">%</mat-button-toggle>
                              </mat-button-toggle-group>
                            </span>
                          </span>
                        </div>
                        <div class="mb-2 float-right">
                          <span class="groupDataLabel">Group Discount in Amount</span> :
                          <span class="groupDataValue">₹ {{ this.getTotalValues(value,'totalPrice') - value.totalPrice
                            || '0'}}</span>
                        </div>
                        <div class="mb-2 float-right">
                          <span class="groupDataLabel">Total Amount After Group Discount</span> :
                          <span class="groupDataValue"><b>₹ {{ value.totalPrice || '0'}}</b></span>
                        </div>

                        <div class="mb-2 float-right">
                          <span class="groupDataLabel">Return Amount</span> :
                          <span class="groupDataValue">
                            <b>₹ {{
                              ((value.totalPrice - value.totalPriceWithReturns) -
                              (this.getAmountDiscount(value, false) +
                              this.getPercentageDiscount(value, true) +
                              (this.getTotalValues(value, 'totalPrice') - value.totalPrice))) > 0
                              ? (value.totalPrice - value.totalPriceWithReturns) -
                              (this.getAmountDiscount(value, false) +
                              this.getPercentageDiscount(value, true) +
                              (this.getTotalValues(value, 'totalPrice') - value.totalPrice))
                              : 0
                              }}</b>
                          </span>
                        </div>

                        <div class="mb-2 float-right">
                          <span class="groupDataLabel">Total Amount After Return Amount</span> :
                          <span class="groupDataValue">
                            <b>₹ {{ getTotalAmount(value) }}</b>
                          </span>
                        </div>

                      </div>

                    </div>

                    <!-- <div class="mt-3 d-flex justify-content-end align-items-center gap-3 ">
                    <div class="form-group customHeightfield groupDiscountClass">
                      <input class="highlighted-input form-control input" type="number" placeholder="Group Discount"
                        autocomplete="off" [(ngModel)]="value.groupDiscount" [ngModelOptions]="{standalone: true}"
                        (keyup)="calculateGroupDiscount($event, value)"
                        (focus)="focusFunctionModel(value ,'groupDiscount')"
                        (focusout)="focusOutFunctionModel(value ,'groupDiscount')">
                      <span class="input-icon radio">
                        <mat-button-toggle-group name="fontStyle" [value]="value.groupTaxDiscount" aria-label="Font Style" (change)="onToggleGroupDis($event.value,value)">
                          <mat-button-toggle [value]="true">%</mat-button-toggle>
                        </mat-button-toggle-group>
                      </span>
                    </div>
                    <div>
                      <div>
                        <span>Total Price:</span>
                      </div>
                      <div class="groupTotalClass">
                        <b>{{ value.totalPrice }}</b>
                      </div>
                    </div>
                  </div> -->

                  </mat-expansion-panel>
                </mat-accordion>
              </div>
            </div>
          </div>
        </div>

        <div>
          <div class="my-2 partyTitles">
            EXTRAS
          </div>
          <mat-divider></mat-divider>
          <div class="mt-2">
            <div class="row" *ngIf="!this.checkPartyStatus" cdkTrapFocus>
              <div class="col">
                <div class="form-group customHeightfield">
                  <label for="uomSelect">Name</label>
                  <input class="highlighted-input form-control" placeholder="Name" autocomplete="off"
                    [formControl]="supplyName" (keyup)="checkDuplicateSupply($event)">
                  <mat-error *ngIf="emptySupplyName" class="mt-2">*Supply name cannot be empty or contain only
                    spaces</mat-error>
                  <mat-error *ngIf="duplicateSupply" class="mt-2">*Supply already exists</mat-error>
                </div>
              </div>

              <div class="col">
                <div class="form-group customHeightfield">
                  <label for="uomSelect">Quantity</label>
                  <input class="highlighted-input form-control" type="number" (input)="enforceMaxLength($event, 5)"
                    placeholder="Quantity" autocomplete="off" [formControl]="quantity"
                    (keyup)="calculateExtraTotal($event)"
                    (keydown)="restrictSpecialCharacters($event); restrictToIntegers($event);"
                    (focus)="focusFunctionFromControl('quantity')" (focusout)="focusOutFunctionFromControl('quantity')">
                </div>
              </div>

              <div class="col">
                <div class="form-group customHeightfield">
                  <label for="uomSelect">Cost</label>
                  <input class="highlighted-input form-control" type="number" placeholder="Cost" autocomplete="off"
                    [formControl]="cost" (keyup)="calculateExtraTotal($event)"
                    (keydown)="restrictSpecialCharacters($event)" (focus)="focusFunctionFromControl('cost')"
                    (focusout)="focusOutFunctionFromControl('cost')" (input)="enforceMaxLength($event, 5)">
                </div>
              </div>

              <div class="col">
                <div class="form-group customHeightfield">
                  <label for="uomSelect">Price</label>
                  <input class="highlighted-input form-control" [formControl]="price" type="number" placeholder="Price"
                    autocomplete="off" readonly>
                </div>
              </div>

              <div class="col">
                <div class="form-group customHeightfield">
                  <label for="uomSelect">Description</label>
                  <textarea class="highlighted-input form-control" type="text" placeholder="Description"
                    autocomplete="off" [formControl]="description" style="height: 39px !important;"></textarea>
                </div>
              </div>

              <div class="col">
                <button mat-raised-button color="accent" (click)="addSupply()" matTooltip="add supply"
                  style="margin-top: 20px;"
                  [disabled]="duplicateSupply || emptySupplyName || !this.supplyName.value || !this.quantity.value || !this.cost.value">Add</button>
              </div>
            </div>

            <div class="extraSuppliesClass m-1">
              <div *ngIf="extraSupplies.controls.length > 0">
                <table class="table table-bordered">
                  <thead>
                    <tr>
                      <th scope="col"><b>#</b></th>
                      <th scope="col"><b>Supply Name</b></th>
                      <th scope="col"><b>Quantity</b></th>
                      <th scope="col"><b>Cost</b></th>
                      <th scope="col"><b>Price</b></th>
                      <th scope="col"><b>Description</b></th>
                      <th scope="col" *ngIf="!this.checkPartyStatus"><b>Action</b></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let supply of extraSupplies.controls; let i = index">
                      <td scope="row">{{ i + 1 }}</td>
                      <td>{{ supply.value.supplyName }}</td>
                      <td>{{ supply.value.quantity }}</td>
                      <td>{{ supply.value.cost }}</td>
                      <td>{{ supply.value.totalPrice }}</td>
                      <td>{{ supply.value.description }}</td>
                      <td>
                        <mat-icon *ngIf="!this.checkPartyStatus" (click)="deleteSupply(i)">delete</mat-icon>
                      </td>
                    </tr>
                  </tbody>
                  <tfoot>
                    <tr>
                      <td></td>
                      <td class="text-start"><strong>Total</strong></td>
                      <td><b>{{this.getSuppliesTotal('quantity')}}</b></td>
                      <td></td>
                      <td><b>{{this.getSuppliesTotal('totalPrice')}}</b></td>
                      <td></td>
                      <td></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </div>


          <!-- <mat-accordion>
            <mat-expansion-panel [expanded]="true">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <span><strong>EXTRAS</strong> </span>
                </mat-panel-title>
                <mat-panel-description>
                  Add and manage extras
                </mat-panel-description>
              </mat-expansion-panel-header>

              <div class="row" *ngIf="!this.checkPartyStatus" cdkTrapFocus>
                <div class="col">
                  <div class="form-group customHeightfield">
                    <label for="uomSelect">Name</label>
                    <input class="highlighted-input form-control" placeholder="Name" autocomplete="off"
                      [formControl]="supplyName" (keyup)="checkDuplicateSupply($event)">
                    <mat-error *ngIf="emptySupplyName" class="mt-2">*Supply name cannot be empty or contain only
                      spaces</mat-error>
                    <mat-error *ngIf="duplicateSupply" class="mt-2">*Supply already exists</mat-error>
                  </div>
                </div>

                <div class="col">
                  <div class="form-group customHeightfield">
                    <label for="uomSelect">Quantity</label>
                    <input class="highlighted-input form-control" type="number" (input)="enforceMaxLength($event, 5)"
                      placeholder="Quantity" autocomplete="off" [formControl]="quantity"
                      (keyup)="calculateExtraTotal($event)"
                      (keydown)="restrictSpecialCharacters($event); restrictToIntegers($event);"
                      (focus)="focusFunctionFromControl('quantity')"
                      (focusout)="focusOutFunctionFromControl('quantity')">
                  </div>
                </div>

                <div class="col">
                  <div class="form-group customHeightfield">
                    <label for="uomSelect">Cost</label>
                    <input class="highlighted-input form-control" type="number" placeholder="Cost" autocomplete="off"
                      [formControl]="cost" (keyup)="calculateExtraTotal($event)"
                      (keydown)="restrictSpecialCharacters($event)" (focus)="focusFunctionFromControl('cost')"
                      (focusout)="focusOutFunctionFromControl('cost')" (input)="enforceMaxLength($event, 5)">
                  </div>
                </div>

                <div class="col">
                  <div class="form-group customHeightfield">
                    <label for="uomSelect">Price</label>
                    <input class="highlighted-input form-control" [formControl]="price" type="number"
                      placeholder="Price" autocomplete="off" readonly>
                  </div>
                </div>

                <div class="col">
                  <div class="form-group customHeightfield">
                    <label for="uomSelect">Description</label>
                    <textarea class="highlighted-input form-control" type="text" placeholder="Description"
                      autocomplete="off" [formControl]="description" style="height: 39px !important;"></textarea>
                  </div>
                </div>

                <div class="col">
                  <button mat-raised-button color="accent" (click)="addSupply()" matTooltip="add supply"
                    style="margin-top: 20px;"
                    [disabled]="duplicateSupply || emptySupplyName || !this.supplyName.value || !this.quantity.value || !this.cost.value">Add</button>
                </div>
              </div>

              <div class="extraSuppliesClass m-1">
                <div *ngIf="extraSupplies.controls.length > 0">
                  <table class="table table-bordered">
                    <thead>
                      <tr>
                        <th scope="col"><b>#</b></th>
                        <th scope="col"><b>Supply Name</b></th>
                        <th scope="col"><b>Quantity</b></th>
                        <th scope="col"><b>Cost</b></th>
                        <th scope="col"><b>Price</b></th>
                        <th scope="col"><b>Description</b></th>
                        <th scope="col" *ngIf="!this.checkPartyStatus"><b>Action</b></th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let supply of extraSupplies.controls; let i = index">
                        <th scope="row">{{ i + 1 }}</th>
                        <td>{{ supply.value.supplyName }}</td>
                        <td>{{ supply.value.quantity }}</td>
                        <td>{{ supply.value.cost }}</td>
                        <td>{{ supply.value.totalPrice }}</td>
                        <td>{{ supply.value.description }}</td>
                        <td>
                          <mat-icon *ngIf="!this.checkPartyStatus" (click)="deleteSupply(i)">delete</mat-icon>
                        </td>
                      </tr>
                    </tbody>
                    <tfoot>
                      <tr>
                        <td></td>
                        <td class="text-start"><strong>Total</strong></td>
                        <td><b>{{this.getSuppliesTotal('quantity')}}</b></td>
                        <td></td>
                        <td><b>{{this.getSuppliesTotal('totalPrice')}}</b></td>
                        <td></td>
                        <td></td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </mat-expansion-panel>
          </mat-accordion> -->
        </div>


      </form>
    </div>
  </div>

  <ng-template #openGroupDialog>
    <div class="closeBtn">
      <mat-icon (click)="closeDialog()" matTooltip="Close" class="closeBtnIcon">close</mat-icon>
    </div>
    <div class="m-4">
      <div class="bottomTitles p-3 mb-3">
        <span>Create Menu Group</span>
      </div>
      <div>
        <mat-form-field appearance="outline" class="groupInput">
          <mat-label>Group Name</mat-label>
          <input matInput placeholder="Group Name" [formControl]="groupName"
            oninput="this.value = this.value.toUpperCase(); if (this.value.length > 25) this.value = this.value.slice(0, 25);"
            (keyup)="checkDuplicatesGroup($event)">
        </mat-form-field>
        <mat-error *ngIf="emptyGroupName" class="mt-2">*Group name cannot be empty or contain only spaces</mat-error>
        <mat-error *ngIf="duplicateMenuGroup" class="mt-2">*Menu group already exists</mat-error>

      </div>
      <div class="d-flex justify-content-end d-flex mt-2">
        <button mat-flat-button type="button" color="accent" matTooltip="create group" (click)="createGroup()"
          [disabled]="duplicateMenuGroup || emptyGroupName || !groupName.value">
          <mat-icon>add_circle</mat-icon>Create
        </button>
      </div>
    </div>
  </ng-template>

  <ng-template #deleteDialog>
    <div class="m-4">
      <div class="bottomTitles p-3 mb-3">
        <span>Delete group</span>
      </div>
      <div class="mb-4" style="font-weight: bold; font-size: larger;">
        Are you sure you want to delete the group '{{delName}}'?
      </div>
      <div class="d-flex justify-content-end d-flex gap-2">
        <button mat-flat-button type="button" (click)="closeDialog()" matTooltip="close">Cancel</button>
        <button mat-flat-button type="button" matTooltip="remove group" color="accent" (click)="onOk()">Remove
          Group</button>
      </div>
    </div>
  </ng-template>