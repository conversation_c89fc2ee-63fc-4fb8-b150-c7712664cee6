import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostListener, Inject, QueryList, TemplateRef, ViewChild, ViewChildren } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { FormBuilder, FormControl, FormGroup, Validators, FormArray } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { Observable, ReplaySubject, Subject, map, of, startWith, takeUntil } from 'rxjs';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { MatDividerModule } from '@angular/material/divider';
import { FormsModule } from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { MatDateRangePicker } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { NativeDateAdapter, MAT_DATE_FORMATS, DateAdapter, MAT_DATE_LOCALE } from '@angular/material/core';
import { NgxMaterialTimepickerComponent, NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { DateTime } from 'luxon';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { InventoryService } from 'src/app/services/inventory.service';
import { AuthService } from 'src/app/services/auth.service';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ShareDataService } from 'src/app/services/share-data.service';
import { NotificationService } from 'src/app/services/notification.service';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { A11yModule } from '@angular/cdk/a11y';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MasterDataService } from 'src/app/services/master-data.service';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { Chart, ChartConfiguration, ChartData } from 'chart.js';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { ChartOptions, ChartType } from 'chart.js';

export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'MM/DD/YYYY',
  },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'MM/DD/YYYY',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-create-party',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatInputModule,
    MatIconModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    NgxMatSelectSearchModule,
    MatSelectModule,
    MatDividerModule,
    FormsModule,
    MatRadioModule,
    MatNativeDateModule,
    MatDatepickerModule,
    NgxMaterialTimepickerModule,
    MatTableModule,
    MatExpansionModule,
    MatTooltipModule,
    MatAutocompleteModule,
    NgxSkeletonLoaderModule,
    A11yModule,
    MatSlideToggleModule,
    MatButtonToggleModule,
    NgChartsModule
  ],
  providers: [
    { provide: DateAdapter, useClass: NativeDateAdapter },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS },
    { provide: MAT_DATE_LOCALE, useValue: 'en-US' },
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './create-party.component.html',
  styleUrls: ['./create-party.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class CreatePartyComponent {
  dialogRef: MatDialogRef<any>;
  isDuplicate: boolean;
  protected _onDestroy = new Subject<void>();
  public menuItemBank: any[] = [];
  public menuItemFilterCtrl: FormControl = new FormControl();
  public menuItemsData: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public priceTierBank: any[] = [];
  public priceTierFilterCtrl: FormControl = new FormControl();
  public priceTiersData: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  createPartyForm: FormGroup;
  menuItemsForm!: FormGroup;
  readonly range = new FormGroup({
    start: new FormControl<Date | null>(null),
    end: new FormControl<Date | null>(null),
  });
  @ViewChild('picker') picker!: MatDateRangePicker<Date>;
  selected: Date | null = null;
  startDate: Date | null = null;
  endDate: Date | null = null;
  minTime: DateTime;
  maxTime: DateTime;
  @ViewChild('timepicker') timepicker: any;
  dataSource: MatTableDataSource<any> = new MatTableDataSource<any>();
  displayedColumns = ['position', 'itemName', 'servingSize', 'quantity', 'costOfProduction', 'overAllCostOfProduction', 'sellingPrice', 'cost', 'itemsDiscount', 'totalPrice']
  user: any;
  partyOrderId: any;
  updateActive: boolean = false;
  partyList: any[];
  existingData: any;
  itemGroups: any[] = []
  menuData: any;
  groupName = new FormControl();
  groupHeading: any;
  delName: any;
  partyNameControl = new FormControl();
  copiedParty: any;
  logo: any;
  cards = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
  lightColors = [
    '#FFEBEE', '#FCE4EC', '#F3E5F5', '#EDE7F6', '#E8EAF6',
    '#E3F2FD', '#E1F5FE', '#E0F7FA', '#E0F2F1', '#E8F5E9'
  ];
  @ViewChild('scrollContainer') scrollContainer!: ElementRef;
  @ViewChild('draftDialog') draftDialog: TemplateRef<any>;
  partyGroupData: any;
  themeName: any;
  tempPartyGroupData: any;
  supplyName = new FormControl('');
  quantity = new FormControl(0);
  price = new FormControl(0);
  cost = new FormControl(0);
  description = new FormControl('');
  modifierOptions: Observable<any[]>;
  servingSizeOptions: Observable<any[]>;
  filtered: any[];
  servingSizeData: any[];
  opConfigData: any;
  priceList: any[];
  ratio: number;
  menuCost: any = 0;
  overAllSellingPrice: any;
  invItems: any;
  nameOptionsBank: any;
  ingredientData: any[];
  menuRecipes: any;
  modifiers: any;
  sellingPriceArray: any[];
  menuMasterServingSize: any;
  menuMasterData: any;
  profitMargin: number;
  profitPercentage: number;
  costPercentage: number;
  dataSources: { [key: string]: MatTableDataSource<any> } = {};
  isEditing: boolean = false;
  totalSuppliesPrice: any;
  totalMenuItemsPrice: any;
  totalMenuItemsReturnsPrice: number;
  duplicateSupply: boolean;
  minDate: Date;
  partyClosed: boolean = false;
  addWastagePressed: boolean = false;
  checkPartyStatus: boolean = false;
  groupServingSize: any = [];
  duplicateMenuGroup: boolean;
  isPanelOpen: boolean = false;
  tempTotalPrice: number;
  tempItemDiscount: number;
  showDuplicate: boolean;
  emptySupplyName: boolean;
  emptyGroupName: boolean;
  partyCode: any;
  sessions: any = [
    { name: 'Breakfast', time: '' },
    { name: 'Lunch', time: '' },
    { name: 'Dinner', time: '' }
  ];
  partyTaxDiscount: boolean = false;
  itemTaxDiscount: boolean = false;
  groupTaxDiscount: boolean = false;
  updatedSessions: any[] = [];
  @ViewChildren('picker') pickers!: QueryList<NgxMaterialTimepickerComponent>;
  pickerMap: { [key: string]: any } = {};
  sessionOptions: Observable<any[]>;
  question = 'Would you like to add "';
  newSession: any;
  priceTierList: { id: any; name: any; }[];
  duplicateSession: any;
  selectedSessions: any[] = [];
  timeError: boolean = false;
  dynamicForm!: FormGroup;
  groupForms: { [key: string]: FormGroup } = {};
  expandedPanels: boolean[] = [];
  isMaxPaxInvalid: boolean = false;

  doughnutChartData: ChartConfiguration<'doughnut'>['data'] = {
    labels: ['Red', 'Blue', 'Yellow'],
    datasets: [
      {
        data: [12, 19, 3],
        backgroundColor: [
          'rgba(255, 99, 132, 0.7)',
          'rgba(54, 162, 235, 0.7)',
          'rgba(255, 206, 86, 0.7)',
        ],
        hoverBackgroundColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
        ]
      }
    ]
  };

  doughnutChartOptions: ChartConfiguration<'doughnut'>['options'] = {
    responsive: true,
    plugins: {
      legend: {
        display: true,
        position: 'right'
      }
    }
  };
  @ViewChild(BaseChartDirective) chart: BaseChartDirective;
  public barChartType: ChartType = 'bar';
  // Bar chart options
  public barChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
        position: 'top',
      }
    },
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      }
    }
  };

  constructor(
    public formBuilder: FormBuilder,
    private cd: ChangeDetectorRef,
    private api: InventoryService,
    private auth: AuthService,
    public dialog: MatDialog,
    private router: Router,
    private sharedData: ShareDataService,
    public notify: NotificationService,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private masterDataService: MasterDataService,
  ) {
    this.user = this.auth.getCurrentUser();
    this.minTime = DateTime.now();
    this.maxTime = DateTime.now().set({ hour: 23, minute: 59 })
    this.minDate = new Date();
    //this.expandedPanels = new Array(this.itemGroups?.length).fill(true);
    this.createPartyForm = this.formBuilder.group({
      restaurantId: ['', Validators.required],
      priceTier: ['', Validators.required],
      partyName: ['', Validators.required],
      partyCode: ['', Validators.required],
      partyCreator: ['', Validators.required],
      phoneNumber: ['', [Validators.required, this.phoneNumberValidator]],
      address: ['', [Validators.required, Validators.maxLength(200)]],
      email: ['', [
        Validators.required,
        Validators.pattern(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)
      ]],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      venue: ['', Validators.required],
      extraSupplies: this.formBuilder.array([]),
      price: [0, Validators.required],
      discontinued: ['no', Validators.required],
      minPax: [1, [Validators.required, Validators.min(1)]],
      maxPax: [1, [Validators.required, Validators.min(1)]],
      partyDiscount: [0],
      session: ['', Validators.required],
      actualPrice: [0],
      priceWithReturns: [0],
      partyTaxDiscount: []
    });

    // this.menuItemsForm = this.formBuilder.group({
    // itemNames: ['', Validators.required],
    // servingSize: ['', Validators.required],
    // quantity: [1, Validators.required],
    // costOfProduction: [0, Validators.required],
    // sellingPrice: [0, Validators.required],
    // itemDiscount: [0],
    // cost: [0],
    // totalPrice: [0, Validators.required],
    // })

    // this.menuItemsForm = this.formBuilder.group({
    //   item: this.formBuilder.array([])  // This will hold the dynamic form array
    // });

    this.groupForms = {
      groupName: new FormGroup({
        itemNames: new FormControl(''),
        servingSize: new FormControl(''),
        quantity: new FormControl(1, [Validators.required, Validators.min(1)]),
        costOfProduction: new FormControl(''),
        sellingPrice: new FormControl(''),
        cost: new FormControl(''),
        itemDiscount: new FormControl(''),
        totalPrice: new FormControl('')
      })
    };

    this.modifiers = this.sharedData.getModifiers().value;

    this.sharedData.getPartyNames.subscribe((obj) => {
      this.partyList = Array.from(new Set(obj)).flat();
    });

    this.sharedData.getCopiedParty.subscribe((obj) => {
      this.copiedParty = obj
    });

    // this.sharedData.getPartyData.subscribe((data) => {
    //   this.partyGroupData = data
    //   this.tempPartyGroupData = this.partyGroupData
    // });

    // if (this.dialogData != null && (this.dialogData.key == false || this.dialogData.partyDraft == true)) {
    //sk-change
    if (this.dialogData.key == false) {
      this.updateActive = true;
      this.isDuplicate = this.dialogData.key;
    } else if (this.dialogData.partyDraft == true) {
      this.updateActive = false;
      this.isDuplicate = false;
    }

    if (Object.keys(this.dialogData.elements).length > 0) {
      let groupNames = this.dialogData.elements.recipes.map(item => item.groupName)
      groupNames.forEach(name => {
        this.initializeGroupForms(name);
        this.sharedData.getMenuData.subscribe((obj) => {
          this.menuData = obj
          this.modifierOptions = this.groupForms[name].get('itemNames').valueChanges.pipe(startWith(''), map(value => this._filterSearch((value || ''), this.menuData['menu master'] ? this.menuData['menu master'] : [])));
          this.menuItemBank = this.menuData['menu master'];
          this.menuItemsData.next(this.menuItemBank?.slice());
          this.menuItemFilterCtrl.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
              this.FilterSearchMenu(
                this.menuItemBank,
                this.menuItemFilterCtrl,
                this.menuItemsData
              );
            });
        });
      })
      this.bindData(this.dialogData.elements);
    } else {
      this.isDuplicate = this.dialogData.key;
      this.addOption();
    }
  }

  getEmailErrorMessage() {
    const emailControl = this.createPartyForm.get('email');

    if (emailControl?.hasError('required')) {
      return 'Email is required.';
    } else if (emailControl?.hasError('pattern')) {
      return 'Please enter a valid email address (e.g., <EMAIL>).';
    }
    return '';
  }

  validateQuantity() {
    const quantityControl = this.createPartyForm.get('quantity');
    if (quantityControl?.value === '' || quantityControl?.value < 1) {
      quantityControl?.setValue(1); // Reset to 1 only when empty or invalid
    }
  }


  protected _filterSearch(value: any, input: any[]): string[] {
    let filterValue = value.toLowerCase();
    this.filtered = input.filter(option => option['menuItemName'].toLowerCase().includes(filterValue));
    if (this.filtered.length == 0) {
      this.filtered = [{ 'menuItemName': 'No Item Found' }];
    }
    return this.filtered
  }

  phoneNumberValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const phoneRegex = /^(?:(?:\+91|91|0)?[6-9]\d{9})$/;
    if (control.value && !phoneRegex.test(control.value)) {
      return { invalidPhoneNumber: true };
    }
    return null;
  }


  getRandomLightColor(): string {
    const r = Math.floor(Math.random() * 156) + 100; // Random number between 100-255 for light shades
    const g = Math.floor(Math.random() * 156) + 100;
    const b = Math.floor(Math.random() * 156) + 100;
    return `rgb(${r}, ${g}, ${b})`;
  }

  getCardColor(index: number): string {
    return this.lightColors[index % this.lightColors.length];
  }

  ngOnInit() {
    this.expandedPanels = new Array(this.itemGroups.length).fill(true);

    Chart.register({
      id: 'centerTextPlugin',
      beforeDraw(chart) {
        const { width } = chart;
        const { height } = chart;
        const ctx = chart.ctx;
        ctx.save();
        const text = ''; // Replace with your desired text
        ctx.font = 'bold 20px Arial';
        ctx.textAlign = 'right';
        ctx.textBaseline = 'top';
        ctx.fillStyle = '#000';
        ctx.fillText(text, width / 2, height / 2);
      }
    });

    this.onStartDateChange()
    const controlNames = Object.keys(this.createPartyForm.controls);
    controlNames.forEach((controlName, index) => {
      if (index < controlNames.length - 1) {
        const currentControl = this.createPartyForm.get(controlName);
        const nextControl = this.createPartyForm.get(controlNames[index + 1]);
        if (currentControl && nextControl) {
          currentControl.statusChanges.subscribe((status) => {
            if (status === 'VALID') {
              nextControl.enable();
            } else {
              nextControl.disable();
            }
          });
        }
      }
    });

    this.sessionOptions = this.createPartyForm.get('session').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.sessions)));
    this.sharedData.getPartiesRecipe.subscribe((obj) => {
      if (Object.keys(obj).length > 0) {
        this.dataSource.data.push(...obj)
        this.dataSource.data = Array.from(new Map(this.dataSource.data.map(item => [item.menuItemCode, item])).values());
      }
    });
  }

  protected _filter(value: string, input: string[]): string[] {
    let filterValue = value.toLowerCase();
    let filtered = input.filter(option => option.toLowerCase().includes(filterValue));
    if (filtered.length == 0) {
      filtered = [this.question + value + '"'];
    }
    return filtered
  }

  FilterSearchMenu(menuItemBank, menuItemFilterCtrl, menuItemsData) {
    const searchValue = menuItemFilterCtrl.value ? menuItemFilterCtrl.value.toLowerCase() : '';
    const filteredMenuItems = menuItemBank.filter((menuItem) => {
      return menuItem.menuItemName.toLowerCase().includes(searchValue);
    });
    menuItemsData.next(filteredMenuItems);
  }

  FilterSearchPriceTier(bank, FilterCtrl, data) {
    const searchValue = FilterCtrl.value ? FilterCtrl.value.toLowerCase() : '';
    const filteredData = bank.filter((data) => {
      return data.name.toLowerCase().includes(searchValue);
    });
    data.next(filteredData);
  }

  initializeDataSources() {
    this.itemGroups.forEach(group => {
      this.dataSources[group.groupName] = new MatTableDataSource(group.items);
    });
  }

  getPriceTires() {
    this.api.getPOSPriceTires(this.user.tenantId, this.createPartyForm.value.restaurantId).subscribe({
      next: (res) => {
        if (Array.isArray(res)) {
          this.priceTierList = res.map(({ id, name }) => ({ id, name }));
          this.priceTierBank = this.priceTierList;
          this.priceTiersData.next(this.priceTierBank?.slice());
          this.priceTierFilterCtrl.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
              this.FilterSearchPriceTier(
                this.priceTierBank,
                this.priceTierFilterCtrl,
                this.priceTiersData
              );
            });
          // let decorationPackage
          // if(this.object && this.object[this.branchData.value]){
          //   decorationPackage = this.priceTierList.find(item => item.name === this.object[this.branchData.value].priceTierName || item.id  === this.object[this.branchData.value].defaultPriceTier );
          // }       
          // this.priceData.setValue(decorationPackage || null);
        }
      },
      error: (err) => {
        console.error(err);
      },
    });
  }

  bindData(data) {
    this.existingData = data
    this.updatedSessions = this.existingData && this.existingData.session ? [...this.existingData.session] : [];
    this.createPartyForm.patchValue({
      restaurantId: data.restaurantId,
      priceTier: data.priceTier,
      partyName: data.partyName,
      partyCreator: data.partyCreator,
      partyCode: data.partyCode,
      phoneNumber: data.phoneNumber,
      address: data.address,
      email: data.email,
      startDate: data.startDate,
      endDate: data.endDate,
      venue: data.venue,
      price: data.price,
      minPax: data.minPax,
      maxPax: data.maxPax,
      partyDiscount: data.partyDiscount,
      discontinued: data.discontinued,
      actualPrice: data.actualPrice,
      partyTaxDiscount: data.partyTaxDiscount
    });

    const sessionData = data.session ? data.session.map(item => item.name) : [];
    this.createPartyForm.patchValue({
      session: sessionData
    });
    let val = data && data.session ? data.session : [];
    this.sessions.unshift(...val)
    this.sessions = this.sessions.filter(
      (session, index, self) =>
        index === self.findIndex((s) => s.name === session.name)
    );
    this.partyOrderId = data.partyOrderId
    this.dataSource.data = data.recipes
    this.itemGroups = [...data.recipes]
    this.initializeDataSources();
    const extraSuppliesArray = this.createPartyForm.get('extraSupplies') as FormArray;
    extraSuppliesArray.clear();
    if (data.extraSupplies && data.extraSupplies.length > 0) {
      data.extraSupplies.forEach(supply => {
        extraSuppliesArray.push(this.formBuilder.group({
          supplyName: [supply.supplyName || ''],
          quantity: [supply.quantity || ''],
          totalPrice: [supply.totalPrice || ''],
          cost: [supply.cost || ''],
          description: [supply.description || '']
        }));
      });
    }

    if (data.restaurantId) {
      this.selectedRestaurant(data.restaurantId)
    }

    this.checkPartyStatus = data.partyClosed
    if (this.checkPartyStatus === true) {
      this.displayedColumns = ['position', 'itemName', 'servingSize', 'quantity', 'costOfProduction', 'overAllCostOfProduction', 'sellingPrice', 'wastage', 'return', 'cost', 'itemsDiscount', 'totalPrice']
    }
    this.isPanelOpen = this.checkPartyStatus
    this.partyTaxDiscount = data.partyTaxDiscount
  }

  close() {
    this.sharedData.setViewRecipe({});
    this.masterDataService.setNavigation('party');
    this.router.navigate(['/dashboard/home']);
    this.dialog.closeAll();
  }

  onOpenedChange(isOpened: boolean) {
    if (isOpened) {
    } else {
    }
  }

  submit() {
    let inputObj = {}
    if (this.updateActive) {
      const startDateControl = this.createPartyForm.get('startDate');
      if (startDateControl) {
        startDateControl.clearValidators(); // Remove validators
        startDateControl.updateValueAndValidity(); // Recalculate form status
      }
    }

    if (this.createPartyForm.invalid) {
      const invalidControls: { [key: string]: any } = {};
      Object.keys(this.createPartyForm.controls).forEach((key) => {
        const control = this.createPartyForm.get(key);
        if (control && control.invalid) {
          invalidControls[key] = control.value;
        }
      });
      this.createPartyForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields')
    } else if (this.timeError) {
      this.notify.snackBarShowError('Please fill out all required fields')
    } else {
      let startDate = new Date(this.createPartyForm.value.startDate);
      startDate.setHours(0, 0, 0, 0);
      let endDate = new Date(this.createPartyForm.value.endDate);
      endDate.setHours(0, 0, 0, 0);
      let startDateISO = startDate.toLocaleDateString('en-GB', { timeZone: 'Asia/Kolkata' }).split('/').reverse().join('-') + "T00:00:00";
      let endDateISO = endDate.toLocaleDateString('en-GB', { timeZone: 'Asia/Kolkata' }).split('/').reverse().join('-') + "T00:00:00";

      inputObj['tenantId'] = this.user.tenantId
      inputObj['restaurantId'] = this.createPartyForm.value.restaurantId
      inputObj['priceTier'] = this.createPartyForm.value.priceTier
      inputObj['partyName'] = this.createPartyForm.value.partyName
      inputObj['partyCreator'] = this.createPartyForm.value.partyCreator
      inputObj['partyCode'] = this.createPartyForm.value.partyCode
      inputObj['phoneNumber'] = this.createPartyForm.value.phoneNumber
      inputObj['address'] = this.createPartyForm.value.address
      inputObj['email'] = this.createPartyForm.value.email
      inputObj['startDate'] = startDateISO
      inputObj['endDate'] = endDateISO
      inputObj['venue'] = this.createPartyForm.value.venue
      inputObj['minPax'] = this.createPartyForm.value.minPax
      inputObj['maxPax'] = this.createPartyForm.value.maxPax
      inputObj['partyDiscount'] = this.createPartyForm.value.partyDiscount
      inputObj['session'] = this.updatedSessions
      inputObj['extraSupplies'] = this.createPartyForm.value.extraSupplies
      inputObj['price'] = this.createPartyForm.value.price
      inputObj['discontinued'] = this.createPartyForm.value.discontinued
      inputObj['recipes'] = this.itemGroups
      inputObj['totalSuppliesPrice'] = this.totalSuppliesPrice
      inputObj['totalMenuItemsPrice'] = this.totalMenuItemsPrice
      inputObj['totalMenuItemsReturnsPrice'] = this.totalMenuItemsReturnsPrice
      inputObj['actualPrice'] = this.createPartyForm.value.actualPrice
      inputObj['partyClosed'] = this.partyClosed
      inputObj['priceWithReturns'] = this.createPartyForm.value.priceWithReturns
      inputObj['partyTaxDiscount'] = this.partyTaxDiscount

      this.api.createPartyOrder(inputObj).subscribe({
        next: (res) => {
          this.notify.snackBarShowSuccess('Party Saved Successfully');
          this.close();
        },
        error: (err) => {
          console.log(err);
        },
      });
    }
  }

  viewRecipe(element) {
    this.close();
    this.router.navigate(['/dashboard/recipe']);
    let inputObj = {}
    inputObj['tenantId'] = this.user.tenantId
    inputObj['restaurantId'] = this.user.restaurantId
    inputObj['priceTier'] = this.createPartyForm.value.priceTier
    inputObj['eventName'] = this.createPartyForm.value.eventName
    inputObj['eventVenue'] = this.createPartyForm.value.eventVenue
    inputObj['eventCategory'] = this.createPartyForm.value.eventCategory
    inputObj['startDate'] = new Date(this.createPartyForm.value.startDate).toISOString()
    inputObj['endDate'] = new Date(this.createPartyForm.value.endDate).toISOString()
    inputObj['minPax'] = this.createPartyForm.value.minPax
    inputObj['maxPax'] = this.createPartyForm.value.maxPax
    inputObj['partyDiscount'] = this.createPartyForm.value.partyDiscount
    inputObj['cost'] = this.createPartyForm.value.cost
    inputObj['ticketType'] = this.createPartyForm.value.ticketType
    inputObj['discontinued'] = this.createPartyForm.value.discontinued
    inputObj['description'] = this.createPartyForm.value.description
    inputObj['image'] = this.createPartyForm.value.image
    inputObj['recipes'] = this.dataSource.data
    inputObj['partyOrderId'] = this.partyOrderId
    let obj = {
      'element': element,
      'createParty': false,
      'event': inputObj
    }
    this.sharedData.setViewRecipe(obj);
  }

  onDateSelected(date: Date) {
    if (!this.startDate) {
      this.startDate = date;
      this.selected = date;
    } else if (!this.endDate && date > this.startDate) {
      this.endDate = date;
      this.selected = date;
    } else {
      this.startDate = date;
      this.endDate = null;
    }
  }

  checkDuplicatesGroup(event: Event): void {
    const val = (event.target as HTMLInputElement).value.trim();
    if (!val) {
      this.duplicateMenuGroup = false;
      this.emptyGroupName = true;
      return;
    }
    const exist = this.itemGroups.some(
      (group) => group.groupName.toLowerCase() === val.toLowerCase()
    );
    this.duplicateMenuGroup = exist;
    this.emptyGroupName = false;
  }

  readIPConfig(value) {
    this.api.readIPConfig(this.user.tenantId).subscribe({
      next: (res) => {
        if (res?.['success']) {
          const data = res['data'];
          this.logo = data.tenantDetails.logo
          this.opConfigData = res['data']
          if (res['data'].hasOwnProperty('defaultPriceTier')) {
            let resData = Object.keys(this.opConfigData.defaultPriceTier)[0]
            let val = this.user.restaurantAccess.find(val => val.restaurantIdOld == resData)
            this.getDetailedPriceList(value);
          }
        }
      },
      error: (err) => { console.log(err) }
    });
  }

  printOption() {
    const menuItemData = this.itemGroups.map(group => ({
      groupName: group.groupName,
      items: group.items.map(item => ({
        menuItemName: item.menuItemName,
        servingSize: item.servingSize,
        quantity: item.quantity,
        costOfProduction: item.costOfProduction,
        overAllCostOfProduction: this.notify.truncateAndFloor(item.overAllCostOfProduction),
        sellingPrice: item.sellingPrice,
        return: item.return,
        wastage: item.wastage,
        totalPrice: item.totalPrice,
      }))
    }));

    // this.extraSupplies.value.supplyName.toString();
    // this.extraSupplies.value.quantity.toString();
    // this.extraSupplies.value.totalPrice.toString();
    // this.extraSupplies.value.cost.toString();
    // this.extraSupplies.value.description.toString();
    //   for (const key in this.extraSupplies.value) {
    //     if (this.extraSupplies.value.hasOwnProperty(key)) {
    //         this.extraSupplies.value[key] = String(this.extraSupplies.value[key]);
    //     }
    // }

    let supply = this.extraSupplies.value.map(item => {
      return Object.keys(item).reduce((acc, key) => {
        acc[key] = String(item[key]); // Convert each value to string
        return acc;
      }, {});
    });

    let obj = {
      factory_name: this.user.name,
      party_details: {
        'Party Name': this.createPartyForm.value.partyName,
        'Party Code': this.createPartyForm.value.partyCode,
        'Party Creator Name': this.createPartyForm.value.partyCreator,
        'Restaurant': this.createPartyForm.value.restaurantId.split('@')[1],
        // 'Price Tier': this.createPartyForm.value.priceTier.toString(),
        'Phone Number': this.createPartyForm.value.phoneNumber,
        'Address': this.createPartyForm.value.address,
        'email': this.createPartyForm.value.email,
        'StartDate': new Date(this.createPartyForm.value.startDate).toLocaleDateString('en-IN', { timeZone: 'Asia/Kolkata' }),
        'EndDate': new Date(this.createPartyForm.value.endDate).toLocaleDateString('en-IN', { timeZone: 'Asia/Kolkata' }),
        'Venue': this.createPartyForm.value.venue,
        'Minimum Pax': this.createPartyForm.value.minPax.toString(),
        'Maximum Pax': this.createPartyForm.value.maxPax.toString(),
        'Session': this.updatedSessions,
      },
      logo: this.logo,
      menu_table_headers: ['ItemName', 'Serving Size', 'Qty', 'Cost', 'OverAll Cost', 'Selling Price', 'Wastage', 'Return', 'Total Price'],
      menus_data: menuItemData,
      supply_table_headers: ['supplyName', 'quantity', 'totalPrice'],
      supply_data: supply,
      summary: {
        'Extra Supplies Amount': this.getSuppliesTotal('totalPrice').toString(),
        'Menu Item Amount': this.getMenuItemsTotal('totalPriceWithReturns').toString(),
        // 'Return Menu Item Amount': this.getMenuItemsReturnsTotal('totalPriceWithReturns').toString(),
        'Total Price': (this.getSuppliesTotal('totalPrice') + this.getMenuItemsTotal('totalPriceWithReturns')).toString(),
        'Party Discount': this.createPartyForm.value.partyDiscount.toString(),
        'Total Price with return': this.createPartyForm.value.priceWithReturns.toString()
      },
      type: 'Party'
    };
    this.api.printPartyInvoice(obj).subscribe({
      next: (data) => {
        this.api.globalPrintPdf(data.pdf_base64);
      },
      error: (err) => {
        console.error(err);
      }
    });
  }

  openGroup(openGroupDialog) {
    this.dialogRef = this.dialog.open(openGroupDialog, {
      width: '500px',
    });
    this.dialogRef.afterClosed().subscribe(result => {
    });
  }

  closeDialog() {
    this.dialogRef.close();
  }

  createGroup() {
    this.itemGroups.push({
      groupName: this.groupName.value,
      items: []
    });
    this.initializeGroupForms(this.groupName.value);
    this.sharedData.getMenuData.subscribe((obj) => {
      this.menuData = obj
      // this.modifierOptions = this.menuItemsForm.get('itemNames').valueChanges.pipe(startWith(''), map(value => this._filterSearch((value || ''), this.menuData['menu master'] ? this.menuData['menu master'] : [])));
      this.modifierOptions = this.groupForms[this.groupName.value].get('itemNames').valueChanges.pipe(startWith(''), map(value => this._filterSearch((value || ''), this.menuData['menu master'] ? this.menuData['menu master'] : [])));
      this.menuItemBank = this.menuData['menu master'];
      this.menuItemsData.next(this.menuItemBank?.slice());
      this.menuItemFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.FilterSearchMenu(
            this.menuItemBank,
            this.menuItemFilterCtrl,
            this.menuItemsData
          );
        });
    });

    this.isEditing = false
    // this.menuItemsForm.reset();
    this.groupName.setValue('');
    this.closeDialog()
    this.cd.detectChanges();
  }

  deleteGroup(i, name, deleteDialog) {
    this.delName = name
    this.dialogRef = this.dialog.open(deleteDialog, {
      width: '500px',
    });
    this.dialogRef.afterClosed().subscribe(result => {
      if (result === 'ok') {
        this.itemGroups = this.itemGroups.filter(group => group.groupName !== name);
        this.groupHeading = null;
        this.cd.detectChanges();
        this.closeDialog();
      }
    });
  }

  onOk() {
    this.dialogRef.close('ok');
  }

  refreshDataSource(groupName: string) {
    const group = this.itemGroups.find(g => g.groupName === groupName);
    if (group) {
      this.dataSources[groupName].data = group.items; // Refresh data
    }
  }

  // addToGroup(groupName) {
  //   this.groupTaxDiscount = false;
  //   // ---------------- DON'T REMOVE -------------------------
  //   // if (this.menuItemsForm.invalid) {
  //   //   const invalidControls: { [key: string]: any } = {};
  //   //   console.log(this.menuItemsForm);
  //   //   console.log(this.menuItemsForm.controls);
  //   //   Object.keys(this.menuItemsForm.controls).forEach((key) => {
  //   //     const control = this.menuItemsForm.get(key);
  //   //     if (control && control.invalid) {
  //   //       invalidControls[key] = control.value;
  //   //     }
  //   //   });
  //   //   console.log("Invalid form values:", invalidControls);
  //   // } else {
  //   //   console.log("Form is valid" , this.menuItemsForm.value);
  //   // }

  //   let group = []
  //   this.groupHeading = groupName
  //   let groupData = this.itemGroups.find(data => data.groupName === this.groupHeading)
  //   if (groupData) {
  //     let menuItems = this.menuData['menu master'].find(item => item.menuItemName.toLowerCase() === this.groupForms[groupName].value.itemNames.toLowerCase())
  //     menuItems['servingSize'] = this.groupForms[groupName].value.servingSize
  //     menuItems['cost'] = this.groupForms[groupName].value.cost;
  //     menuItems['sellingPrice'] = this.groupForms[groupName].value.sellingPrice
  //     menuItems['quantity'] = this.groupForms[groupName].value.quantity;
  //     menuItems['costOfProduction'] = this.groupForms[groupName].value.costOfProduction
  //     menuItems['overAllCostOfProduction'] = this.groupForms[groupName].value.costOfProduction * this.groupForms[groupName].value.quantity
  //     menuItems['return'] = 0
  //     menuItems['wastage'] = 0
  //     menuItems['cost'] = this.groupForms[groupName].value.cost
  //     menuItems['totalPrice'] = this.groupForms[groupName].value.totalPrice;
  //     menuItems['itemDiscount'] = this.groupForms[groupName].value.itemDiscount;
  //     menuItems['itemTaxDiscount'] = this.itemTaxDiscount;
  //     groupData.items.push(menuItems)
  //     groupData['totalPrice'] = this.getTotalValues(groupData, 'totalPrice');
  //     groupData['totalPriceWithReturns'] = groupData['totalPrice'];
  //     groupData['actualPrice'] = this.getTotalValues(groupData, 'cost');
  //     groupData['groupDiscount'] = 0
  //     groupData['groupCostofProduction'] = this.getTotalValues(groupData, 'overAllCostOfProduction');
  //     groupData['groupTaxDiscount'] = this.groupTaxDiscount
  //     this.initializeDataSources();
  //     this.refreshDataSource(groupName);
  //     this.groupForms[groupName].reset();
  //     this.cd.detectChanges();
  //   }
  //   this.totalPartyPrice();
  //   this.setDraft();
  // }

  addToGroup(groupName) {
    this.groupTaxDiscount = false;

    let group = []
    this.groupHeading = groupName
    let groupData = this.itemGroups.find(data => data.groupName === this.groupHeading)

    if (groupData) {
      let existingDiscount = groupData.groupDiscount || 0; // Preserve existing discount
      let menuItems = this.menuData['menu master'].find(
        item => item.menuItemName.toLowerCase() === this.groupForms[groupName].value.itemNames.toLowerCase()
      );

      menuItems['servingSize'] = this.groupForms[groupName].value.servingSize;
      menuItems['cost'] = this.groupForms[groupName].value.cost;
      menuItems['sellingPrice'] = this.groupForms[groupName].value.sellingPrice;
      menuItems['quantity'] = this.groupForms[groupName].value.quantity;
      menuItems['costOfProduction'] = this.groupForms[groupName].value.costOfProduction;
      menuItems['overAllCostOfProduction'] = this.groupForms[groupName].value.costOfProduction * this.groupForms[groupName].value.quantity;
      menuItems['return'] = 0;
      menuItems['wastage'] = 0;
      menuItems['totalPrice'] = this.groupForms[groupName].value.totalPrice;
      menuItems['itemDiscount'] = this.groupForms[groupName].value.itemDiscount;
      menuItems['itemTaxDiscount'] = this.itemTaxDiscount;

      groupData.items.push(menuItems);
      groupData['totalPrice'] = this.getTotalValues(groupData, 'totalPrice');
      groupData['totalPriceWithReturns'] = groupData['totalPrice'];
      groupData['actualPrice'] = this.getTotalValues(groupData, 'cost');
      groupData['groupCostofProduction'] = this.getTotalValues(groupData, 'overAllCostOfProduction');
      groupData['groupTaxDiscount'] = this.groupTaxDiscount;

      // **Reapply group discount**
      this.reapplyGroupDiscount(groupData, existingDiscount);

      this.initializeDataSources();
      this.refreshDataSource(groupName);
      this.groupForms[groupName].reset();
      this.cd.detectChanges();
    }
    this.totalPartyPrice();
    this.setDraft();
  }


  calItemPrice(group) {
    this.groupForms[group.groupName].get('cost').setValue(this.notify.truncateAndFloor(this.groupForms[group.groupName].value.quantity * this.groupForms[group.groupName].value.sellingPrice))
    this.groupForms[group.groupName].get('totalPrice').setValue(this.groupForms[group.groupName].value.cost)
  }

  totalPartyPrice() {
    let totalPrice = 0;
    let actualPrice = 0;
    this.itemGroups.forEach(group => {
      // group.items.forEach(item => {
      // totalPrice += item.totalPrice;
      totalPrice += group.totalPrice;
      actualPrice += group.actualPrice;
      // });
    });

    this.extraSupplies.controls.forEach(supply => {
      const supplyPrice = supply.get('totalPrice').value || 0;
      totalPrice += supplyPrice;
      actualPrice += supplyPrice;
    });

    this.createPartyForm.get('actualPrice').setValue(this.notify.truncateAndFloor(actualPrice));
    this.createPartyForm.get('price').setValue(this.notify.truncateAndFloor(totalPrice));
    this.tempTotalPrice = this.createPartyForm.get('price').value
    if (this.createPartyForm.value.partyDiscount) {
      this.createPartyForm.get('price').setValue(this.notify.truncateAndFloor(this.createPartyForm.value.price - this.createPartyForm.value.partyDiscount));
    }
    this.cd.detectChanges();
  }

  getTotalReturns() {
    let totalPrice = 0;
    this.itemGroups.forEach(group => {
      totalPrice += group.totalPriceWithReturns;
    });
    console.log(this.itemGroups)
    this.extraSupplies.controls.forEach(supply => {
      const supplyPrice = supply.get('totalPrice').value || 0;
      totalPrice += supplyPrice;
    });
    let discountAmount = this.partyTaxDiscount == true ? (this.createPartyForm.value.partyDiscount * totalPrice) / 100 : this.createPartyForm.value.partyDiscount
    this.createPartyForm.get('priceWithReturns').setValue(this.notify.truncateAndFloor(totalPrice - discountAmount));
    return this.createPartyForm.get('priceWithReturns').value
  }

  addOption() {
    // if (this.partyNameControl.valid) {
    //   if (Object.keys(this.copiedParty).length > 0 && !this.partyNameControl.value) {
    //     this.copiedParty.partyName = ''
    //     let today = new Date();
    //     today.setHours(0, 0, 0, 0);
    //     let startDate = new Date(this.copiedParty.startDate);
    //     startDate.setHours(0, 0, 0, 0);
    //     this.copiedParty.startDate = startDate < today ? today : this.copiedParty.startDate
    //     let groupNames = this.copiedParty.recipes.map(item => item.groupName)
    //     groupNames.forEach(name =>{
    //       this.initializeGroupForms(name);
    //       this.sharedData.getMenuData.subscribe((obj) => {
    //         this.menuData = obj      
    //         this.modifierOptions = this.groupForms[name].get('itemNames').valueChanges.pipe(startWith(''), map(value => this._filterSearch((value || ''), this.menuData['menu master'] ? this.menuData['menu master'] : [])));
    //         this.menuItemBank = this.menuData['menu master'];
    //         this.menuItemsData.next(this.menuItemBank?.slice());
    //         this.menuItemFilterCtrl.valueChanges
    //           .pipe(takeUntil(this._onDestroy))
    //           .subscribe(() => {
    //             this.FilterSearchMenu(
    //               this.menuItemBank,
    //               this.menuItemFilterCtrl,
    //               this.menuItemsData
    //             );
    //           });
    //       });
    //     })
    // this.bindData(this.copiedParty);
    // } else {
    this.createPartyForm.get('partyName').setValue(this.partyNameControl.value);
    let todayDate = new Date()
    let currentTime = todayDate.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
    todayDate.setHours(0, 0, 0, 0);
    this.createPartyForm.patchValue({
      startDate: todayDate,
      endDate: todayDate,
      time: currentTime,
    });
    // }
    this.generatePartyCode()
    this.isDuplicate = false
    // }
  }

  generatePartyCode() {
    this.api.getPartyCode(this.user.tenantId).subscribe({
      next: (res) => {
        res['success'] ? (this.partyCode = res['partyCode']) : this.notify.snackBarShowError('Something Went Wrong!');
        this.createPartyForm.get('partyCode').setValue(this.partyCode);
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  checkItem(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    if (!inputElement) return;
    let inputValue = inputElement.value;

    inputValue = inputValue.replace(/[^A-Za-z0-9 !@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/g, '')
                           .toUpperCase();
    const isDuplicate = this.partyList.some(
      party => party.toUpperCase().replace(/\s+/g, ' ').trim() === inputValue.replace(/\s+/g, ' ').trim()
    );
    this.showDuplicate = isDuplicate;
    inputElement.value = inputValue;
    this.createPartyForm.controls['partyName'].setValue(inputValue, { emitEvent: false });
  }
  

  validatePartyName(event: Event): void {
    const value = (event.target as HTMLInputElement).value.trim();

    if (!value) {
      this.partyNameControl.setErrors({ emptySpaces: true });
      return;
    }

    const hasMinimumCharacters = value.replace(/[^a-zA-Z]/g, '').length >= 3;

    if (!hasMinimumCharacters) {
      this.partyNameControl.setErrors({ invalidFormat: true });
    } else {
      this.partyNameControl.setErrors(null);
    }
  }

  checkItemsGroup() {
    if (this.itemGroups.length === 0) {
      return true;
    }
    return this.itemGroups.some(group => group.items.length === 0);
  }

  cloneParty() {
    let inputObj = {}
    inputObj['tenantId'] = this.user.tenantId
    inputObj['restaurantId'] = this.createPartyForm.value.restaurantId
    inputObj['priceTier'] = this.createPartyForm.value.priceTier
    inputObj['partyName'] = this.createPartyForm.value.partyName
    inputObj['partyCode'] = this.createPartyForm.value.partyCode
    inputObj['partyCreator'] = this.createPartyForm.value.partyCreator
    inputObj['phoneNumber'] = this.createPartyForm.value.phoneNumber
    inputObj['address'] = this.createPartyForm.value.address
    inputObj['email'] = this.createPartyForm.value.email
    inputObj['startDate'] = new Date(this.createPartyForm.value.startDate).toISOString()
    inputObj['endDate'] = new Date(this.createPartyForm.value.endDate).toISOString()
    inputObj['venue'] = this.createPartyForm.value.venue
    inputObj['minPax'] = this.createPartyForm.value.minPax
    inputObj['maxPax'] = this.createPartyForm.value.maxPax
    inputObj['partyDiscount'] = this.createPartyForm.value.partyDiscount
    inputObj['session'] = this.updatedSessions
    inputObj['extraSupplies'] = this.createPartyForm.value.extraSupplies
    inputObj['price'] = this.createPartyForm.value.price
    inputObj['discontinued'] = this.createPartyForm.value.discontinued
    inputObj['recipes'] = this.itemGroups
    inputObj['totalSuppliesPrice'] = this.totalSuppliesPrice
    inputObj['totalMenuItemsPrice'] = this.totalMenuItemsPrice
    inputObj['totalMenuItemsReturnsPrice'] = this.totalMenuItemsReturnsPrice
    inputObj['actualPrice'] = this.createPartyForm.value.actualPrice
    this.sharedData.copyParty(inputObj);
    this.notify.snackBarShowSuccess('Party Cloned Successfully');
  }

  checkCopiedParty() {
    return Object.keys(this.copiedParty).length > 0
  }

  getTotalValues(value, property: string) {
    return value.items.reduce((total, item) => {
      return this.notify.truncateAndFloor(total + (item[property] || 0));
    }, 0);
  }

  setDraft() {
    let inputObj = {}
    let startDate = new Date(this.createPartyForm.value.startDate);
    startDate.setHours(0, 0, 0, 0);
    let endDate = new Date(this.createPartyForm.value.endDate);
    endDate.setHours(0, 0, 0, 0);
    let startDateISO = startDate.toLocaleDateString('en-GB', { timeZone: 'Asia/Kolkata' }).split('/').reverse().join('-') + "T00:00:00";
    let endDateISO = endDate.toLocaleDateString('en-GB', { timeZone: 'Asia/Kolkata' }).split('/').reverse().join('-') + "T00:00:00";

    inputObj['tenantId'] = this.user.tenantId
    inputObj['restaurantId'] = this.createPartyForm.value.restaurantId
    inputObj['priceTier'] = this.createPartyForm.value.priceTier
    inputObj['partyName'] = this.createPartyForm.value.partyName
    inputObj['partyCreator'] = this.createPartyForm.value.partyCreator
    inputObj['partyCode'] = this.createPartyForm.value.partyCode
    inputObj['phoneNumber'] = this.createPartyForm.value.phoneNumber
    inputObj['address'] = this.createPartyForm.value.address
    inputObj['email'] = this.createPartyForm.value.email
    inputObj['startDate'] = startDateISO
    inputObj['endDate'] = endDateISO
    inputObj['venue'] = this.createPartyForm.value.venue
    inputObj['minPax'] = this.createPartyForm.value.minPax
    inputObj['maxPax'] = this.createPartyForm.value.maxPax
    inputObj['partyDiscount'] = this.createPartyForm.value.partyDiscount
    inputObj['session'] = this.updatedSessions
    inputObj['extraSupplies'] = this.createPartyForm.value.extraSupplies
    inputObj['price'] = this.createPartyForm.value.price
    inputObj['recipes'] = this.itemGroups
    inputObj['discontinued'] = 'no'
    inputObj['actualPrice'] = this.createPartyForm.value.actualPrice
    inputObj['priceWithReturns'] = this.createPartyForm.value.priceWithReturns
    inputObj['partyClosed'] = this.partyClosed
    inputObj['partyTaxDiscount'] = this.partyTaxDiscount

    this.api.setPartyDraft(inputObj).subscribe({
      next: (res) => {

      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  scrollLeft() {
    this.scrollContainer.nativeElement.scrollBy({ left: -200, behavior: 'smooth' });
  }

  scrollRight() {
    this.scrollContainer.nativeElement.scrollBy({ left: 200, behavior: 'smooth' });
  }

  showCard(card) {
    this.themeName = card.partyName
    this.itemGroups = [...card.recipes]
  }

  filterParties(event) {
    let filterValue = event.target.value;
    filterValue = filterValue.trim().toLowerCase();
    if (filterValue === "") {
      this.partyGroupData = [...this.tempPartyGroupData];
    } else {
      this.partyGroupData = this.tempPartyGroupData.filter(item =>
        item.partyName.toLowerCase().includes(filterValue)
      );
    }
  }

  getSuppliesTotal(val): number {
    return this.extraSupplies.controls.reduce((total, supply) => {
      const price = supply.get(val)?.value || 0;
      this.totalSuppliesPrice = total + price
      return total + price;
    }, 0);
  }

  getMenuItemsTotal(val) {
    this.totalMenuItemsPrice = this.itemGroups.reduce((acc, group) => acc + group[val], 0);
    // if(val == 'totalPrice'){
    //   console.log(this.totalMenuItemsPrice);
    //   console.log(this.itemGroups);
    // }
    return this.totalMenuItemsPrice
  }

  getMenuItemsReturnsTotal(column) {
    return this.itemGroups.reduce((total, item) => {
      this.totalMenuItemsReturnsPrice = this.notify.truncateAndFloor(total + (item[column] || 0));
      return this.totalMenuItemsReturnsPrice
    }, 0);
  }

  get extraSupplies(): FormArray {
    return this.createPartyForm.get('extraSupplies') as FormArray;
  }

  addSupply() {
    const supplyNameValue = this.supplyName.value;
    const quantityValue = this.quantity.value;
    const priceValue = this.price.value;
    const cost = this.cost.value;
    const description = this.description.value;

    if (supplyNameValue && quantityValue && priceValue) {
      this.extraSupplies.push(this.formBuilder.group({
        supplyName: [supplyNameValue],
        quantity: [quantityValue],
        totalPrice: [priceValue],
        cost: [cost],
        description: [description]
      }));
      this.totalPartyPrice();
      this.setDraft();
      this.clearForm();
    }
  }

  clearForm() {
    this.supplyName.reset();
    this.quantity.reset();
    this.price.reset();
    this.cost.reset();
    this.description.reset();
  }

  deleteSupply(index: number) {
    this.extraSupplies.removeAt(index);
    this.totalPartyPrice();
  }

  itemOptionSelected(value, item) {
    // const currentItemName = this.menuItemsForm.get('itemNames')?.value;
    const currentItemName = this.groupForms[item.groupName].get('itemNames')?.value;
    const currentServingSize = this.groupForms[item.groupName].get('servingSize')?.value;
    const quantity = this.groupForms[item.groupName].get('quantity')?.value;
    const costOfProduction = this.groupForms[item.groupName].get('costOfProduction')?.value;
    const sellingPrice = this.groupForms[item.groupName].get('sellingPrice')?.value;
    const cost = this.groupForms[item.groupName].get('cost')?.value;
    const itemDiscount = this.groupForms[item.groupName].get('itemDiscount')?.value;
    const totalPrice = this.groupForms[item.groupName].get('totalPrice')?.value;
    const actualPrice = this.groupForms[item.groupName].get('actualPrice')?.value;

    this.groupForms[item.groupName].reset({
      itemNames: currentItemName,
      servingSize: currentServingSize,
      quantity: quantity || 1,
      costOfProduction: costOfProduction,
      sellingPrice: sellingPrice,
      cost: cost,
      itemDiscount: itemDiscount,
      totalPrice: totalPrice,
      actualPrice: actualPrice,
    });

    if (!this.isEditing && (item && item.items.length > 0)) {
      let groupData = item.items.filter(item => item.menuItemName.toLowerCase() == value.toLowerCase())
      this.groupServingSize = groupData.length > 0 ? groupData[0].servingSize.split(',') : []
    }
    let data = this.menuData['menu master'].filter(item => item.menuItemName.toLowerCase() == value.toLowerCase())
    this.menuRecipes = this.menuData['menu recipes'].filter(item => item.menuItemCode.toLowerCase() == data[0].menuItemCode.toLowerCase())
    this.menuRecipes.forEach((el) => {
      el['Discontinued'] = ['no', 'NO', 'No', 'N', null, ''].includes(el['Discontinued']) ? 'no' : 'yes'
      // let portion = this.createRecipeForm.get('portion').value
      let requiredItem = this.invItems.find(
        (item) => item.itemCode == el['ingredientCode']
      );
      let conversionCoefficient;
      // requiredItem.hasOwnProperty('portionWeight') ? this.AccessibleUOM = [el['ConsumptionUOM'],"PORTION"] : undefined ;   
      if (requiredItem && el.hasOwnProperty('portionCount') && (!['', null, undefined].includes(el.portionCount))) {
        el['ConsumptionUOM'] = 'PORTION'
        el['portionCount'] = this.notify.truncateAndFloor(el['portionCount'])
        //  el['weightInUse'] = this.notify.truncateAndFloor(el['portionCount'] * (requiredItem['portionWeight']/portion)) ;
        el['InitialWeight'] = this.notify.truncateAndFloor(el['weightInUse'] / this.notify.truncateAndFloor(el['Yield']));
      }
      conversionCoefficient = requiredItem
        ? requiredItem['uom'] == 'NOS'
          ? 1
          : 1000
        : 0;
      let rate = requiredItem
        ? ((requiredItem.hasOwnProperty('packageQty') ? requiredItem['packageQty'] : 1) / conversionCoefficient) * requiredItem['withTaxPrice']
        : 0;
      el['rate'] = this.notify.truncateAndFloor(rate);
      el['defaultUOM'] = el['ConsumptionUOM'];
      el['isSubRecipe'] = (requiredItem && requiredItem.hasOwnProperty('ItemType') && requiredItem['ItemType'] === 'SubRecipe') ? true : false;
      // el['InitialWeight'] = this.notify.truncateAndFloor(el['InitialWeight'] * portion);  
      // el['weightInUse'] = this.notify.truncateAndFloor(el['weightInUse'] * portion) ;
      el['finalRate'] = this.notify.truncateAndFloor(rate * el['InitialWeight']);
      let requiredStatus = this.nameOptionsBank.find(
        (item) => item['itemCode'] === el['ingredientCode']
      );
      el['inventoryStatus'] = requiredStatus
        ? requiredStatus['status']
        : 'discontinued';
    });

    this.menuRecipes.forEach(item => {
      item.Discontinued = item.Discontinued && (item.Discontinued.toLowerCase() === 'yes' || item.Discontinued.toLowerCase() === 'y')
        ? 'yes'
        : 'no';
    });
    this.servingSizeData = data[0].servingSize.split(',')
    this.servingSizeOptions = this.groupForms[item.groupName].get('servingSize').valueChanges.pipe(startWith(''), map(value => this._filterServing((value || ''), this.servingSizeData)));
  }

  protected _filterServing(value: string, input: string[]): string[] {
    let filterValue = value.toLowerCase();
    this.filtered = input.filter(option => option.toLowerCase().includes(filterValue));
    if (this.filtered.length == 0) {
      this.filtered = ['No Item Found'];
    }
    return this.filtered
  }

  servingOptionSelected(value, group) {
    this.menuMasterServingSize = value
    this.menuMasterData = this.menuData['menu master'].filter(item => item.menuItemName.toLowerCase() == this.groupForms[group.groupName].value.itemNames.toLowerCase())
    if (value == 'full-default') {
      this.ratio = 1;
    } else {
      let currentServingSize = this.menuData['servingsize conversion'].find(
        (el) => el['Serving Size'].toLowerCase() === value.toLowerCase()
      );
      this.ratio = currentServingSize ? currentServingSize['Ratio'] / 100 : 1;
      let requiredData = this.priceList.find(
        (el) =>
          el['pluCode'] === this.menuMasterData[0].menuItemCode &&
          el['servingSizeName'].toLowerCase() === value.toLowerCase()
      );
      if (requiredData) {
        this.menuCost = this.notify.truncateAndFloor(requiredData['priceStr'].replace(/,/g, ''));
      } else {
        this.menuCost = 'N/A';
      }
      this.menuCost === 'N/A' ? this.getMenuCost(group) : this.onSelectAll(true, group);
    }
    this.calItemPrice(group);
  }

  onSelectAll(event: any, group) {
    this.sellingPriceArray = [];
    if (event) {
      let currentModifiers = this.menuRecipes.filter(
        (el) =>
          el['isModifier'] === 'Y' || el['isModifier'].toLowerCase() === 'yes'
      );
      if (currentModifiers.length > 0) {
        currentModifiers.forEach((el) => {
          let requiredItem = this.modifiers.find(
            (item) => item['name'] === el['modifierName']
          );
          this.getModifierCost(requiredItem ? requiredItem['id'] : 0, el, group);
        });
      } else {
        this.getMenuCost(group);
      }
    }
  }

  getModifierCost(modifierId, currentModifier, group) {
    let obj = {
      tenantId: this.user.tenantId,
      modifierId: modifierId,
    };
    this.api.getDetailedModifierList(obj).subscribe({
      next: (res) => {
        if (Object.keys(res).length > 0) {
          let currentPriceTier = res['tierPrices'].find(
            (el) =>
              el['tieredPriceGroupID'] ===
              this.sharedData.getDefaultPriceTier()
          ),
            requiredPrice,
            requiredServingSize;
          if (currentPriceTier) {
            if (currentPriceTier.hasOwnProperty('variantPrices')) {
              requiredServingSize = currentPriceTier['variantPrices'].find(
                (el) =>
                  el['servingSizeName'] === this.menuMasterServingSize
              );
              requiredPrice = requiredServingSize
                ? requiredServingSize['priceStr'] === ''
                  ? 0
                  : this.notify.truncateAndFloor(requiredServingSize['priceStr'])
                : undefined;
            }
          } else {
            requiredPrice = 0;
          }
          let obj = {
            modifierName: currentModifier['modifierName'],
            modifierCost: requiredPrice,
            ingredientName: currentModifier['ingredientName'],
          };
          this.sellingPriceArray.push(obj);
        } else {
          this.notify.snackBarShowWarning('Modifiers Cost Not Available');
        }
        this.getMenuCost(group);
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  getDetailedPriceList(value) {
    let obj = {
      tenantId: this.user.tenantId,
      priceId: this.createPartyForm.value.priceTier,
      restaurantId: value,
    };
    // this.opConfigData.defaultPriceTier[value].defaultPriceTier
    this.api.getDetailedPriceList(obj).subscribe({
      next: (res) => {
        if (Array.isArray(res)) {
          this.priceList = res;
        }
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  getMenuCost(group) {
    let requiredData = this.priceList.find(
      (el) => {
        let serving = this.menuMasterServingSize;
        return (
          el['pluCode'] === this.menuMasterData[0].menuItemCode &&
          el['servingSizeName'].toLowerCase() === serving.toLowerCase()
        );
      }
    );
    if (requiredData) {
      this.menuCost = this.notify.truncateAndFloor(requiredData['priceStr'].replace(/,/g, ''));
    } else {
      this.menuCost = 'N/A';
    }
    let modifiersCost = this.sellingPriceArray.reduce((total, item) => {
      return total + this.notify.truncateAndFloor(item['modifierCost']);
    }, 0);
    this.overAllSellingPrice = this.menuCost === 'N/A' ? 'N/A' : this.menuCost + modifiersCost;
    let sellingPrice = this.menuCost === 'N/A' ? 0 : this.notify.truncateAndFloor(this.menuCost);
    let preparationCost = this.getTotal('finalRate');
    this.profitMargin = sellingPrice != 0 ? this.notify.truncateAndFloor((this.overAllSellingPrice - preparationCost)) : 0;
    let profitPercent = this.notify.truncateAndFloor(
      (((this.overAllSellingPrice - preparationCost) / this.overAllSellingPrice) * 100)
    );
    this.profitPercentage = sellingPrice != 0 ? profitPercent : 0;
    this.costPercentage = sellingPrice != 0 ? this.notify.truncateAndFloor(((preparationCost / sellingPrice) * 100)) : 0;
    // this.menuItemsForm.get('costOfProduction').setValue(preparationCost)
    // this.menuItemsForm.get('sellingPrice').setValue(sellingPrice)
    // this.menuItemsForm.get('cost').setValue(0)
    // this.menuItemsForm.get('totalPrice').setValue(0)

    this.groupForms[group.groupName].get('costOfProduction').setValue(preparationCost)
    this.groupForms[group.groupName].get('sellingPrice').setValue(sellingPrice)
    this.groupForms[group.groupName].get('cost').setValue(0)
    this.groupForms[group.groupName].get('totalPrice').setValue(0)
    this.cd.detectChanges();
  }

  calQuantity(event, group) {
    this.groupForms[group.groupName].get('cost').setValue(this.notify.truncateAndFloor(event.target.value * this.groupForms[group.groupName].value.sellingPrice))
    this.groupForms[group.groupName].get('totalPrice').setValue(this.groupForms[group.groupName].value.cost);
  }

  getTotal(key: string) {
    let filteredItems = this.menuRecipes.filter(item => item.Discontinued === 'no');
    let total = filteredItems.reduce((total, item) => {
      return total + this.notify.truncateAndFloor(item[key]);
    }, 0);
    return this.notify.truncateAndFloor(total * this.ratio)
  }

  getPartyTotal() {
    let totalPrice = 0;
    this.itemGroups.forEach(group => {
      totalPrice += group.totalPrice;
    });

    this.extraSupplies.controls.forEach(supply => {
      const supplyPrice = supply.get('totalPrice').value || 0;
      totalPrice += supplyPrice;
    });
    return totalPrice
  }

  selectedRestaurant(value) {
    this.getPriceTires()
    this.selectedPriceTier(null)
  }

  selectedPriceTier(event) {
    this.readIPConfig(this.createPartyForm.value.restaurantId);
    this.getMenuRecipes();
  }

  getMenuRecipes() {
    let obj = {};
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] = this.createPartyForm.value?.restaurantId
    this.api.getInvList(obj).subscribe({
      next: (res) => {
        if (res['success']) {
          this.invItems = res['invList'];
          this.nameOptionsBank = this.invItems.map((item) => ({
            itemName: item.itemName,
            status: item.status,
            itemCode: item.itemCode,
            isSubRecipe: item.hasOwnProperty('ItemType') && item['ItemType'] === 'SubRecipe' ? true : false
          }));
          this.ingredientData = [...new Set(this.nameOptionsBank)];

          //     this.nameOptionsBank = this.invItems.map((item) => ({
          //       itemName: item.itemName,
          //       status: item.status,
          //       itemCode: item.itemCode,
          //       isSubRecipe : item.hasOwnProperty('ItemType') && item['ItemType'] === 'SubRecipe' ? true : false 
          //     }));
          //     this.ingredientData = [...new Set(this.nameOptionsBank)];
          //     this.subRecData = this.ingredientData
          // .filter((item) => item.itemName && item.isSubRecipe === true)
          // .map((item) => item.itemName);
          //     let val = this.ingredientData.map(item => item.itemName)
        }
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  closeParty() {
    this.partyClosed = true;
    this.addWastagePressed = true;
    this.displayedColumns = ['position', 'itemName', 'servingSize', 'quantity', 'costOfProduction', 'overAllCostOfProduction', 'sellingPrice', 'wastage', 'return', 'cost', 'itemsDiscount', 'totalPrice']
  }

  editItem(groupName: string, menuItemName: string, servingSize: string) {
    this.isEditing = true;
    const groupData = this.itemGroups.find(group => group.groupName === groupName);
    const item = groupData?.items.find(item =>
      item.menuItemName === menuItemName && item.servingSize === servingSize
    );
    console.log(item)
    if (item) {
      this.itemTaxDiscount = item.itemTaxDiscount
      this.groupForms[groupName].patchValue({
        itemNames: item.menuItemName,
        servingSize: item.servingSize,
        quantity: item.quantity,
        costOfProduction: item.costOfProduction,
        sellingPrice: item.sellingPrice,
        cost: item.cost,
        itemDiscount: item.itemDiscount,
        totalPrice: item.totalPrice
      });
      this.tempItemDiscount = item.totalPrice;
    }
    this.itemOptionSelected(item.menuItemName, groupData)
  }

  updateItem(groupName) {
    const groupData = this.itemGroups.find(group => group.groupName === groupName);

    if (groupData) {
      const existingDiscount = groupData.groupDiscount || 0; // Preserve discount
      const existingItem = groupData.items.find(
        item => item.menuItemName === this.groupForms[groupName].value.itemNames
      );

      if (existingItem) {
        existingItem.quantity = this.groupForms[groupName].value.quantity;
        existingItem.costOfProduction = this.groupForms[groupName].value.costOfProduction;
        existingItem.sellingPrice = this.groupForms[groupName].value.sellingPrice;
        existingItem.totalPrice = this.groupForms[groupName].value.totalPrice;
        existingItem.cost = this.groupForms[groupName].value.cost;
        existingItem.overAllCostOfProduction = this.groupForms[groupName].value.costOfProduction * this.groupForms[groupName].value.quantity;
        existingItem['itemDiscount'] = this.groupForms[groupName].value.itemDiscount;
        existingItem['itemTaxDiscount'] = this.itemTaxDiscount;

        groupData['totalPrice'] = this.getTotalValues(groupData, 'totalPrice');
        groupData['actualPrice'] = this.getTotalValues(groupData, 'cost');
        groupData['totalPriceWithReturns'] = groupData['totalPrice'];
        groupData['groupCostofProduction'] = this.getTotalValues(groupData, 'overAllCostOfProduction');
        groupData['groupTaxDiscount'] = this.groupTaxDiscount;

        // **Reapply group discount**
        this.reapplyGroupDiscount(groupData, existingDiscount);

        this.initializeDataSources();
        this.refreshDataSource(groupName);
        this.groupForms[groupName].reset();
        this.isEditing = false;
      }
      this.totalPartyPrice();
    }
  }


  deleteItem(groupName: string, itemName: string, servingSize: string) {
    const groupData = this.itemGroups.find(data => data.groupName === groupName);
    if (groupData) {
      const itemIndex = groupData.items.findIndex(item =>
        item.menuItemName.toLowerCase() === itemName.toLowerCase() &&
        item.servingSize === servingSize
      );
      if (itemIndex > -1) {
        groupData.items.splice(itemIndex, 1);
        groupData['totalPrice'] = this.getTotalValues(groupData, 'totalPrice');
        groupData['actualPrice'] = this.getTotalValues(groupData, 'totalPrice');
        groupData['totalPriceWithReturns'] = groupData['totalPrice'];
        this.initializeDataSources();
        this.refreshDataSource(groupName);
        this.cd.detectChanges();
      } else {
        console.warn(`Item '${itemName}' not found in group '${groupName}'`);
      }
      this.totalPartyPrice();
    }
    this.groupForms[groupName].reset();
  }

  calculateReturn(groupData, element, event) {
    let returns = parseFloat(event.target.value) || 0; // Ensure it's a number
    let total = this.notify.truncateAndFloor(element.return * element.sellingPrice);

    let totalReturnValue = 0;

    // Calculate total return amount correctly
    groupData.items.forEach((item) => {
      if (item.return > 0) {
        totalReturnValue += item.return * item.sellingPrice; // Corrected
      }
    });

    // Ensure totalPriceWithReturns is calculated correctly
    groupData['totalPriceWithReturns'] = this.notify.truncateAndFloor(groupData['totalPrice'] - totalReturnValue);
  }



  checkDuplicates(item: any, value: any): boolean {
    const matchedItem = value.items.find(i => i.menuItemName.toLowerCase() === item.menuItemName.toLowerCase());
    console.log("matchedItem", matchedItem)
    if (matchedItem) {
      const servingSizes = item.servingSize.split(',');
      console.log("servingSizes", item.servingSize)
      if (servingSizes.length === 1) {
        return true;
      }
    }
    return false;
  }

  checkDuplicateSupply(event: Event): void {
    const val = (event.target as HTMLInputElement).value.trim();
    if (!val) {
      this.duplicateSupply = false;
      this.emptySupplyName = true;
      return;
    }
    const exist = this.extraSupplies.value.some(
      (supply) => supply.supplyName.toLowerCase() === val.toLowerCase()
    );
    this.duplicateSupply = exist;
    this.emptySupplyName = false;
  }

  calculateWastageQnty(element: any, event): void {
    const inputValue = +event.target.value;
    const totalUsed = inputValue + (element.return || 0);
    if (totalUsed > element.quantity) {
      element.wastage = element.quantity - (element.return || 0);
    } else {
      element.wastage = inputValue;
    }
  }

  calculateReturnQnty(element: any, event): void {
    const inputValue = +event.target.value;
    const totalUsed = inputValue + (element.wastage || 0);
    if (totalUsed > element.quantity) {
      element.return = element.quantity - (element.wastage || 0);
    } else {
      element.return = inputValue;
    }
  }

  restrictToIntegers(event: KeyboardEvent): void {
    if (event.key === '.' || event.key === ',' || event.key === '-' || event.key === 'e') {
      event.preventDefault(); // Prevent decimals, negative numbers, and scientific notation
    }
  }

  restrictSpecialCharacters(event: KeyboardEvent): void {
    const allowedKeys = ['Backspace', 'Tab', 'Enter', 'ArrowLeft', 'ArrowRight', 'Delete'];
    if (!/^[0-9]$/.test(event.key) && !allowedKeys.includes(event.key)) {
      event.preventDefault(); // Allow only numbers
    }
  }

  // Function to enforce max 4-character input
  limitInputLength(event: Event, fieldName: string): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement.value.length > 4) {
      inputElement.value = inputElement.value.slice(0, 4); // Limit input to 4 characters
    }
    this.createPartyForm.controls[fieldName].setValue(inputElement.value, { emitEvent: false });

    this.validatePax(); // Validate maxPax whenever input changes
  }

  // Function to validate that maxPax is greater than minPax
  validatePax(): void {
    const minPax = Number(this.createPartyForm.get('minPax')?.value);
    const maxPax = Number(this.createPartyForm.get('maxPax')?.value);

    // Ensure maxPax is always greater than minPax
    this.isMaxPaxInvalid = maxPax > 0 && minPax > 0 && maxPax <= minPax;

    if (this.isMaxPaxInvalid) {
      this.createPartyForm.controls['maxPax'].setErrors({ incorrect: true });
    } else {
      this.createPartyForm.controls['maxPax'].setErrors(null);
    }
  }


  dynamicFocusFunction(key, group) {
    if (this.groupForms[group.groupName].get(key)) {
      if (this.notify.truncateAndFloor(this.groupForms[group.groupName].get(key).value) === 0) {
        this.groupForms[group.groupName].get(key).setValue(null)
      }
    }
  }

  dynamicFocusOutFunction(key, group) {
    if (this.groupForms[group.groupName].get(key)) {
      if (this.groupForms[group.groupName].get(key).value === null) {
        this.groupForms[group.groupName].get(key).setValue(0)
      }
    }
  }

  focusFunctionModel(value, key) {
    if (value[key] === 0) {
      value[key] = null
    }
  }

  focusOutFunctionModel(value, key) {
    if (value[key] === null) {
      value[key] = 0
    }
  }

  focusFunction(formKey: string) {
    if (this.createPartyForm.get(formKey)) {
      if (this.notify.truncateAndFloor(this.createPartyForm.get(formKey).value) === 0) {
        this.createPartyForm.get(formKey).setValue(null)
      }
    }
  }

  focusOutFunction(formKey: string) {
    if (this.createPartyForm.get(formKey)) {
      if (this.createPartyForm.get(formKey).value === null) {
        this.createPartyForm.get(formKey).setValue(0)
      }
    }
  }

  focusFunctionFromControl(field) {
    this[field].setValue(this[field].value == 0 ? null : this[field].value)
  }

  focusOutFunctionFromControl(field) {
    this[field].setValue(this[field].value == null ? 0 : this[field].value)
  }

  onStartDateChange(): void {
    const startDate = this.createPartyForm.get('startDate')?.value;
    const endDateControl = this.createPartyForm.get('endDate');
    const endDate = endDateControl?.value;
    if (startDate && endDate) {
      this.checkEndDate(startDate, endDate);
    }
  }

  checkEndDate(startDate: string | Date, endDate: string | Date): void {
    if (new Date(endDate) < new Date(startDate)) {
      this.createPartyForm.get('endDate')?.setValue(null);
    }
  }

  calculatePartyDiscount(event) {
    let discount = event && event.target.value ? event.target.value : 0;
    let amount = this.getPartyTotal()
    // if (discount >= this.createPartyForm.value.price || discount == 0 || !discount) {
    //   this.createPartyForm.get('partyDiscount').setValue(0);
    //   this.createPartyForm.get('price').setValue(amount)
    // } else {
    if (this.partyTaxDiscount) {
      if (this.createPartyForm.get('partyDiscount').value < 100) {
        const taxAmount = (this.createPartyForm.value.partyDiscount * amount) / 100;
        this.createPartyForm.get('price').setValue(this.notify.truncateAndFloor(amount - taxAmount))
      } else {
        this.createPartyForm.get('partyDiscount').setValue(0);
        this.createPartyForm.get('price').setValue(amount)
        this.notify.snackBarShowInfo('Enter a discount of less than 100%')
      }
    } else {
      if (discount >= this.createPartyForm.value.price || discount == 0 || !discount) {
        this.createPartyForm.get('partyDiscount').setValue(0);
        this.createPartyForm.get('price').setValue(amount)
      } else {
        this.createPartyForm.get('price').setValue(this.notify.truncateAndFloor(amount - this.createPartyForm.value.partyDiscount))
      }
    }
    // }
    this.setDraft();
  }

  calculateItemDiscount(event, group) {
    let discount = event && event.target.value ? event.target.value : 0;
    if (discount >= this.groupForms[group.groupName].value.cost || discount == 0 || !discount) {
      this.groupForms[group.groupName].get('itemDiscount').setValue(0);
      this.groupForms[group.groupName].get('totalPrice').setValue(this.groupForms[group.groupName].value.cost)
    } else {
      if (this.itemTaxDiscount) {
        if (this.groupForms[group.groupName].get('itemDiscount').value <= 100) {
          const taxAmount = (this.groupForms[group.groupName].value.itemDiscount * this.groupForms[group.groupName].value.cost) / 100;
          this.groupForms[group.groupName].get('totalPrice').setValue(this.notify.truncateAndFloor(this.groupForms[group.groupName].value.cost - taxAmount))
        } else {
          this.groupForms[group.groupName].get('itemDiscount').setValue(0);
        }
      } else {
        this.groupForms[group.groupName].get('totalPrice').setValue(this.notify.truncateAndFloor(this.groupForms[group.groupName].value.cost - this.groupForms[group.groupName].value.itemDiscount))
      }
    }
    this.totalPartyPrice();
  }

  calculateGroupDiscount(event, value) {
    let discount = parseFloat(event && event.target.value ? event.target.value : '0');

    if (isNaN(discount) || discount < 0) {
      discount = 0; // Prevent NaN and negative values
    }

    const groupData = this.itemGroups.find(group => group.groupName === value.groupName);
    if (groupData) {
      let amount = this.getTotalValues(groupData, 'totalPrice');

      if (this.groupTaxDiscount) {
        // Ensure percentage does not exceed 100%
        if (discount > 100) {
          discount = 100;
          event.target.value = '100'; // Reset input field to 100
        }
        const taxAmount = (discount * amount) / 100;
        groupData.totalPrice = this.notify.truncateAndFloor(amount - taxAmount);
      } else {
        // Ensure discount does not exceed total price
        if (discount > amount) {
          discount = amount;
          event.target.value = amount.toString(); // Reset input field to max allowed
        }
        groupData.totalPrice = this.notify.truncateAndFloor(amount - discount);
      }

      // Assign corrected discount value
      value.groupDiscount = discount;
      groupData['groupDiscount'] = discount;
      groupData.totalPriceWithReturns = groupData.totalPrice;
    }

    this.totalPartyPrice();
    this.setDraft();
  }

  getGroupDiscount() {
    return this.itemGroups.reduce((totalDiscount, group) => {
      const groupDiscount = parseInt(group.groupDiscount, 10) || 0; // Parse as integer
      return totalDiscount + groupDiscount;
    }, 0);
  }

  getItemsDiscount() {
    return this.itemGroups.reduce((totalDiscount, group) => {
      const groupItemDiscount = group.items.reduce((itemDiscountTotal, item) => {
        const itemDiscount = parseInt(item.itemDiscount, 10) || 0; // Parse itemDiscount as integer
        return itemDiscountTotal + itemDiscount;
      }, 0);
      return totalDiscount + groupItemDiscount;
    }, 0);
  }

  calculateExtraTotal(event) {
    this.price.setValue(this.notify.truncateAndFloor(this.quantity.value * this.cost.value))
  }

  onToggleChange(event: boolean) {
    this.partyTaxDiscount = event;
    // this.partyTaxDiscount = !this.partyTaxDiscount;
    this.createPartyForm.get('partyDiscount').setValue(0)
    this.createPartyForm.get('partyTaxDiscount').setValue(this.partyTaxDiscount)
    this.calculatePartyDiscount(null);
  }

  onToggleItemDis(event: boolean, group): void {
    this.itemTaxDiscount = event;
    // !this.itemTaxDiscount;
    this.groupForms[group.groupName].get('itemDiscount').setValue(0)
    this.calculateItemDiscount(null, group);
  }

  onToggleGroupDis(event: boolean, value): void {
    this.groupTaxDiscount = event;
    //  !this.groupTaxDiscount;
    value.groupDiscount = 0
    this.calculateGroupDiscount(null, value)
  }

  checkTime() {
    this.timeError = this.updatedSessions.some(item => !item.time);
    return this.timeError;
  }

  onSessionChange(event: any) {
    this.selectedSessions = event.value;
    this.updatedSessions = this.createPartyForm.value.session.map(session => {
      const existingSession = this.updatedSessions.find(time => time.name === session);
      return {
        name: session,
        time: existingSession?.time || ''
      };
    });
    this.checkTime();
  }

  onTimeChange(name, val) {
    this.updatedSessions.find(session => {
      if (session.name.toLowerCase() == name.toLowerCase()) {
        session.time = val
      }
    });
    this.checkTime();
  }

  getSessionTime(sessionName: string): string {
    let session1 = this.existingData && this.existingData.session ? this.existingData.session.find(s => s.name === sessionName) : [];
    return session1 ? session1.time : '';
  }

  addNewSession(inputElement: HTMLInputElement) {
    this.sessions.unshift({
      name: this.newSession,
      time: ''
    })
    inputElement.value = '';
  }

  checkSession() {
    return this.newSession == undefined || this.newSession == '' ? true : false
  }

  checkDuplicateSession(event) {
    this.newSession = event.target.value.toUpperCase();
    this.newSession = this.newSession.trim();
    this.duplicateSession = this.sessions.some(session => session.name.toUpperCase() === this.newSession);
  }

  handleKeyDown(event: KeyboardEvent): void {
    if (event.key === ' ') {
      event.preventDefault();
      event.stopPropagation();
    }
  }

  enforceMaxLength(event: Event, maxLength: number): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement.value.length > maxLength) {
      inputElement.value = inputElement.value.slice(0, maxLength); // Truncate to maxLength
    }
  }

  onPanelSelected(index: number, isExpanded: boolean): void {
    if (isExpanded) {
      // console.log(`Panel with index ${index} is expanded.`);
    } else {
      // console.log(`Panel with index ${index} is collapsed.`);
    }
  }

  initializeGroupForms(groupName) {
    this.groupForms[groupName] = new FormGroup({
      itemNames: new FormControl(''),
      servingSize: new FormControl(''),
      quantity: new FormControl(''),
      costOfProduction: new FormControl(''),
      sellingPrice: new FormControl(''),
      cost: new FormControl(''),
      itemDiscount: new FormControl(''),
      totalPrice: new FormControl('')
    });
  }

  getGroupForm(groupName: string): FormGroup {
    return this.groupForms[groupName];
  }

  getAmountDiscount(value: any, type): number {
    if (!value || !Array.isArray(value.items)) {
      console.error("Invalid input structure");
      return 0;
    }
    const totalDiscount = value.items.reduce((sum: number, item: any) => {
      if (item.itemTaxDiscount === false) {
        return sum + (item.itemDiscount || 0);
      }
      return sum;
    }, 0);
    return totalDiscount;
  }

  getPercentageDiscount(value: any, type): number {
    // if (!value || !Array.isArray(value.items)) {
    //   return 0;
    // }
    const totalDiscount = value.items.reduce((sum: number, item: any) => {
      if (item.itemTaxDiscount === true) {
        const discountAmount = (item.itemDiscount / 100) * (item.cost || 0);
        return sum + discountAmount;
      }
      return sum;
    }, 0);
    return totalDiscount;
  }

  getTotalPartDiscount(form, formValue) {
    if (form.partyTaxDiscount) {
      const discountAmount = (formValue / 100) * (form.price || 0);
      return discountAmount.toFixed(2);
    } else {
      return formValue;
    }
  }

  getSingleGroupDiscount(value) {
    if (value.groupTaxDiscount) {
      let discount = value.groupDiscount
      const groupData = this.itemGroups.find(group => group.groupName === value.groupName);
      let amount = this.getTotalValues(groupData, 'totalPrice')
      var taxAmount = (discount * amount) / 100;
      return taxAmount
    } else {
      return value.groupDiscount;
    }
  }

  // getTotalGroupDiscount(){
  //     let totalDiscount = 0;
  //     this.itemGroups.forEach(group => {
  //       const groupDiscount = parseFloat(group.groupDiscount);
  //       if (group.groupTaxDiscount) {
  //         console.log('+++++++++++++++++++++++++++++++++++++')
  //         const groupData = this.itemGroups.find(group => group.groupTaxDiscount === true);
  //         let amount = this.getTotalValues(groupData, 'totalPrice')
  //         var taxAmount = (groupDiscount * amount) / 100;
  //         totalDiscount = taxAmount;
  //       } else {
  //         console.log('||||||||||||||||||||||||||||||||' , totalDiscount, groupDiscount)
  //         totalDiscount += groupDiscount;
  //       }
  //     });
  //     return totalDiscount;
  // }


  getTotalGroupDiscount() {
    let totalDiscount = 0;

    this.itemGroups.forEach(group => {
      const groupDiscount = parseFloat(group.groupDiscount);

      if (group.groupTaxDiscount) {
        // Calculate tax amount based on total price for the group
        const amount = this.getTotalValues(group, 'totalPrice');
        const taxAmount = (groupDiscount * amount) / 100;
        totalDiscount += taxAmount;
      } else {
        // Add group discount directly
        totalDiscount += groupDiscount;
      }
    });

    return totalDiscount;
  }


  getTotalItemDiscount() {
    let taxDiscountAmount = 0; // Amount for items where itemTaxDiscount == true
    let nonTaxDiscountAmount = 0; // Amount for items where itemTaxDiscount == false

    this.itemGroups.forEach(group => {
      group.items.forEach(item => {
        const itemDiscount = parseFloat(item.itemDiscount);
        const cost = parseFloat(item.cost);

        if (item.itemTaxDiscount) {
          // Calculate percentage-based discount using cost
          taxDiscountAmount += (itemDiscount / 100) * cost;
        } else {
          // Directly sum the itemDiscount as an amount
          nonTaxDiscountAmount += itemDiscount;
        }
      });
    });

    // Sum both discount amounts
    const totalItemDiscount = taxDiscountAmount + nonTaxDiscountAmount;

    // console.log('Tax-based Discount Amount:', taxDiscountAmount);
    // console.log('Non-Tax-based Discount Amount:', nonTaxDiscountAmount);
    // console.log('Total Item Discount:', totalItemDiscount);

    return isNaN(totalItemDiscount) ? 0 : totalItemDiscount;
  }

  getTotalCostPrice() {
    let cost = 0;

    this.itemGroups.forEach(group => {
      group.items.forEach(item => {
        const cop = parseFloat(item.costOfProduction);

        cost += cop;
      });
    });

    const totalItemDiscount = cost;
    console.log(totalItemDiscount)
    return isNaN(totalItemDiscount) ? 0 : totalItemDiscount;
  }



  barChartData(value: any): ChartData {
    let itemDiscounts = this.getAmountDiscount(value, false) + this.getPercentageDiscount(value, true)
    let groupDiscounts = this.getSingleGroupDiscount(value)
    return {
      labels: ['I-Amount', 'Item Disc', 'Group Disc', 'Total Amount', 'Returns'],
      datasets: [
        {
          // label: value.groupName,
          data: [value.actualPrice, itemDiscounts, groupDiscounts, value.totalPrice, value.totalPriceWithReturns],
          backgroundColor: [
            'rgba(255, 99, 132, 0.2)',
            'rgba(54, 162, 235, 0.2)',
            'rgba(255, 206, 86, 0.2)',
            'rgba(75, 192, 192, 0.2)',
            'rgba(153, 102, 255, 0.2)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
  }

  ngAfterViewInit() {
    const chartCanvas = this.chart?.chart?.canvas;
    if (chartCanvas) {
      chartCanvas.height = 160; // Set custom height
      chartCanvas.width = 300;  // Set custom width
    }
  }

  getTotalOverAllCostOfProduction(): number {
    return this.itemGroups.reduce((groupTotal, group) => {
      const itemTotal = group.items.reduce((itemSum, item) => itemSum + (item.overAllCostOfProduction || 0), 0);
      return groupTotal + itemTotal;
    }, 0);
  }

  restrictNumbers(event: KeyboardEvent) {
    const allowedRegex = /^[a-zA-Z0-9\s!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]*$/;
    const key = event.key;

    if (!allowedRegex.test(key)) {
      event.preventDefault();
    }
  }

  getTotalAmount(value: any): number {
    const computedValue = value.totalPrice ===
      (value.totalPrice - value.totalPriceWithReturns) -
      (this.getAmountDiscount(value, false) + this.getPercentageDiscount(value, true))
      ? 0
      : value.totalPriceWithReturns || 0;

    return computedValue < 0 ? 0 : computedValue;
  }

  getSafeValue(value: number): number {
    return value < 0 ? 0 : value;
  }

  getSafeMenuItemsTotal(type: string): number {
    const total = this.getMenuItemsTotal(type);
    return this.getSafeValue(total);
  }

  getSafeTotalItemDiscount(): number {
    const total = this.getTotalItemDiscount();
    return this.getSafeValue(total);
  }

  getSafeTotalGroupDiscount(): number {
    const total = this.getTotalGroupDiscount();
    return this.getSafeValue(total);
  }

  getSafeProfitPercentage(): number {
    const totalReturns = this.getMenuItemsTotal('totalPriceWithReturns');
    const totalCost = this.getTotalOverAllCostOfProduction();

    if (totalCost === 0) return 0; // Avoid division by zero

    const profitPercentage = ((totalReturns - totalCost) / totalCost) * 100;

    return profitPercentage < 0 ? 0 : profitPercentage; // Ensure no negative values
  }

  getSafeSuppliesAndMenuTotal(): number {
    const total =
      this.getSuppliesTotal('totalPrice') + this.getMenuItemsTotal('totalPriceWithReturns');
    return total < 0 ? 0 : total;
  }

  getSafeTotalPartDiscount(): number {
    const discount = this.getTotalPartDiscount(this.createPartyForm.value, this.createPartyForm.value.partyDiscount);
    return discount < 0 ? 0 : discount;
  }

  getSafePriceWithReturns(): number {
    const priceWithReturns = this.createPartyForm.get('priceWithReturns').value;
    return priceWithReturns < 0 ? 0 : priceWithReturns;
  }

  getSafeTotalReturns(): number {
    const totalReturns = this.getTotalReturns();
    return totalReturns < 0 ? 0 : totalReturns;
  }

  reapplyGroupDiscount(groupData, existingDiscount) {
    let amount = this.getTotalValues(groupData, 'totalPrice');
    if (this.groupTaxDiscount) {
      const taxAmount = (existingDiscount * amount) / 100;
      groupData.totalPrice = this.notify.truncateAndFloor(amount - taxAmount);
    } else {
      groupData.totalPrice = this.notify.truncateAndFloor(amount - existingDiscount);
    }
    groupData.groupDiscount = existingDiscount;
    groupData.totalPriceWithReturns = groupData.totalPrice;
  }


}