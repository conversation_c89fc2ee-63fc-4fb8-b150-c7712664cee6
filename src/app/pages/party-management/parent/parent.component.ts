import { ChangeDetectionStrategy, ChangeDetectorRef, Component, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { BackgroundImageCardComponent } from 'src/app/components/background-image-card/background-image-card.component';
import { ShareDataService } from 'src/app/services/share-data.service';
import { MatButtonModule } from '@angular/material/button';
import { InventoryService } from 'src/app/services/inventory.service';
import { AuthService } from 'src/app/services/auth.service';
import { MatDialog, MatDialogRef ,MatDialogModule } from '@angular/material/dialog';
import { BackgroundImageCardHeaderComponent } from 'src/app/components/background-image-card-header/background-image-card-header.component';
import { MatTabsModule } from '@angular/material/tabs';
import { HttpTableComponent } from 'src/app/components/http-table/http-table.component';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { FormBuilder, FormControl, FormGroup, ValidationErrors, Validators, ValidatorFn, AbstractControl, FormArray,} from '@angular/forms';
import { MasterDataService } from 'src/app/services/master-data.service';
import { Observable, ReplaySubject, Subject, first, map, of, startWith, takeUntil,} from 'rxjs';
import { MatTableModule } from '@angular/material/table';

@Component({
  selector: 'app-parent',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatTabsModule,
    NgxSkeletonLoaderModule,
    MatTabsModule,
    MatTableModule,
    HttpTableComponent,
    BackgroundImageCardHeaderComponent,
    BackgroundImageCardComponent,
    MatDialogModule,
    MatIconModule
  ],
  templateUrl: './parent.component.html',
  styleUrls: ['./parent.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ParentComponent {
  isDataReady = false;
  baseData: any;
  public selectedTabIndex: number = 0;
  public tabs: { label: string; page: string; index: number; icon: string }[] =
  [
    { label: 'Party', page: 'Party Order', index: 0, icon: 'menu_book' },
  ];
  page = 'Party Order'
  user: any;
  draftParties: any=[];
  smallDialog = 'smallCustomDialog'
  mediumDialog = 'mediumCustomDialog'
  largeDialog = 'largeCustomDialog'
  activeParties: any = [];
  previousParties: any = [];
  upcomingParties: any = [];
  discontinuedParties: any = [];
  selectedTabPage: string;
  public childTabs: { label: string; page: string; index: number; icon: string }[] =
    [
      { label: 'Active', page:'Party Order', index: 0, icon: 'menu_book' },
      { label: 'UpComing', page: 'Party Order', index: 1, icon: 'archive' },
      { label: 'Completed', page: 'Party Order', index: 2, icon: 'room_service' },
      { label: 'Closed', page: 'Party Order', index: 3, icon: 'room_service' },
      { label: 'Draft', page:'Party Order', index: 4, icon: 'menu_book' },
    ];
  tempBaseData: any;
  draftDeleted : boolean = false
  constructor(
    public formBuilder: FormBuilder,
    private api: InventoryService,
    private auth: AuthService,
    private cd: ChangeDetectorRef,
    private sharedData: ShareDataService,
    private masterDataService: MasterDataService,
    public dialog: MatDialog,

  ) {
    this.user = this.auth.getCurrentUser();
    this.sharedData.getDraftClear.subscribe(obj => {
      if(obj == 'draftParty'){
        this.getPartyDraft();
        this.cd.detectChanges()
      }else if(obj == 'cloneParty'){
        this.getPartyOrder();
        this.cd.detectChanges()
      }
    })
  }

  ngOnInit(): void {
    // Initialize data in sequence to ensure proper loading
    this.getBaseData();
    this.getPartyNames();
    this.getModifiers();

    // Load party data with retry mechanism
    this.loadPartyData();
  }

  // Method to load party data with retry mechanism
  loadPartyData(retryCount = 0, maxRetries = 3): void {
    if (this.selectedTabIndex === 4) {
      this.getPartyDraft();
    } else {
      this.getPartyOrder();
    }

    // Force change detection after a short delay to ensure UI updates
    setTimeout(() => {
      this.cd.detectChanges();

      // If data is still not ready and we haven't exceeded max retries, try again
      if (!this.isDataReady && retryCount < maxRetries) {
        console.log(`Retrying party data load, attempt ${retryCount + 1}`);
        this.loadPartyData(retryCount + 1, maxRetries);
      }
    }, 500);
  }

  tabClick(tab: any) {
    this.selectedTabIndex = tab.index;
    this.selectedTabPage = this.childTabs[tab.index].page;

    // Reset data ready flag to show loading state
    this.isDataReady = false;
    this.cd.detectChanges();

    // Load appropriate data based on selected tab
    if(this.selectedTabIndex == 4){
      this.getPartyDraft();
    } else {
      this.getPartyOrder();
    }

    // Force change detection to ensure tabs render properly
    // Use a slightly longer timeout to ensure API has time to respond
    setTimeout(() => {
      // If data is still not ready, force another check
      if (!this.isDataReady) {
        console.log('Tab data not ready after initial timeout, forcing update');
        this.cd.detectChanges();
      }
    }, 100);
  }

  getPartyDraft(){
    this.isDataReady = false;
    this.cd.detectChanges(); // Ensure loading state is shown

    this.api.getPartyDraft(this.user.tenantId).subscribe({
      next: (res) => {
        if (res['success']) {
          this.baseData = res['data'];
        } else {
          this.baseData = [];
        }
        this.isDataReady = true;
        this.cd.detectChanges();
      },
      error: (err) => {
        console.error('Error fetching party drafts:', err);
        // Set empty data on error to prevent UI from being stuck in loading state
        this.baseData = [];
        this.isDataReady = true;
        this.cd.detectChanges();
      },
      complete: () => {
        // Ensure UI is updated when request completes
        if (!this.isDataReady) {
          this.isDataReady = true;
          this.cd.detectChanges();
        }
      }
    });
  }

  getBaseData() {
    this.baseData = this.sharedData.getBaseData().value;
    let obj = {};
    obj['tenantId'] = this.user.tenantId;
    obj['userEmail'] = this.user.email;
    obj['type'] = 'recipe';
    this.api.getPresentData(obj).pipe(first()).subscribe({
        next: (res) => {
          if (res['success'] && (res['data'].length > 0 || Object.keys(res['data']).length > 0)) {
            let menuData = res['data']
            this.sharedData.setMenuData(menuData);
          } else {
            let menuData = []
            this.sharedData.setMenuData(menuData);
          }
          this.cd.detectChanges();
        },
        error: (err) => {
          console.log(err);
        },
    });
  }

  getPartyOrder(){
    this.isDataReady = false;
    this.cd.detectChanges(); // Ensure loading state is shown

    this.masterDataService.route$.pipe(first()).subscribe(() => {
      this.api.getPartyOrder(this.user.tenantId).subscribe({
        next: (res) => {
          if (res['success']) {
            this.baseData = res['data'];
          } else {
            this.baseData = [];
          }
          this.sharedData.setPartyData(this.baseData);
          this.isDataReady = true;
          this.cd.detectChanges();
        },
        error: (err) => {
          console.error('Error fetching party orders:', err);
          // Set empty data on error to prevent UI from being stuck in loading state
          this.baseData = [];
          this.sharedData.setPartyData(this.baseData);
          this.isDataReady = true;
          this.cd.detectChanges();
        },
        complete: () => {
          // Ensure UI is updated when request completes
          if (!this.isDataReady) {
            this.isDataReady = true;
            this.cd.detectChanges();
          }
        }
      });
    }, error => {
      console.error('Error with master data service:', error);
      this.baseData = [];
      this.isDataReady = true;
      this.cd.detectChanges();
    });
  }

  getPartyNames(){
    this.api.getPartyNames(this.user.tenantId).subscribe({
      next: (res) => {
        this.sharedData.setPartyNames(res['data'].map(name => name.partyName));
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  getModifiers() {
    if (this.sharedData.getModifiers().value.length === 0){
      this.api.getModifiers(this.user.tenantId).subscribe({
        next: (res) => {
          let modifiers = [];
          if (Array.isArray(res)) {
            modifiers = res;
          }
          this.sharedData.setModifiers(modifiers);
        },
        error: (err) => {
          console.log(err);
        },
      });
    }
  }

  getPartyCount(label) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    let count
    if (label === 'Active') {
      count = this.baseData.filter(party => {
        const startDate = new Date(party.startDate);
        const endDate = new Date(party.endDate);
        return (
          party.partyClosed === false &&
          startDate <= today &&
          endDate >= today
        );
      });
    } else if (label === 'UpComing') {
      count = this.baseData.filter(party => {
        const startDate = new Date(party.startDate);
        return (
          party.partyClosed === false &&
          startDate > today
        );
      });
    } else if (label === 'Completed') {
      count = this.baseData.filter(party => {
        const endDate = new Date(party.endDate);
        return (
          party.partyClosed === false &&
          endDate < today
        );
      });
    } else if (label === 'Closed') {
      count = this.baseData.filter(party => party.partyClosed === true);
    }
    return count?.length
  }

}
