<mat-nav-list>
  <div *ngIf="isDataReady">
    <div class="header d-flex justify-content-between align-items-center flex-wrap mx-2">
      <h2><b>More Actions</b></h2>
      <button class="closeAction" (click)="close($event)" matTooltip="close">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div [matTooltip]="!hasModifiedEntry ? 'No items are modified' : (hasPackageError ? 'Resolve all exceptions' : '')" *ngIf = "syncTypeName != 'Recipe'">
      <a mat-list-item class="custom-list-item mat-list-item-hover" [ngClass]="{disabled : hasPackageError || !hasModifiedEntry}"
      [disabled]="excelUploadDone == false || isButtonDisabled || checkSyncAvailable">
        <button (click)="syncInventory(createSyncInventory); isButtonDisabled = true" style="width: 100%;"
        [disabled]="isButtonDisabled">
          <span matListItemTitle><strong>Sync {{syncTypeName}} Management</strong></span>
          <span matLine>Please click here to update the changes to RMS</span>
          <mat-icon style="float: right; transform: scale(1.5);">forward</mat-icon>
        </button>
      </a>
    </div>
    <a mat-list-item class="custom-list-item mat-list-item-hover" (click)="syncHistory()"  *ngIf = "syncTypeName != 'Recipe'">
      <span matListItemTitle> <strong>Sync History</strong></span>
      <span matLine>Click here to view history of previous synchronization</span>
    </a>
    <a mat-list-item class="custom-list-item mat-list-item-hover" [ngClass]="{disabled : !enableExportAndImport}">
      <button (click)="openExcel(createSyncExcel)" [disabled]="!enableExportAndImport" class="w-100">
        <span matListItemTitle> <strong>Export</strong></span>
        <span matLine>Please click here to download the data into an Excel file</span>
      </button>
    </a>
    <a mat-list-item class="custom-list-item mat-list-item-hover" [ngClass]="{disabled : !enableExportAndImport}" *ngIf = "syncTypeName != 'Recipe'">
      <button (click)="checkUpload(uploadSyncExcel)" [disabled]="!enableExportAndImport" class="w-100">
        <strong>Upload Excel</strong>
      </button>
    </a>
    <a mat-list-item class="custom-list-item mat-list-item-hover" *ngIf="syncTypeName !== 'Inventory' && syncTypeName !== 'User'">
      <button (click)="resetData()" class="w-100">
        <strong>Refresh POS Items</strong>
      </button>
    </a>
    <a mat-list-item class="custom-list-item mat-list-item-hover" *ngIf="syncTypeName !== 'Inventory' && syncTypeName !== 'User'">
      <button (click)="export(exportExcel)" class="w-100">
        <strong>Export Menu Mapping</strong>
      </button>
    </a>
    <a mat-list-item class="custom-list-item mat-list-item-hover" *ngIf="syncTypeName !== 'Inventory' && syncTypeName !== 'User'">
      <button (click)="import(importExcel)" class="w-100">
        <strong>Import Menu Mapping</strong>
      </button>
    </a>
  </div>
  <div *ngIf="!isDataReady">
    <ngx-skeleton-loader count="5" animation="pulse" [theme]="{
      'border-radius': '4px',
      'height': '30px',
      'margin-bottom': '8px',
      'width': '19%',
      'margin-right': '1%',
      'display': 'inline-block',
      'opacity': '0.85'
    }"></ngx-skeleton-loader>
  </div>
</mat-nav-list>


<ng-template #createSyncInventory>
  <div class="closeBtn">
    <mat-icon class="closeBtnIcon" matTooltip="close" (click)="closeSyncInventory()">close</mat-icon>
  </div>
  <div class="px-4 pb-4 smallDialog">
    <div class="bottomTitles p-2 my-3 d-flex flex-wrap align-items-center">
      Sync Inventory
    </div>
    <div class="m-4 confromationText">
      Are you sure want to Sync Inventory?
    </div>
    <div class="text-end">
      <button mat-raised-button (click)=closeSyncInventory() class="me-2">
        No
      </button>
      <button mat-raised-button color="accent" (click)=create()>
        Yes
      </button>
    </div>
  </div>
</ng-template>

<ng-template #createSyncExcel>
  <div class="closeBtn">
    <mat-icon class="closeBtnIcon" matTooltip="close" (click)="closeSyncExcel()">close</mat-icon>
  </div>
  <div class="px-4 pb-4 smallDialog">
    <div class="bottomTitles p-2 my-3 d-flex flex-wrap align-items-center">
      Export Data
    </div>
    <div class="m-4 confromationText">
      Are you sure want to export data to excel?
    </div>
    <div class="text-end">
      <button mat-raised-button (click)=closeSyncExcel() class="me-2">
        No
      </button>
      <button mat-raised-button color="accent" (click)=exportToExcel()>
        Yes
      </button>
    </div>
  </div>
</ng-template>

<ng-template #exportExcel>
  <div class="closeBtn">
    <mat-icon class="closeBtnIcon" matTooltip="close" (click)="closeExportExcel()">close</mat-icon>
  </div>
  <div class="px-4 pb-4 smallDialog">
    <div class="bottomTitles p-2 my-3 d-flex flex-wrap align-items-center">
      Export Menu Mapping Data
    </div>
    <div class="m-4 conformationText">
      Are you sure want to export menu mapping data to excel?
    </div>
    <div class="text-end">
      <button mat-raised-button (click)=closeExportExcel() class="me-2">
        No
      </button>
      <button mat-raised-button color="accent" (click)=exportMenuMapping()>
        Yes
      </button>
    </div>
  </div>
</ng-template>

<ng-template #importExcel>
  <div class="closeBtn">
    <mat-icon class="closeBtnIcon" matTooltip="close" (click)="closeImportExcel()">close</mat-icon>
  </div>
  <div class="px-4 pb-4 smallDialog">
    <div class="bottomTitles p-2 my-3 d-flex flex-wrap align-items-center">
      Upload Menu Mapping Data
    </div>
    <div class="m-4 conformationText">
      Are you sure want to upload menu mapping data to excel?
    </div>
    <div class="text-end">
      <button mat-raised-button (click)=closeImportExcel() class="me-2">
        No
      </button>
      <input type="file" #fileInput (change)="changeFileListener($event)" accept=".xlsx, .xls" style="display: none;" />
      <button mat-raised-button color="accent" (click)="fileInput.click()">
        Yes
      </button>
    </div>
  </div>
</ng-template>

<ng-template #uploadSyncExcel>
  <div class="closeBtn">
    <mat-icon class="closeBtnIcon" matTooltip="close" (click)="closeUploadExcel()">close</mat-icon>
  </div>
  <div class="px-4 pb-4 smallDialog">
    <div class="bottomTitles p-2 my-3 d-flex flex-wrap align-items-center">
      Upload Excel
    </div>
    <div class="m-4 confromationText">
      Are you sure want to upload excel?
    </div>
    <div class="text-end">
      <button mat-raised-button (click)="closeUploadExcel()" class="me-2">
        No
      </button>
      <input type="file" #fileInput (change)="fileChangeListener($event)" accept=".xlsx, .xls" style="display: none;" />
      <button mat-raised-button color="accent" (click)="fileInput.click()">
        Yes
      </button>
    </div>
  </div>
</ng-template>

<ng-template #openExcelError>
  <div class="closeBtn">
    <mat-icon class="closeBtnIcon" matTooltip="close" (click)="closeDialog()">close</mat-icon>
  </div>
  <div class="px-4 pb-4 smallDialog errorDialog">
    <div class="bottomTitles p-2 my-3 d-flex flex-wrap align-items-center">
      Excel Upload Error!
    </div>

    <div *ngFor="let data of errorData; let i = index;">
      <div class="m-2" style="font-size: larger;">
        <b class="d-flex align-items-center">
         {{data?.sheetName | titlecase}} Sheet 
         <mat-icon class="checkIcon errorCheckIcon" *ngIf="data.missingColumns.length === 0">check_circle</mat-icon>
         <mat-icon class="cancelIcon errorCheckIcon" *ngIf="data.missingColumns.length > 0">cancel</mat-icon> 
        </b> 
       </div>
       <div>
          <div *ngFor="let value of data.missingColumns; let i = index;" class="errorData">
              {{i + 1 }} - {{value}} *
          </div>
       </div>
    </div>

    <div class="bottomTitles errormsg p-2 my-3 d-flex flex-wrap align-items-center">
      * There are missing columns in your uploaded sheet
    </div>
  </div>
</ng-template>