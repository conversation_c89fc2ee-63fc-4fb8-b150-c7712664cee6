<div class="closeBtn" *ngIf="isDuplicate == true">
  <mat-icon (click)="close()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
</div>
<div class="registration-form m-3 py-2 px-3">
  <div *ngIf="isDuplicate" class="mt-3 smallDialog">
    <div class="text-center p-2 my-2 bottomTitles">
      Vendor Form
    </div>
    <div class="col-md-12">
      <mat-form-field appearance="outline">
        <mat-label>Search Vendor ..</mat-label>
        <input matInput placeholder="Vendor Name" aria-label="Vendor" [matAutocomplete]="auto1"
          (keyup)="checkItem($event)" [formControl]="itemNameControl" (keyup.enter)="addOption('vendors')"
          oninput="this.value = this.value.toUpperCase()">
        <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelected('vendors', $event.option)">
          <mat-option *ngFor="let item of vendorOptions | async" [value]="item">
            <span>{{ item }}</span>
          </mat-option>
        </mat-autocomplete>
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
      <div class="text-end">
        <button (click)="addOption('vendors')" mat-raised-button color="accent">
          <div *ngIf="loadBtn" class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <mat-icon *ngIf="!updateBtnActive">library_add</mat-icon>
          <mat-icon *ngIf="updateBtnActive">update</mat-icon>
          <span *ngIf="updateBtnActive">Update</span>
          <span *ngIf="!updateBtnActive">Add</span>
        </button>
      </div>
    </div>
  </div>
  <div class="mb-2 topCreateAndUpdateBtn"  style="float: right; padding-top: 1rem;">

    <button *ngIf="!isUpdateActive && !isDuplicate" style="margin-right: 5px;" (click)="submit()" mat-raised-button color="accent"
      matTooltip="create"  [disabled]="isCreateButtonDisabled">
      <div *ngIf="loadSpinnerForApi" class="spinner-border" role="status">
        <span class="sr-only">Loading...</span>
      </div> <mat-icon *ngIf="!loadSpinnerForApi">add_circle</mat-icon> Create
    </button>
    <button *ngIf="isUpdateActive && !isDuplicate" style="margin-right: 5px;" (click)="update()" mat-raised-button color="accent"
      matTooltip="update" [disabled]="loadVendorBtn || isUpdateButtonDisabled">
      <div *ngIf="loadSpinnerForApi" class="spinner-border" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      <!-- <mat-icon *ngIf="!loadSpinnerForApi">update</mat-icon> -->
      Update
    </button>
    <button *ngIf="isDuplicate == false" mat-raised-button color="warn" style="margin-right: 5px;" (click)="close()" matTooltip="Close">
      <mat-icon>close</mat-icon>
      Close
    </button>
  </div>

  <div class="my-2 p-3 bottomTitles" *ngIf="!isDuplicate">
    Vendor Form
  </div>

  <form class="my-3" [formGroup]="registrationForm" *ngIf="!isDuplicate">
    <div class="row">

      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>Vendor</mat-label>
          <input formControlName="vendorName" type="text" matInput placeholder="vendor"
            oninput="this.value = this.value.toUpperCase()" (keyup)="checkVendorName($event)">
        </mat-form-field>
        <mat-error class="formError"
            *ngIf="(!registrationForm.get('vendorName').valid && registrationForm.get('vendorName').dirty) && !registrationForm.get('vendorName').hasError('vendorItemExists')">
            * Invalid limit(1 min & 100 max)
          </mat-error>
          <mat-error class="formError"
            *ngIf="registrationForm.get('vendorName').hasError('vendorItemExists')">
            * Vendor already exists.
          </mat-error>
      </div>

      <div class="col-md-6">
        <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
          <mat-label>Vendor Id</mat-label>
          <input formControlName="vendorTenantId" matInput placeholder="vendorTenantId" autocomplete="off"
            [readonly]="isReadOnly">
        </mat-form-field>
      </div>

      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>Contact Name</mat-label>
          <input formControlName="contactName" type="text" matInput placeholder="Contact Name" autocomplete="off"
            oninput="this.value = this.value.toUpperCase()">
          <mat-icon matSuffix>person</mat-icon>
        </mat-form-field>
      </div>

      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>Contact No</mat-label>
          <input formControlName="contactNo" type="number" matInput placeholder="contact No" autocomplete="off"
            maxlength="10">
          <mat-icon matSuffix>phone</mat-icon>
        </mat-form-field>
        <!-- <mat-error class="formError"
          *ngIf="!registrationForm.get('contactNo').valid && registrationForm.get('contactNo').dirty">
          * Invalid Mobile No.
        </mat-error> -->
      </div>

      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>Address</mat-label>
          <textarea formControlName="address" matInput placeholder="address" autocomplete="off" ></textarea>
          <mat-icon matSuffix>location_on</mat-icon>
        </mat-form-field>
      </div>

      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-chip-grid #chipList formArrayName="email">
            <mat-chip-row selected *ngFor="let item of registrationForm.value.email; let i=index" [selectable]="true"
              [removable]="removable" (removed)="removeEmail(item)" required name="chips">
              {{item}}
              <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
            </mat-chip-row>
            <input #chipInput placeholder="Add emails" [matChipInputFor]="chipList"
              [matChipInputSeparatorKeyCodes]="separatorKeysCodes " (matChipInputTokenEnd)="add($event) " />
          </mat-chip-grid>
        </mat-form-field>
      </div>

      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>CIN No</mat-label>
          <input formControlName="cinNo" matInput placeholder="eg : U01100DL2021PTC375975" autocomplete="off"
            oninput="this.value = this.value.toUpperCase()">
        </mat-form-field>
        <mat-error class="formError"
          *ngIf="!registrationForm.get('cinNo').valid && registrationForm.get('cinNo').dirty">
          * Invalid CIN No.
        </mat-error>
      </div>

      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>GST No</mat-label>
          <input formControlName="gstNo" matInput placeholder="eg : 22AAAAA0000A1Z5" autocomplete="off"
            oninput="this.value = this.value.toUpperCase()">
        </mat-form-field>
        <mat-error class="formError"
          *ngIf="!registrationForm.get('gstNo').valid && registrationForm.get('gstNo').dirty">
          * Invalid GST No.
        </mat-error>
      </div>

      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>PAN No</mat-label>
          <input formControlName="panNo" matInput placeholder="eg : **********" autocomplete="off"
            oninput="this.value = this.value.toUpperCase()">
        </mat-form-field>
        <mat-error class="formError"
          *ngIf="!registrationForm.get('panNo').valid && registrationForm.get('panNo').dirty">
          * Invalid PAN No.
        </mat-error>
      </div>

      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>TIN No</mat-label>
          <input formControlName="tinNo" matInput placeholder="tin No" autocomplete="off"
            oninput="this.value = this.value.toUpperCase()">
        </mat-form-field>
        <mat-error class="formError"
          *ngIf="!registrationForm.get('tinNo').valid && registrationForm.get('tinNo').dirty">
          * Invalid TIN No.
        </mat-error>
      </div>

      <!-- <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>Po Terms</mat-label>
          <input formControlName="poTerms" matInput placeholder="Po Terms" maxlength="100" autocomplete="off">
          <mat-icon matSuffix> short_text</mat-icon>
        </mat-form-field>
      </div> -->

      <div class="col-md-6">
        <mat-form-field appearance="outline">
          <mat-label>Po Terms</mat-label>
          <!-- <textarea formControlName="poTerms" matInput placeholder="Po Terms" maxlength="150" autocomplete="off"></textarea> -->
          <textarea formControlName="poTerms" matInput placeholder="Po Terms" autocomplete="off"
           #textInput (keydown)="onKeyDown($event)"></textarea>
        </mat-form-field>
      </div>

      <div *ngIf="isUpdateActive">
        <div class="col">
          <label>Do you want to discontinue?</label>
          <mat-radio-group formControlName="Discontinued" aria-labelledby="example-radio-group-label">
            <mat-radio-button value="yes">Yes</mat-radio-button>
            <mat-radio-button value="no">No</mat-radio-button>
          </mat-radio-group>
        </div>
      </div>
    </div>
  </form>
</div>