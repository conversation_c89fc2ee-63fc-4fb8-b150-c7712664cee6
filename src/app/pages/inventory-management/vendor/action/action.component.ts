import { CUSTOM_ELEMENTS_SCHEMA, inject, ChangeDetectionStrategy, Component, ElementRef, HostListener, Inject, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Vendor } from 'src/app/models/user.model';
import { InventoryService } from 'src/app/services/inventory.service';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSliderModule } from '@angular/material/slider';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { Observable, first, map, startWith } from 'rxjs';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { ShareDataService } from 'src/app/services/share-data.service';
import { ActionComponent as ActionComponentVendor } from '../../vendor/action/action.component';
import { HttpClientModule } from '@angular/common/http';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { COMMA, ENTER, SPACE, TAB } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';
import { MatChipsModule } from '@angular/material/chips';
import { LiveAnnouncer } from '@angular/cdk/a11y';
import { NotificationService } from 'src/app/services/notification.service';
import { AuthService } from 'src/app/services/auth.service';
import { MasterDataService } from 'src/app/services/master-data.service';
import { TooltipPosition, MatTooltipModule } from '@angular/material/tooltip';
export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'MM-DD-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-action',
  standalone: true,
  imports: [
    CommonModule,
    MatTooltipModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatInputModule,
    MatSliderModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSelectModule,
    MatRadioModule,
    MatAutocompleteModule,
    MatDialogModule, HttpClientModule,
    MatChipsModule,
  ],
  providers: [{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
  { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }],
  templateUrl: './action.component.html',
  styleUrls: ['./action.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ActionComponent implements OnInit {
  oldData: any;
  itemsToUpdate: any[] = [];
  pack: any[] = [];
  @HostListener('window:keyup.esc') onKeyUp() {
    this.dialogRef.close();
  }
  @ViewChild('chipInput') chipInput: ElementRef;
  public separatorKeysCodes = [ENTER, COMMA, SPACE, TAB];
  public emailList = [];
  removable = true;
  question = 'Would you like to add "';
  itemNameControl = new FormControl('');
  vendorOptions: Observable<string[]>;
  registrationForm!: FormGroup;
  isUpdateActive: boolean = false;
  isDuplicate: boolean = true;
  baseData: any;
  user: any;
  isReadOnly: boolean = true;
  loadBtn: boolean = false;
  loadVendorBtn: boolean = true;
  formControl = new FormControl(['angular']);
  announcer = inject(LiveAnnouncer);
  updateBtnActive: boolean = false;
  loadSpinnerForApi: boolean = false;
  isCreateButtonDisabled = false;
  isUpdateButtonDisabled = false;
  @ViewChild('textInput') textInputRef!: ElementRef<HTMLTextAreaElement>;
  constructor(
    private fb: FormBuilder,
    private api: InventoryService,
    private router: Router,
    private masterDataService: MasterDataService,
    private dialogRef: MatDialogRef<ActionComponent>,
    public dialog: MatDialog,
    private auth: AuthService,
    private sharedData: ShareDataService,
    private notify: NotificationService,
    private cd: ChangeDetectorRef,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
  ) {
    this.user = this.auth.getCurrentUser();

    this.registrationForm = this.fb.group({
      vendorName: new FormControl<string>('', [Validators.required, Validators.maxLength(100)]),
      vendorTenantId: new FormControl<string>('', Validators.required),
      contactName: new FormControl<string>('', [Validators.maxLength(100)]),
      contactNo: new FormControl<number>(null),  //, [this.validatePhoneNumber.bind(this))
      address: new FormControl<string>(''),
      email: this.fb.array([]),
      cinNo: new FormControl<string>('', [Validators.maxLength(21), this.validateAlphaNumeric.bind(this)]),
      gstNo: new FormControl<string>('', [Validators.maxLength(15), this.validateAlphaNumeric.bind(this)]),
      panNo: new FormControl<string>('', [Validators.maxLength(10), this.validateAlphaNumeric.bind(this)]),
      tinNo: new FormControl<string>(''),
      Discontinued: new FormControl<string>('no', Validators.required),
      poTerms: new FormControl<string>(''),
      row_uuid: new FormControl<string>(''),
    }) as FormGroup;
    // , [this.validateArrayNotEmpty]
    // DON'T REMOVE ---------------------------------------
    // email: ['', [Validators.required, Validators.pattern('[a-zA-Z0-9.-_]{1,}@[a-zA-Z.-]{2,}[.]{1}[a-zA-Z]{2,}')]],
    // cinNo: ['', [ Validators.pattern('[LUT][0-9]{5}[A-Z]{2}[0-9]{4}[PLCT]{3}[0-9]{6}')]],
    // gstNo: ['', [ Validators.pattern(/^\d{2}[A-Z]{5}\d{4}[A-Z]{1}\d[Z]{1}[A-Z\d]{1}$/i)]],
    // panNo: ['', [ Validators.pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/)]],
    // tinNo: ['', [ Validators.pattern(/^[0-9A-Z]{8,20}$/)]],

    this.isDuplicate = this.dialogData.key;
    this.sharedData.getItemNames.pipe(first()).subscribe(obj => {
      this.getBaseData();
      let vendor = obj.vendorObject.map(item => item.vendorName)
      this.vendorOptions = this.itemNameControl.valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), vendor)));
      if (this.dialogData.key == false) {
        this.isUpdateActive = true;
        this.setValuesForForm(this.dialogData.elements);
      }
    });
  }

  ngOnInit(): void {
  }

  validateAlphaNumeric(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    const alphaNumericRegex = /^[a-zA-Z0-9]*$/;
    if (value && !alphaNumericRegex.test(value)) {
      return { alphanumeric: true };
    }
    return null;
  }

  getBaseData() {
    this.baseData = this.sharedData.getBaseData().value;
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['userEmail'] = this.user.email
    obj['type'] = 'inventory'
    obj['specific'] = 'vendors'
    this.api.getPresentData(obj).pipe(first()).subscribe({
      next: (res) => {
        if (res['success']) {
          this.baseData['vendors'] = res['data'][0] ?? res['data']['vendors'];
          this.cd.detectChanges();
        }
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

  submit() {
    this.isCreateButtonDisabled = true;
    this.loadSpinnerForApi = true;
    if (this.registrationForm.invalid) {
      this.registrationForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields')
      this.loadSpinnerForApi = false;
      this.isCreateButtonDisabled = false;
      this.cd.detectChanges();
    } else {
      if (Object.keys(this.baseData).length > 0) {
        let updatedData = this.convertVendorKeys();
        updatedData['modified'] = "yes";
        // const poTermsValue = this.registrationForm.get('poTerms')?.value;
        // const formattedValue = poTermsValue.replace(/\n/g, '\\n');
        updatedData['POTerms'] = this.registrationForm.value.poTerms
        let tempObj = {}
        tempObj['vendors'] = this.baseData['vendors']
        tempObj['vendors'].unshift(updatedData);
        tempObj['vendors'] = tempObj['vendors'].filter(item => item.modified === "yes");
        let obj = {}
        obj['tenantId'] = this.user.tenantId
        obj['userEmail'] = this.user.email
        obj['data'] = tempObj
        obj['type'] = 'inventory'
        this.api.updateData(obj).pipe(first()).subscribe({
          next: (res) => {
            if (res['success']) {
              this.notify.snackBarShowSuccess('Vendor added successfully!');
            } else {
              this.notify.snackBarShowError('Something went wrong!');
            }
            this.loadSpinnerForApi = false;
            this.cd.detectChanges();
            this.close();
          },
          error: (err) => {
            console.log(err);
          }
        });
      } else {
        this.loadSpinnerForApi = false;
        this.isCreateButtonDisabled = false;
        this.cd.detectChanges();
        this.notify.snackBarShowError('Something went wrong!')
      }
    }

  }

  fillFormToUpdate(user: Vendor) {
    this.registrationForm.setValue({
      firstName: user.firstName,
      lastName: user.lastName,
      mobile: user.mobile,
      weight: user.weight,
      height: user.height,
      bmi: user.bmi,
      bmiResult: user.bmiResult,
      gender: user.gender,
      requireTrainer: user.requireTrainer,
      package: user.package,
      important: user.important,
      haveGymBefore: user.haveGymBefore,
      enquiryDate: user.enquiryDate,
      row_uuid: user.row_uuid
    })
  }

  update() {
    this.isUpdateButtonDisabled = true;   
    this.loadSpinnerForApi = true;    
    if (this.registrationForm.invalid) {
      this.registrationForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields')
      this.loadSpinnerForApi = false;
      this.isUpdateButtonDisabled = false;   
      this.cd.detectChanges();
    } else {
      if (Object.keys(this.baseData).length > 0) {
        let updatedData = this.convertVendorKeys();
        let tempObj = {}
        if (this.oldData.vendorName.toLowerCase() != updatedData['vendorName'].toLowerCase()) { 
          this.getInvAndPack(this.oldData.vendorName , updatedData['vendorName']);
        }else if (updatedData['Discontinued'] === 'yes'){
          this.discontinuedVendors(updatedData['vendorName']);          
        }
        if(this.pack.length > 0){
          tempObj['inventory master'] = this.itemsToUpdate
          tempObj['packagingmasters'] = this.pack
        }
        updatedData['modified'] = "yes";
        // const poTermsValue = this.registrationForm.get('poTerms')?.value;
        // const formattedValue = poTermsValue.replace(/\n/g, '\\n');
        updatedData['POTerms'] = this.registrationForm.value.poTerms
        tempObj['vendors'] = this.baseData['vendors']
        let requiredVendor = tempObj['vendors'].find((el) => el.vendorTenantId == updatedData['vendorTenantId'])
        let index = tempObj['vendors'].indexOf(requiredVendor)
        tempObj['vendors'][index] = updatedData;
        tempObj['vendors'] = tempObj['vendors'].filter(item => item.modified === "yes");
        let obj = {}
        obj['tenantId'] = this.user.tenantId
        obj['userEmail'] = this.user.email
        obj['data'] = tempObj
        obj['type'] = 'inventory'
        this.api.updateData(obj).pipe(first()).subscribe({
          next: (res) => {
            if (res['success']) {
              this.notify.snackBarShowSuccess('Vendor updated successfully')
            }
          },
          error: (err) => {
            console.log(err);
          }
        });
        this.loadSpinnerForApi = false;
        this.cd.detectChanges();
      } else {
        this.loadSpinnerForApi = false;
        this.isUpdateButtonDisabled = false;   
        this.cd.detectChanges();
        this.notify.snackBarShowError('Something went wrong!')
      }
    }
  }

  getInvAndPack(oldVendorName , newVendorName){
    this.itemsToUpdate = this.baseData['inventory master'].filter(item => item.vendor.includes(oldVendorName));
    this.itemsToUpdate.forEach(item => {
      item.vendor = item.vendor.replace(this.oldData.vendorName, newVendorName);
      item.modified = 'yes';
    });
    this.itemsToUpdate.forEach(invItem => {
      const itemCode = invItem.itemCode;
      const packData = this.baseData['packagingmasters'].find(item => item.InventoryCode === itemCode);
      if (packData) {
        packData.modified = "yes"
        this.pack.push(packData);
      }
    });
  }

  discontinuedVendors(vendor){
    this.itemsToUpdate = this.baseData['inventory master'].map(item => {
      if (!item.vendor || item.vendor.trim() === '') {
        return item;
      }
      const vendors = item.vendor.split(',').map(v => v.trim());
      const updatedVendors = vendors.filter(v => v !== vendor);
      item.vendor = updatedVendors.join(',');
      if (vendors.includes(vendor)) {
        item.modified = 'yes';
      }
      return item;
    });
    this.itemsToUpdate = this.itemsToUpdate.filter(item => item.modified === 'yes');
      this.itemsToUpdate.forEach(invItem => {
        const itemCode = invItem.itemCode;
        const packData = this.baseData['packagingmasters'].find(item => item.InventoryCode === itemCode);
        if (packData) {
          packData.modified = "yes"
          this.pack.push(packData);
        }
    });
  }

  close() {
    this.masterDataService.setNavigation('vendorList');
    this.router.navigate(['/dashboard/home']);
    this.dialog.closeAll()
  }

  _filter(value: string, inputList: any): string[] {
    const filterValue = value.toLowerCase();
    let filtered = inputList.filter(option => option.toLowerCase().includes(filterValue));
    if (filtered.length == 0) {
      filtered = [this.question + value + '"'];
    }
    return filtered
  }

  checkItem(event) {
    let invItem = this.sharedData.getDataForFillTheForm(event.target.value, 'vendors')
    if (invItem) {
      this.updateBtnActive = true;
    } else {
      this.updateBtnActive = false;
    }
  }


  optionSelected(type, option) {
    let invItem = this.sharedData.getDataForFillTheForm(option.value, 'vendors')
    if (invItem) {
      this.updateBtnActive = true;
    } else {
      this.updateBtnActive = false;
    }
    if (option.value.indexOf(this.question) === 0) {
      this.addOption(type);
    }
  }

  findId(): any {
    let numericIds = this.baseData.vendors.map(item => {
      let numericPart = item['vendorTenantId'].match(/\d+/);
      return numericPart ? parseInt(numericPart[0]) : 0;
    });
    let highestCode = Math.max(...numericIds);
    let nextId = highestCode + 1;
    return 'V' + nextId;
  }

  addOption(type: string) {
    this.loadBtn = true;
    if (type == "package") {
      this.itemNameControl.reset()
      this.openDialog(ActionComponentVendor);
    } else if (type == "vendors") {
      let vendorItem = this.sharedData.getDataForFillTheForm(this.itemNameControl.value, 'vendors')
      if (vendorItem) {
        this.isUpdateActive = true;
        this.setValuesForForm(vendorItem);
      } else {
        // let code = this.findId();
        this.generateCode('vendorCode');
      }
      this.registrationForm.controls['vendorName'].patchValue(this.removePromptFromOption(this.itemNameControl.value));
      this.itemNameControl.reset();
      this.isDuplicate = false
    }
    this.loadBtn = false;
  }

  generateCode(code){
    let obj = {}
    let data 
    obj['tenantId'] = this.user.tenantId
    obj['code'] = code
    this.api.getCode(obj).pipe(first()).subscribe({
      next: async (res) => {
        if (res['success']) {
          data =  res['data']
          this.registrationForm.get('vendorTenantId').setValue(data)
        }
      },
    })
  }

  setValuesForForm(vendorItem) {
    this.oldData = vendorItem
    this.registrationForm.patchValue({
      vendorName: vendorItem['vendorName'],
      vendorTenantId: vendorItem['vendorTenantId'],
      contactName: vendorItem['contactName'],
      contactNo: vendorItem['contactNo'],
      address: vendorItem['address'],
      cinNo: vendorItem['cinNo'],
      gstNo: vendorItem['gstNo'],
      panNo: vendorItem['panNo'],
      tinNo: vendorItem['tinNo'],
      // poTerms: vendorItem['poTerms'],
      Discontinued: ['no','NO' ,'No', 'N', null,''].includes(vendorItem['Discontinued']) ? 'no' : 'yes',
      row_uuid: vendorItem['row_uuid']
    });
    
    let poTerms = vendorItem['poTerms'] ? vendorItem['poTerms'] : vendorItem['POTerms']
    if (poTerms) {
      poTerms = poTerms.replace(/(\d\.\s.*?)(?=\d\.)/g, '$1\n');
      this.registrationForm.patchValue({        
        poTerms: poTerms,
      });
    }
    var email: any
    if (vendorItem.email) {
      if (Array.isArray(vendorItem.email) && typeof vendorItem.email === 'string') {
        vendorItem.email = JSON.parse(vendorItem.email);
        email = vendorItem.email;
      } else if (typeof vendorItem.email === 'string') {
        vendorItem.email = vendorItem.email.split(',');
        email = vendorItem.email;
      }
      // let emailValues = []
      // if (Array.isArray(email)) {
      //   emailValues = email.map(item => item);
      // }else{
      //   emailValues = [email];
      // }
      const emailsControl = this.registrationForm.get('email') as FormArray;
      emailsControl.clear();
      email.forEach(value => emailsControl.push(this.fb.control(value)));
    }
    this.loadVendorBtn =  false;
  }

  validatePhoneNumber(control) {
    const phoneNumberPattern = /^\d{10}$/;
    if (control.value && !phoneNumberPattern.test(control.value)) {
      return { invalidPhoneNumber: true };
    }
    return null;
  }

  openDialog(component) {
    this.dialog.open(component, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '95vh',
    });
  }

  removePromptFromOption(option) {
    if (option.startsWith(this.question)) {
      option = option.substring(this.question.length, option.length - 1);
    }
    return option;
  }

  convertVendorKeys() {
    const keyData = ['vendorName', 'vendorTenantId', 'contactName', 'contactNo', 'address', 'email', 'cinNo', 'gstNo', 'panNo', 'tinNo', 'Discontinued', 'POTerms', 'vendorQuotationRef', 'row_uuid'];
    const updatedVendorData = {};
    this.convertVendorDataTypes(this.registrationForm.value);
    keyData.forEach((key) => {
      let value = this.registrationForm.value[key];
      if (key === 'email' && typeof value === 'string' && value.trim() === "[]") {
        value = '';
      } else if (key === 'email' && Array.isArray(value) && value.length == 0) {
        value = '';
      } else if (key === 'email' && Array.isArray(value) && value.length > 0) {
        value = value.join(',');
      }
      updatedVendorData[key] = value || ''
    });
    return updatedVendorData
  }

  convertVendorDataTypes(jsonData){
  this.registrationForm.patchValue({
    vendorName: jsonData.vendorName,
    vendorTenantId: jsonData.vendorTenantId,
    contactName: jsonData.contactName,
    contactNo: this.notify.truncateAndFloor(jsonData.contactNo), // Convert string to number using parseFloat
    address: jsonData.address,
    cinNo: jsonData.cinNo,
    gstNo: jsonData.gstNo,
    panNo: jsonData.panNo,
    tinNo: jsonData.tinNo,
    Discontinued: jsonData.Discontinued,
    poTerms: jsonData.poTerms,
    row_uuid: jsonData.row_uuid
  });
}


  add(event: MatChipInputEvent): void {
    if (event.value) {
      if (this.validateEmail(event.value)) {
        this.emailList.push({ value: event.value, invalid: false });
      }
      if (Array.isArray(this.registrationForm.value.email)) {
        this.registrationForm.value.email.forEach(email => {
          this.emailList.push({ value: email });
        });
      } else {
        this.emailList.push({ value: this.registrationForm.value.email });
      }
      const emailValues = new Set(this.emailList.map(item => item.value));
      const emailsControl = this.registrationForm.get('email') as FormArray;
      emailsControl.clear();
      emailValues.forEach(value => emailsControl.push(this.fb.control(value)));
    }

    if (event.input) {
      event.input.value = '';
    }
  }

  removeEmail(data: any): void {
    if (this.registrationForm.value.email.indexOf(data) >= 0) {
      this.emailList.splice(this.emailList.indexOf(data), 1);
      this.registrationForm.value.email.splice(this.registrationForm.value.email.indexOf(data), 1)
    }
  }

  private validateEmail(email) {
    var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  }

  calculateColor(email) {
    if (this.validateEmail(email) == false) {
      return 'warn';
    }
    return '';
  }

  isInvalidEmail(email: string): boolean {
    return email && this.emailList.some(item => item.value === email && item.invalid);
  }

  private validateArrayNotEmpty(c: FormControl) {
    if (c.value && c.value.length === 0) {
      return {
        validateArrayNotEmpty: { valid: false }
      };
    }
    return null;
  }

  checkVendorName(filterValue){
    filterValue = filterValue.target.value;
    let data = this.sharedData.getBaseData().value;
        
    const isItemAvailable = data['vendors'].some(item => item.vendorName  === filterValue);    
  if (isItemAvailable) {
    this.registrationForm.get('vendorName').setErrors({ 'vendorItemExists': true });
  } else {
    this.registrationForm.get('vendorName').setErrors(null);
  }
  }

  onKeyDown(event: KeyboardEvent) {
    if (event.key === '.' && !event.shiftKey) {
      event.preventDefault(); // Prevent default dot behavior
      this.insertSpaceAfterDot();
    } else if (event.key === 'Enter') {
      event.preventDefault(); // Prevent default Enter behavior
      this.handleEnterKey();
    }
  }

  private insertSpaceAfterDot() {
    const textInput = this.textInputRef.nativeElement;
    const currentValue = textInput.value;
    const cursorPosition = textInput.selectionStart;
    const newText = currentValue.slice(0, cursorPosition) + '. ' + currentValue.slice(cursorPosition);
    textInput.value = newText;
    textInput.selectionStart = cursorPosition + 2; // Move cursor after the inserted dot and space
    textInput.selectionEnd = cursorPosition + 2;
  }

  private handleEnterKey() {
    const textInput = this.textInputRef.nativeElement;
    const currentValue = textInput.value;
    const lines = currentValue.split('\n');
    const lastLine = lines[lines.length - 1];
  
    // Check if the last line is a bullet point (e.g., "1. ", "2. ", etc.)
    const isBulletPoint = /^\d+\.\s/.test(lastLine.trim());
  
    if (isBulletPoint) {
      // If there is a bullet point, find the next number and add it with a bullet
      const nextNumber = parseInt(lastLine.trim().split('.')[0], 10) + 1;
      const newText = `${currentValue}\n${nextNumber}. `;
      textInput.value = newText;
    } else {
      // If there is no bullet point, simply add a new line
      textInput.value = `${currentValue}\n`;
    }
    textInput.scrollTop = textInput.scrollHeight;
  }

}