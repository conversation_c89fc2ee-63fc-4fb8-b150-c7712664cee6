.section {
  width: 100%;
  overflow-y: auto;
}

.bottomSearchInput{
  width: 100% !important;
}

::ng-deep .mat-step-icon-selected {
  background-color: rgba(211, 211, 211, 0.3607843137) !important;
 }

::ng-deep .mat-step-icon-state-done {
  background-color: #38869d !important;
}

.disabledBtn{
  color: grey;
}

// mat-error{
//   margin-top: -25px;
// }

.createClass{
  margin: 2.5rem auto;
  text-align: center;
}

.createTextClass{
  font-size: large;
}

.createBtnClass{
  margin: 15px auto;
  display: table;
}
.suffix{
  // padding-right: 15px;
  // font-size: 15px;
  // position: absolute;
  // margin-top: -29px;
  // margin-left: 90px;
  font-size: 15px;
  // position: relative;
  margin-top: -29px;
  margin-left: en;
  /* margin-left: 101px; */
  text-align: end;
  margin-right: 5px;
}

.material-symbols-outlined {
  font-size: 40px;
  line-height: 48px;
  color: green;
  cursor: pointer;
}

.registration-form {
  background-color: #f9f9f9; /* Background color for the form */
  border-radius: 8px; /* Rounded corners */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Shadow for better appearance */
}

.readonly-field {
  background-color: #e0e0e0;
}

.non-editable-field {
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
}

.non-editable-field label {
  font-weight: bold;
  margin-bottom: 4px;
}

.invFormError{
  margin-top: -27px;
}

.link{
  color: blue;
  text-decoration: underline;
  cursor: pointer;
  margin-left: 5px;
  font-size: 15px;
}

.clickable{
  cursor: pointer;
}

.non-clickable{
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.5;
}

.ai-search-container {
  display: flex;
  color: #673ab7;
  padding-left: 5px !important;
}

.search-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  float: inline-end;
  margin-top: -28px !important;
  margin-right: 10px;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.custom-search-icon {
  width: 35px;
  height: 35px;
  padding: 0px;
  border-radius: 5px;
  font-size: 35px;
  color: rgb(0 101 129 / 80%);
  border: 1px solid rgb(0 101 129 / 80%);
}

.ai-icon {
  color: #673ab7;
  font-size: 34px;
  vertical-align: middle;
  margin-right: 15px;
  width: 35px;
  height: 32px
}

.table-container {
  max-height: 200px;
  overflow-y: auto;
  display: block;
  margin-top: 15px !important;
}

.sticky-header {
  position: sticky;
  top: 0;
  background: #e5e5e5;
  color: rgba(0, 0, 0, 0.6);
  z-index: 2;
  text-align: left;
  font-weight: bolder;
  font-size: larger;
  text-align: left !important;
  vertical-align: middle !important;
}

.item-names {
  text-align: left !important;
  vertical-align: middle !important;
}

.word{
  font-style: italic;
  font-size: 16px;
}