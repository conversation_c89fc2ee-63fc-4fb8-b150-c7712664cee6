<div class="closeBtn" *ngIf="isDuplicate == true">
  <mat-icon (click)="checkInventory()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
</div>

<div class="registration-form py-2 px-3">

  <div class="m-1" *ngIf="isDuplicate === null">
    <mat-form-field appearance="outline">
      <mat-label>Search</mat-label>
      <input matInput placeholder="Search" (keyup)="filterDialog($event)" aria-label="Search">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
  </div>

  <div *ngIf="isDuplicate == true" class="mt-3 smallDialog">
    <div class="col-md-12">
      <div class="text-center my-2 p-2 bottomTitles">
        <span>Inventory Form</span>
      </div>
      <mat-form-field appearance="outline">
        <!-- <mat-label >Search inventory ..</mat-label> -->
        <input matInput placeholder="Search Inventory .." aria-label="Inventory" style="margin-top: 7px;"
          [formControl]="itemNameControl" (keyup.enter)="addOption('inventory master')"
          oninput="this.value = this.value.toUpperCase()">

          <button style="float: inline-end; margin-top: -30px;" *ngIf="!updateBtnActive" (click)="addOption('inventory master')"
            mat-raised-button color="accent" [disabled]="!itemNameControl.value" matTooltip="Add" matTooltipPosition="right">
            Add
          </button>

        <div matPrefix class="ai-search-container">
          <div *ngIf="aiSearch" class="spinner-border" style="margin-right: 20px;" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <mat-icon *ngIf="!aiSearch" class="ai-icon" matTooltipPosition="above" matTooltip="AI-Search">auto_awesome</mat-icon>
        </div>

        <!-- <mat-icon *ngIf="!updateBtnActive" class="custom-icon" (click)="addOption('inventory master')" matTooltipPosition="right"
        [matTooltip]="itemNameControl.value ? 'Add' : ''" [ngClass]="itemNameControl.value ? 'clickable' : 'non-clickable'"
        matSuffix >library_add</mat-icon> -->

      </mat-form-field>

      <span *ngIf="showIngredientName" class="word">Did you mean:</span>
      <span *ngIf="showIngredientName" class="link" (click)="setItemName(ingredientName, 'inventory master')">
        {{ ingredientName }}
      </span>

      <span *ngIf="noMatchFound" class="word">No Items Match:</span>
      <span *ngIf="noMatchFound" style="color: blue; font-size: 14px;"> {{ msg }} </span>

      <div *ngIf="itemNameControl.value" class="table-container">
        <table mat-table [dataSource]="itemNameOptions | async" class="mat-elevation-z8">
          <ng-container matColumnDef="inventoryName">
            <th mat-header-cell *matHeaderCellDef class="sticky-header" >
              {{ existingItems }} existing items found for "{{ itemNameControl.value }}"
            </th>
            <td mat-cell *matCellDef="let item" class="item-names">
              <span >{{ item }}</span>
              <span class="link" (click)="updateItem(item)" [matTooltip]="existingItems === 0 ? 'Add' : 'Update'" matTooltipPosition="above" style="float: inline-end;">
                {{ existingItems === 0 ? 'Add' : 'Update' }}
              </span>
            </td>
          </ng-container>
          <tr mat-header-row *matHeaderRowDef="['inventoryName']"></tr>
          <tr mat-row *matRowDef="let row; columns: ['inventoryName'];"></tr>
        </table>
      </div>
    </div>
  </div>

  <div class="mb-2 topCreateAndUpdateBtn" style="float: right; padding-top: 1rem;">
    <button *ngIf="isUpdateActive && (isDuplicate == false && !isPackaging)" style="margin-right: 5px;" (click)="updateInventory()"
      mat-raised-button color="accent" matTooltip="update" [disabled]="loadInvBtn || isUpdateButtonDisabled">
      <!-- <div *ngIf="loadSpinnerForApi" class="spinner-border" role="status"; isButtonDisabled = true  || isButtonDisabled>
        <span class="sr-only">Loading...</span>
      </div> *ngIf="!loadSpinnerForApi" -->
      Update
    </button>
    <button *ngIf="!isUpdateActive && (isDuplicate == false && !isPackaging)" style="margin-right: 5px;" (click)="createInventory();" mat-raised-button color="accent" matTooltip="create"
      >
      <!-- [disabled]="dataSource.data.length === 0 || this.registrationForm.invalid || isCreateButtonDisabled" -->

      <div *ngIf="loadSpinnerForApi" class="spinner-border" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      <mat-icon *ngIf="!loadSpinnerForApi">add_circle</mat-icon> Create
    </button>
    <button *ngIf="isDuplicate == false" mat-raised-button color="warn" style="margin-right: 5px;" (click)="checkInventory()" matTooltip="Close">
      <mat-icon>close</mat-icon>
      Close
    </button>
  </div>

  <div class="my-2 p-3 bottomTitles" *ngIf="!isDuplicate">
    Inventory
  </div>

  <div *ngIf="isDuplicate == false && !isPackaging">
    <div class="d-flex justify-content-center mt-3">
        <form [formGroup]="registrationForm">
          <div class="row">
            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Item Name</mat-label>
                <input formControlName="itemName" matInput placeholder="Item Name"
                  oninput="this.value = this.value.toUpperCase()" (keyup)="checkInvItemName($event)" (keydown)="restrictKeys($event)"
                  [readonly]="!isUpdateActive" [ngClass]="{'readonly-field': !isUpdateActive}">
              </mat-form-field>
              <!-- <mat-error class="formError"
                *ngIf="(!registrationForm.get('itemName').valid && registrationForm.get('itemName').dirty) && !registrationForm.get('itemName').hasError('itemExists')">
                * Invalid limit(1 min & 100 max)
              </mat-error> -->
              <mat-error class="formError" *ngIf="registrationForm.get('itemName').hasError('itemExists')">
                * Item name already exists
              </mat-error>
              <mat-error class="invFormError" *ngIf="registrationForm.get('itemName')?.hasError('required') && registrationForm.get('itemName')?.dirty">
                ItemName is required
              </mat-error>
              <mat-error class="invFormError" *ngIf="registrationForm.get('itemName').hasError('invStartsWithSpace')">
                * Cannot start with a space
              </mat-error>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Item Code</mat-label>
                <input formControlName="itemCode" matInput placeholder="Item Code" [readonly]="isInvFormCheck"
                [ngClass]="{'readonly-field': isInvFormCheck}">
              </mat-form-field>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>HSN/HAC Code</mat-label>
                <input formControlName="hsnCode" matInput placeholder="HSN/HAC code">
              </mat-form-field>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Ledger</mat-label>
                <input formControlName="ledger" matInput placeholder="Ledger"
                  oninput="this.value = this.value.toUpperCase()">
                <mat-icon matSuffix>account_balance</mat-icon>
              </mat-form-field>
              <mat-error class="invFormError" *ngIf="registrationForm.get('ledger')?.hasError('required') && registrationForm.get('ledger')?.dirty">
                Ledger is required
              </mat-error>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Category</mat-label>
                <input matInput placeholder="Category Name" aria-label="Category" [matAutocomplete]="auto1"
                  formControlName="category" (keydown)="restrictKeys($event)" (keyup.enter)="addOptionCat()" oninput="this.value = this.value.toUpperCase()">
                <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelectedCat($event.option)">
                  <mat-option *ngFor="let cat of catBank | async" [value]="cat">
                    <span>{{ cat }}</span>
                  </mat-option>
                </mat-autocomplete>
              </mat-form-field>
              <mat-error class="invFormError" *ngIf="registrationForm.get('category')?.hasError('required') && registrationForm.get('category')?.dirty">
                Category is required
              </mat-error>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Sub Category</mat-label>
                <input matInput placeholder="sub Category" aria-label="SubCategory" [matAutocomplete]="auto2"
                  formControlName="subCategory" (keyup.enter)="addOptionSubCat()" (keydown)="restrictKeys($event)"
                  oninput="this.value = this.value.toUpperCase()">
                <mat-autocomplete #auto2="matAutocomplete" (optionSelected)="optionSelectedSubCat($event.option)">
                  <mat-option *ngFor="let sub of subCatBank | async" [value]="sub">
                    <span>{{ sub }}</span>
                  </mat-option>
                </mat-autocomplete>
              </mat-form-field>
              <mat-error class="invFormError" *ngIf="registrationForm.get('subCategory')?.hasError('required') && registrationForm.get('subCategory')?.dirty">
                Sub Category is required
              </mat-error>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Classification</mat-label>
                <mat-select formControlName="classification">
                  <mat-option *ngFor="let option of ['Stockable', 'Non-Stockable']" [value]="option">
                    {{option}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <mat-error class="invFormError" *ngIf="registrationForm.get('classification')?.hasError('required') && registrationForm.get('classification')?.dirty">
                Classification is required
              </mat-error>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Vendor</mat-label>
                <mat-select formControlName="vendor" multiple>
                  <mat-option>
                    <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                      [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
                  </mat-option>
                  <mat-option class="hide-checkbox" (click)="toggleSelectAllVendor()">
                    <mat-icon matSuffix>check_circle</mat-icon>
                    Select All / Deselect All
                  </mat-option>
                  <mat-divider></mat-divider>
                  <mat-option *ngFor="let option of vendor | async" [value]="option">{{option}}</mat-option>
                </mat-select>
              </mat-form-field>
              <mat-error class="invFormError" *ngIf="registrationForm.get('vendor')?.hasError('required') && registrationForm.get('vendor')?.dirty">
                Vendor is required
              </mat-error>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Inventory UOM</mat-label>
                <mat-select formControlName="inventoryUom" (selectionChange)="selectValueForClosing($event.value)">
                  <mat-option *ngFor="let uom of ['KG', 'LITRE', 'NOS', 'MTR']" [value]="uom">
                    {{uom}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <mat-error class="invFormError" *ngIf="registrationForm.get('inventoryUom')?.hasError('required') && registrationForm.get('inventoryUom')?.dirty">
                InventoryUom is required
              </mat-error>
            </div>

            <div class="col-md-3">
                <div class="non-editable-field">
                  <label>Closing UOM</label>
                  <span>{{ registrationForm.get('closingUOM').value }}</span>
                </div>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Procured At</mat-label>
                <mat-select formControlName="procuredAt" multiple (selectionChange)="locationChange($event.value)">
                  <mat-option>
                    <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                      [formControl]="procuredAtFilterCtrl"></ngx-mat-select-search>
                  </mat-option>
                  <mat-option class="hide-checkbox" (click)="toggleSelectAllProcuredAt()">
                    <mat-icon matSuffix>check_circle</mat-icon>
                    Select All / Deselect All
                  </mat-option>
                  <mat-option *ngFor="let location of procuredAtLocation | async"
                    [value]="location"
                    [disabled]="discontinuedProcuredAtData.includes(location)"
                    [ngClass]="{'disabled-option': this.defaultProcuredAtData.includes(location) || discontinuedProcuredAtData.includes(location)}">
                    <span [ngClass]="{'disabledSelect': discontinuedProcuredAtData.includes(location)}">{{ location | uppercase }}</span>
                    <mat-icon *ngIf="this.defaultProcuredAtData.includes(location) && !this.discontinuedProcuredAtData.includes(location)"
                      class="deleteIconForMatSelect" matTooltip="discontinue"
                      (click)="onDelete(location, $event, 'procuredAt','null')"
                      [ngClass]="{'clickable': discontinuedProcuredAtData.includes(location)}">
                      delete
                    </mat-icon>
                    <mat-icon *ngIf="this.discontinuedProcuredAtData.includes(location)"
                      class="deleteIconForMatSelect" matTooltip="restore"
                      (click)="onRestore(location, $event, 'procuredAt','null')">
                      settings_backup_restore</mat-icon>
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <mat-error class="invFormError" *ngIf="registrationForm.get('procuredAt')?.hasError('required') && registrationForm.get('procuredAt')?.dirty">
                ProcuredAt is required
              </mat-error>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Issued To</mat-label>
                <mat-select formControlName="issuedTo" multiple>
                  <mat-option>
                    <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                      [formControl]="issuedToFilterCtrl"></ngx-mat-select-search>
                  </mat-option>
                  <mat-option class="hide-checkbox" (click)="toggleSelectAllIssuedTo()">
                    <mat-icon matSuffix>check_circle</mat-icon>
                    Select All / Deselect All
                  </mat-option>
                  <mat-optgroup *ngFor="let group of workAreas | async" [label]="group.restaurantIdOld.split('@')[1]"
                    [disabled]="group.disabled">
                    <mat-option *ngFor="let data of group.workAreas" [value]="data" [disabled]="isOptionDisabled(data , group)"
                    [ngClass]="{'disabled-option': isCheckOptionDisabled(data , group) || this.defaultIssuedToData.includes(data)}">
                    <span [ngClass]="{'disabledSelect': isOptionDisabled(data , group) || group.disabled}">{{ data | uppercase }}</span>
                      <mat-icon *ngIf="!discontinuedProcuredAtData.includes(group.abbreviatedRestaurantId) && this.defaultIssuedToData.includes(data) && !isOptionDisabled(data , group)"
                        class="deleteIconForMatSelect" matTooltip="discontinue"
                        (click)="onDelete(data, $event, 'issuedTo' , group)"
                        [ngClass]="{'clickable': discontinuedIssuedToData.includes(data)}">
                        delete
                      </mat-icon>
                      <mat-icon *ngIf="isOptionDisabled(data , group) && !group.disabled"
                        class="deleteIconForMatSelect" matTooltip="restore" (click)="onRestore(data, $event, 'issuedTo',group)">
                        settings_backup_restore
                      </mat-icon>
                      </mat-option>
                  </mat-optgroup>
                </mat-select>
              </mat-form-field>
              <mat-error class="invFormError" *ngIf="registrationForm.get('issuedTo')?.hasError('required') && registrationForm.get('issuedTo')?.dirty">
                IssuedTo is required
              </mat-error>
            </div>

            <div class="col-md-2">
              <mat-form-field appearance="outline">
                <mat-label>Weight (GM/ML/NOS)</mat-label>
                <input [ngClass]="{'highlighted-input': isWeightDisabled()}" formControlName="weight" matInput type="number"
                  [readonly]="isWeightDisabled()" (focus)="focusFunction('weight')" (focusout)="focusOutFunction('weight')"
                  autocomplete="off" placeholder="Weight">
              </mat-form-field>
              <mat-error class="invFormError" *ngIf="weight.hasError('min') && registrationForm.get('weight')?.dirty">
              <!-- class="invFormError" *ngIf="weight.invalid && (weight.dirty || weight.touched)" -->
                <!-- <div *ngIf="weight.hasError('required')">This field is required.</div> -->
                <div >Value must be greater than or equal to 1</div>
              </mat-error>
              <mat-error class="invFormError" *ngIf="registrationForm.get('weight')?.hasError('required') && registrationForm.get('weight')?.dirty">
                Weight is required
              </mat-error>
            </div>

            <div class="col-md-2">
              <mat-form-field appearance="outline">
                <mat-label>Yield</mat-label>
                <input formControlName="yield" matInput type="number" (focus)="focusFunction('yield')"
                  (focusout)="focusOutFunction('yield')" (keyup)="sumForFinalRate($event)" autocomplete="off"
                  placeholder="Yield">
                <!-- <mat-error *ngIf="yield.invalid && (yield.dirty || yield.touched)">
                  <div *ngIf="yield.hasError('required')">This field is required.</div>
                </mat-error> -->
              </mat-form-field>
              <mat-error class="invFormError" *ngIf="registrationForm.get('yield')?.hasError('required') && registrationForm.get('yield')?.dirty">
                Yield is required
              </mat-error>
            </div>

            <div class="col-md-2">
              <mat-form-field appearance="outline">
                <mat-label>Tax (%)</mat-label>
                <input formControlName="taxRate" matInput type="number" (keyup)="setTax('taxRate'); sumForFinalRate($event)"
                  (focus)="focusFunction('taxRate')" (focusout)="focusOutFunction('taxRate')" autocomplete="off"
                  placeholder="Tax %">
                <mat-icon matSuffix>percent</mat-icon>
              </mat-form-field>
              <mat-error class="invFormError" *ngIf="registrationForm.get('taxRate')?.hasError('required') && registrationForm.get('taxRate')?.dirty">
                TaxRate is required
              </mat-error>
            </div>

            <div class="col-md-2">
              <mat-form-field appearance="outline">
                <mat-label>Lead Time</mat-label>
                <input formControlName="leadTime" type="number" (focus)="focusFunction('leadTime')"
                  (focusout)="focusOutFunction('leadTime')" autocomplete="off" matInput placeholder="Lead Time">
                <span matSuffix class="m-2 fw-bold">Days</span>
              </mat-form-field>
              <mat-error class="invFormError" *ngIf="registrationForm.get('leadTime')?.hasError('required') && registrationForm.get('leadTime')?.dirty">
                LeadTime is required
              </mat-error>
            </div>

            <div class="col-md-2">
              <mat-form-field appearance="outline">
                <mat-label>Unit Cost</mat-label>
                <input formControlName="rate" matInput type="number" (focus)="focusFunction('rate')"
                  (focusout)="focusOutFunction('rate')" (keyup)="sumForFinalRate($event)" autocomplete="off"
                  placeholder="Rate ">
                <mat-icon matSuffix>&#8377;</mat-icon>
                <mat-error *ngIf="rate.invalid && (rate.dirty || rate.touched)">
                  <div *ngIf="rate.hasError('required')">This field is required.</div>
                </mat-error>
              </mat-form-field>
            </div>

            <!-- temporally commanded -->

            <!-- <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Stock Conversion</mat-label>
                <mat-select formControlName="stockConversion" placeholder="Stock Conversion"
                  (selectionChange)="checkChildItems($event.value)">
                  <mat-option *ngFor="let data of ['yes', 'no']" [value]="data">
                    {{data | titlecase}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div> -->

            <!-- <div class="col-md-3" *ngIf="registrationForm.value.stockConversion == 'yes'">
              <mat-form-field appearance="outline"
                [ngClass]="{'highlighted-input': registrationForm.value.stockConversion == 'no'}">
                <mat-label>Child Items</mat-label>
                <mat-select formControlName="childItemCode" multiple placeholder="Child Items"
                  (selectionChange)="getChildItemCode($event.value)">
                  <mat-option>
                    <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                      [formControl]="itemsFilterCtrl"></ngx-mat-select-search>
                  </mat-option>
                  <mat-option class="hide-checkbox" (click)="toggleSelectAllItems()"
                    [disabled]="registrationForm.value.stockConversion == 'no'">
                    <mat-icon matSuffix>check_circle</mat-icon>
                    Select All / Deselect All
                  </mat-option>
                  <mat-divider></mat-divider>
                  <mat-option *ngFor="let value of items | async" [value]="value.itemCode"
                    [disabled]="value.itemName === registrationForm.value.itemName">
                    {{value.itemName}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div> -->
            <!-- temporally commanded -->

            <div *ngIf="isUpdateActive">
              <div class="col">
                <label>Do you want to discontinue?</label>
                <mat-radio-group formControlName="discontinued" aria-labelledby="example-radio-group-label">
                  <mat-radio-button value="yes">Yes</mat-radio-button>
                  <mat-radio-button value="no">No</mat-radio-button>
                </mat-radio-group>
              </div>
            </div>
          </div>
        </form>
    </div>

    <div>
      <div class="my-2 p-3 bottomTitles">
        Package
      </div>
      <mat-slide-toggle [(ngModel)]="isChecked" class="mt-2 mx-2 floatRightBtn" (change)="onToggleChange($event)" >Show Bottle Weights</mat-slide-toggle>
      <!-- <mat-slide-toggle [(ngModel)]="isExpiryChecked" class="mt-2 mx-2 floatRightBtn" (change)="toggleChange($event)" >Add expiry date</mat-slide-toggle> -->
      <br><br>
      <div style="margin-top: 10px;">
        <div cdkTrapFocus>
          <form [formGroup]="packagingForm">
            <div class="d-flex gap-3 mb-3">
              <div class="form-group customHeightfield">
                <label for="ingredientSelect">Package</label>
                <!-- <select class="form-select selectInputCustom" placeholder="Package Name" formControlName="packageName"
                (change)="optionSelectedPack(packagingForm.value.packageName)" style="width: 220px;">
                <option>
                    <input type="text" class="form-control" placeholder="search..." (focus)="openSelect()"
                          [formControl]="nameOptionsFilterCtrl">
                </option>
                  <option *ngFor="let name of packNameOptions | async" [value]="name" >
                      {{ name | uppercase }}
                  </option>
                </select>  (keyup.enter)="addOptionPack()"-->

                  <input matInput placeholder="Package Name" [matAutocomplete]="autoPack" class="form-control"
                    formControlName="packageName" oninput="this.value = this.value.toUpperCase()" (keyup)="enterPackage()">
                  <mat-autocomplete #autoPack="matAutocomplete" (optionSelected)="optionSelectedPack($event.option)">
                    <mat-option *ngFor="let name of packNameOptions | async" [value]="name" [disabled]="packageNames?.length && packageNames.includes(name)">
                      <span>{{ name | uppercase }}</span>
                    </mat-option>
                  </mat-autocomplete>

                <mat-error class="formError" style="padding-bottom: 20px;"
                    *ngIf="(!packagingForm.get('packageName').valid && packagingForm.get('packageName').dirty) && !packagingForm.get('packageName').hasError('packItemExists')">
                    * Invalid limit(1 min & 100 max)
                  </mat-error>

                  <mat-error class="formError" *ngIf="packagingForm.get('packageName').hasError('packItemExists')">
                    * Package name already exists
                  </mat-error>

                  <mat-error class="formError" *ngIf="packagingForm.get('packageName').hasError('startsWithSpace')">
                    * Cannot start with a space
                  </mat-error>
              </div>

              <!-- <div class="form-group customHeightfield">
                <label for="modifierSelect">UOM</label>
                <select class="form-select" id="modifierSelect"  formControlName="uom" style="width: 80px !important;">
                  <option *ngFor="let data of ['GM', 'ML', 'NOS', 'MM']" [value]="data" [disabled]="true">
                    {{ data }}
                  </option>
                </select>
              </div> -->

              <div class="form-group customHeightfield">
                <label for="uomSelect">Brand</label>
                <input formControlName="brand" type="number" class = "highlighted-input form-control"
                  placeholder="Brand" oninput="this.value = this.value.toUpperCase()"
                  autocomplete="off">
              </div>

              <div class="form-group customHeightfield" *ngIf="isChecked">
                <label for="portionCountInput">Empty bottle</label>
                <!-- <label *ngIf="1200 >= checkWidth" for="portionCountInput">Empty bottle</label> -->
                <input formControlName="emptyBottleWeight" type="number" class = "highlighted-input form-control"
                placeholder="Empty bottle weight" autocomplete="off"
                (focus)="focusFunction('emptyBottleWeight')"
                (focusout)="focusOutFunctionPackage('emptyBottleWeight')">
              </div>

              <div class="form-group customHeightfield" *ngIf="isChecked">
                <label for="yieldInput">Full bottle</label>
                <!-- <label *ngIf="1200 >= checkWidth" for="yieldInput">Full bottle</label> -->
                <input formControlName="fullBottleWeight" type="number"
                placeholder="Full bottle weight" autocomplete="off"
                (focus)="focusFunction('fullBottleWeight')" class = "highlighted-input form-control"
                (focusout)="focusOutFunctionPackage('fullBottleWeight')">
              </div>

              <div class="form-group customHeightfield">
                <label for="yieldInput">Qty</label>
                <input formControlName="quantityPerUnit" type="number" class = "highlighted-input form-control"
                placeholder="Quantity per unit" autocomplete="off" (focus)="focusFunction('quantityPerUnit')"
                (focusout)="focusOutFunctionPackage('quantityPerUnit')" (keyup)="getSumOfPackage()"
                [readonly]="checkUnitPackage()">
                <div matSuffix class="suffix">{{ selectedUOM }}</div>
                <!-- <mat-error *ngIf="quantityPerUnit.invalid && (quantityPerUnit.dirty || quantityPerUnit.touched)">
                  <div>Value must be greater than or equal to 0.001</div>
                </mat-error> -->
                <!-- <mat-error class="mt-2" *ngIf="packagingForm.get('quantityPerUnit')?.hasError('required') && packagingForm.get('quantityPerUnit')?.dirty">
                  Required and must be greater than 0
                </mat-error> -->
                <mat-error class="mt-2"
                  *ngIf="packagingForm.get('quantityPerUnit')?.hasError('required') && packagingForm.get('quantityPerUnit')?.dirty">
                  Required field
                </mat-error>
                <mat-error class="mt-2"
                  *ngIf="packagingForm.get('quantityPerUnit')?.value === 0 && packagingForm.get('quantityPerUnit')?.dirty">
                  Must be greater than 0
                </mat-error>
              </div>

              <div class="form-group customHeightfield">
                <label for="yieldInput">Price</label>
                <input formControlName="packagePrice" type="number"
                placeholder="Package Price" autocomplete="off" (focus)="focusFunction('packagePrice')"
                (focusout)="focusOutFunctionPackage('packagePrice')"  class = "highlighted-input form-control"
                [readonly]="checkUnitPackage()">
                <!-- <mat-error class="mt-2" *ngIf="packagePrice.invalid && (packagePrice.dirty || packagePrice.touched)">
                  <span *ngIf="packagePrice.hasError('required')">This field is required.</span>
                  <span *ngIf="packagePrice.hasError('min')">Value must be greater than or equal to 1</span>
                </mat-error> -->
                <mat-error style="margin-top: -1px;"
                  *ngIf="packagingForm.get('packagePrice')?.hasError('required') && packagingForm.get('packagePrice')?.dirty">
                  Required field
                </mat-error>
                <mat-error style="margin-top: -1px;"
                  *ngIf="packagePrice.hasError('min') && packagingForm.get('packagePrice')?.dirty">
                  Must be greater than 0
                </mat-error>
              </div>

              <div class="form-group customHeightfield">
                <label for="yieldInput">ParLevel</label>
                <input formControlName="parLevel" type="number"
                placeholder="ParLevel" autocomplete="off" (focus)="focusFunction('parLevel')"
                (focusout)="focusOutFunctionPackage('parLevel')"  class = "highlighted-input form-control"
                [readonly]="checkUnitPackage()">
              </div>

              <div class="form-group flex-shrink-0 d-flex align-items-end justify-content-end"
                style="margin-bottom: 0.1px;">
                <button type="submit" style="height: 2.3rem;" class="btn btn-secondary btn-sm px-3"
                  (click)="addNewPackage()" matTooltip="Add"
                  [disabled]="this.packagingForm.invalid || (this.registrationForm.value.discontinued == 'yes' || this.registrationForm.invalid)">
                  <!-- [disabled]="!packagingForm.value.packageName || (this.registrationForm.value.discontinued == 'yes' || this.registrationForm.invalid)"> -->
                  <i class="material-icons align-middle">add</i> Add
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="section" #section #widgetsContent>
        <div class="tableDiv" *ngIf="isPackageDataReady">
            <mat-table [dataSource]="dataSource" matSort>
              <ng-container matColumnDef="action">
                <mat-header-cell *matHeaderCellDef class="tableActionColdel"> Action </mat-header-cell>
                <mat-cell *matCellDef="let element" class="tableActionColdel">
                  <button (click)="preFillPackageForm(element,addPackaging)" backgroundColor="primary" matTooltip="edit"
                    class="mx-2 editIconBtn"
                    ><mat-icon class="mt-1">edit</mat-icon></button>
                    <!-- <button *ngIf="element.row_uuid == '' || element.newItem == 'true'" (click)="openDeleteDialog(element)" backgroundColor="primary"
                      matTooltip="edit" class="mx-2 editIconBtn"><mat-icon class="mt-1">delete</mat-icon></button> -->
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="position">
                <mat-header-cell *matHeaderCellDef class="tableSnoCol"> No. </mat-header-cell>
                <mat-cell *matCellDef="let element; let i = index;" class="tableSnoCol"> {{i+1}} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="discontinued">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Status </mat-header-cell>
                <mat-cell class="custom-cell justify-content-start" *matCellDef="let element">
                  <div *ngIf="element.Discontinued == 'yes'" class="d-flex align-items-center">
                    <mat-icon class="cancelIcon">cancel</mat-icon> &nbsp; Discontinued
                  </div>
                  <div *ngIf="element.Discontinued == 'no'" class="d-flex align-items-center">
                    <mat-icon class="checkIcon">check_circle</mat-icon> &nbsp; Active
                  </div>
                  <div *ngIf="element.Discontinued != 'no' && element.Discontinued != 'yes'"
                    class="d-flex align-items-center">
                    <mat-icon class="checkIcon">check_circle</mat-icon> &nbsp; Active
                  </div>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="modified">
                <mat-header-cell *matHeaderCellDef class="tableModCol"> Modified </mat-header-cell>
                <mat-cell *matCellDef="let element" class="tableModCol">
                  <div *ngIf="element.modified == 'yes'">
                    <mat-chip color="primary">NOT SYNCED</mat-chip>
                  </div>
                  <div *ngIf="element.modified == 'no' || element.modified == '-'">
                    -
                  </div>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="packageName">
                <mat-header-cell class="custom-header" *matHeaderCellDef style="min-width: 220px !important;"> Package </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element" style="min-width: 220px !important;">
                  <div style="width: 125px !important;">
                    {{element.PackageName }}
                  </div>
                  <div *ngIf="element.Discontinued == 'yes'" class="d-flex align-items-center">
                    <mat-icon class="cancelIcon">cancel</mat-icon>
                  </div>
                  <div *ngIf="element.Discontinued == 'no'" class="d-flex align-items-center">
                    <mat-icon class="checkIcon">check_circle</mat-icon>
                  </div>
                  <div *ngIf="element.Discontinued != 'no' && element.Discontinued != 'yes'"
                    class="d-flex align-items-center">
                    <mat-icon class="checkIcon">check_circle</mat-icon>
                  </div>
                  <button (click)="preFillPackageForm(element,addPackaging)" backgroundColor="primary" matTooltip="edit"
                    class="mx-2 editIconBtn" [disabled]="this.registrationForm.value.discontinued == 'yes'"><mat-icon class="mt-1">edit</mat-icon></button>

                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="brand">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Brand </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element.brand || '-' }} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="category">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Category </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element.category }} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="subCategory">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Sub Category </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element.subCategory}} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="inventoryCode">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Inventory Code </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element.InventoryCode }} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="itemName">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Item Name </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element.ItemName }} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="package">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Package </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{ element['Units/ package'] }} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="quantityPerUnit">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Quantity </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{this.notify.truncateAndFloor( element['Quantity per unit']) | number:'1.2-2' }} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="totalQtyOfPackage">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Tot Qty Of Package </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element['Total qty of package']}} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="unitUOM">
                <mat-header-cell class="custom-header" *matHeaderCellDef> UOM </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element.UnitUOM }} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="emptyBottleWeight">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Empty bottle weight </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element['Empty bottle weight']}} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="fullBottleWeight">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Full bottle weight </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element['Full bottle weight']}} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="packagePrice">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Price </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element.PackagePrice }} </mat-cell>
              </ng-container>

              <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumns;"
                [ngClass]="{'highlighted-row': row.Discontinued === 'yes'}"></mat-row>
            </mat-table>
        </div>

        <div *ngIf="!isPackageDataReady">
          <ngx-skeleton-loader count="20" animation="pulse" [theme]="{
            'border-radius': '4px',
            'height': '30px',
            'margin-bottom': '8px',
            'width': '19%',
            'margin-right': '1%',
            'display': 'inline-block',
            'opacity': '0.85'
          }"></ngx-skeleton-loader>
        </div>

        <div *ngIf="dataSource.data.length == 0 && isPackageDataReady">
          <app-empty-state
            icon="inventory_2"
            title="No Packages Found"
            message="No packages have been added yet. Click the 'Add' button to create your first package."
            customClass="dialog-empty-state"
          ></app-empty-state>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- ----------------------------------   PACKAGING FORM    ---------------------------------- -->
<ng-template #addPackaging>
  <div class="closeBtn">
    <mat-icon class="closeBtnIcon" matTooltip="close" (click)="closePackage()">close</mat-icon>
  </div>

  <div class="registration-form py-2 px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span>Package Form</span>
    </div>

    <div class="mb-2 topCreateAndUpdateBtn">
      <button *ngIf="updatePackaging" (click)="editExistingPackage()" mat-raised-button color="accent"
        matTooltip="update" [disabled]="loadPackBtn">
        Update</button>
      <button *ngIf="!updatePackaging" (click)="addNewPackage()" mat-raised-button color="accent" matTooltip="add">
        <mat-icon>library_add</mat-icon>Add</button>
    </div>

    <form [formGroup]="packagingForm">
      <div class="row">
        <!-- <div class="col-md-6">
          <mat-form-field  [ngClass]="{'highlighted-input': isReadOnly}">
            <mat-label>Inventory Code</mat-label>
            <input formControlName="inventoryCode" matInput placeholder="Inventory Code" [readonly]="isReadOnly">
          </mat-form-field>
        </div>

        <div class="col-md-6">
          <mat-form-field  [ngClass]="{'highlighted-input': isReadOnly}">
            <mat-label>Item Name</mat-label>
            <input formControlName="itemName" matInput placeholder="Item Name" [readonly]="isReadOnly"
              oninput="this.value = this.value.toUpperCase()">
          </mat-form-field>
        </div>
        oninput="this.value = this.value.toUpperCase()" (keyup)="checkPackItemName($event)" [readonly]="updatePackaging && packagingForm.value.packageName" -->

        <div>
          <mat-form-field  [ngClass]="{'highlighted-input': updatePackaging && packagingForm.value.packageName}">
            <mat-label>Package Name</mat-label>
            <input formControlName="packageName" matInput placeholder="Package Name"readonly>
          </mat-form-field>
          <mat-error class="formError"
            *ngIf="(!packagingForm.get('packageName').valid && packagingForm.get('packageName').dirty) && !packagingForm.get('packageName').hasError('packItemExists')">
            * Invalid limit(1 min & 100 max)
          </mat-error>

          <mat-error class="formError" *ngIf="packagingForm.get('packageName').hasError('packItemExists')">
            * Package name already exists
          </mat-error>

        </div>

        <div>
          <mat-form-field >
            <mat-label>Brand</mat-label>
            <input formControlName="brand" minlength="1" matInput placeholder="brand"
              oninput="this.value = this.value.toUpperCase()">
          </mat-form-field>
        </div>

        <!-- <div class="col-md-6">
          <mat-form-field  [ngClass]="{'highlighted-input': isReadOnly}">
            <mat-label>Units/ package</mat-label>
            <input formControlName="package" matInput type="number" autocomplete="off" [readonly]="isReadOnly"
              (keyup)="getSumOfPackage()" (focus)="focusFunction('package')"
              (focusout)="focusOutFunctionPackage('package')" placeholder="Units/ package">
            <mat-error *ngIf="package.invalid && (package.dirty || package.touched)">
              <div *ngIf="package.hasError('required')">This field is required.</div>
              <div *ngIf="package.hasError('min')">Value must be greater than or equal to 1</div>
            </mat-error>
          </mat-form-field>
        </div> -->

        <div>
          <mat-form-field  [ngClass]="{'highlighted-input': checkUnitPackage()}">
            <mat-label>Quantity per unit</mat-label>
            <input formControlName="quantityPerUnit" matInput type="number" autocomplete="off"
              (keyup)="getSumOfPackage()" (focus)="focusFunction('quantityPerUnit')"
              (focusout)="focusOutFunctionPackage('quantityPerUnit')" placeholder="Quantity per unit"
              [readonly]="checkUnitPackage()">
            <mat-error *ngIf="quantityPerUnit.invalid && (quantityPerUnit.dirty || quantityPerUnit.touched)">
              <div>Value must be greater than or equal to 0.001</div>
            </mat-error>
          </mat-form-field>
        </div>

        <!-- <div class="col-md-6">
          <mat-form-field  [ngClass]="{'highlighted-input': isReadOnly}" >
            <mat-label>Inventory UOM</mat-label>
            <mat-select formControlName="unitUOM" [disabled]="isReadOnly">
              <mat-option *ngFor="let uom of ['KG', 'LITRE', 'NOS', 'MTR']" [value]="uom" [disabled]="isReadOnly">
                {{uom}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div> -->

        <div>
          <mat-form-field >
            <mat-label>Empty bottle weight</mat-label>
            <input formControlName="emptyBottleWeight" matInput type="number" autocomplete="off"
              (focus)="focusFunction('emptyBottleWeight')" (focusout)="focusOutFunctionPackage('emptyBottleWeight')"
              placeholder="Empty bottle weight">
          </mat-form-field>
        </div>

        <div>
          <mat-form-field >
            <mat-label>Full bottle weight</mat-label>
            <input formControlName="fullBottleWeight" matInput type="number" autocomplete="off"
              (focus)="focusFunction('fullBottleWeight')" (focusout)="focusOutFunctionPackage('fullBottleWeight')"
              placeholder="Full bottle weight">
          </mat-form-field>
        </div>

        <div>
          <mat-form-field >
            <mat-label>package Price</mat-label>
            <input formControlName="packagePrice" matInput type="number" (focus)="focusFunction('packagePrice')"
              (focusout)="focusOutFunctionPackage('packagePrice')" autocomplete="off" placeholder="Package Price">
            <mat-error *ngIf="packagePrice.invalid && (packagePrice.dirty || packagePrice.touched)">
              <div *ngIf="packagePrice.hasError('required')">This field is required.</div>
              <div *ngIf="packagePrice.hasError('min')">Value must be greater than or equal to 1</div>
            </mat-error>
          </mat-form-field>
        </div>

        <div>
          <mat-form-field >
            <mat-label>Par Level</mat-label>
            <input formControlName="parLevel" matInput type="number" autocomplete="off"
              (focus)="focusFunction('parLevel')" (focusout)="focusOutFunctionPackage('parLevel')"
              placeholder="Par Level">
          </mat-form-field>
        </div>

        <!-- <div class="col">
          <label style="margin-right: 30px;">Do you want add expiry date?</label>
          <mat-radio-group formControlName="expiryDate" aria-labelledby="example-radio-group-label">
            <mat-radio-button value="yes" >Yes</mat-radio-button>
            <mat-radio-button value="no" >No</mat-radio-button>
          </mat-radio-group>
        </div> -->

        <div *ngIf="updatePackaging">
          <div class="col">
            <label style="margin-right: 37px;">Do you want to discontinue?</label>
            <mat-radio-group formControlName="discontinued" aria-labelledby="example-radio-group-label">
              <mat-radio-button value="yes" [disabled]="checkPkgAvailability()">Yes</mat-radio-button>
              <mat-radio-button value="no" >No</mat-radio-button>
            </mat-radio-group>
          </div>
        </div>
      </div>
    </form>
  </div>
</ng-template>

<div *ngIf="isDuplicate === null" class="mt-3 smallDialog dropDndDialog">
  <div *ngFor="let data of filteredData;let i = index" class="my-2">
    {{i + 1}}. {{data}}
  </div>
  <div *ngIf="filteredData?.length == 0">
    <app-empty-state
      icon="search_off"
      title="No Results Found"
      message="No matching data found for your search criteria."
      customClass="dialog-empty-state"
    ></app-empty-state>
  </div>
</div>

<ng-template #openDraftChangeDialog>
  <div class="closeBtn">
    <mat-icon class="closeBtnIcon" matTooltip="close" (click)="closeInfoDialog()">close</mat-icon>
  </div>
  <div class="registration-form py-2 px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span>Unsaved changes</span>
    </div>
    <div class="m-3 infoText">
      Want to save your changes ?
    </div>
    <div class="text-end m-2">
      <button (click)="updateInventory()" mat-raised-button color="accent" matTooltip="Update" class="m-1">
        Yes</button>
      <button (click)="closeInfoDialog()" mat-raised-button matTooltip="close" class="m-1">
        No</button>
    </div>
  </div>
</ng-template>

<ng-template #deleteItemDialog>
  <div class="registration-form py-2 px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span>Delete Item</span>
    </div>
    <div class="m-3 infoText">
      Want to delete items ?
    </div>
    <div class="text-end m-2">
      <button (click)="deleteFun()" mat-raised-button color="accent" matTooltip="Update" class="m-1">
        Yes</button>
      <button (click)="closeDialog()" mat-raised-button matTooltip="close" class="m-1">
        No</button>
    </div>
  </div>
</ng-template>


<ng-template #discontinuedSelectDialog>
  <div class="registration-form py-2 px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span>Discontinued Location</span>
    </div>
    <div class="m-3 infoText text-center">
      Would you like to discontinue {{selectedData}} ?
    </div>
    <div class="text-end m-2">
      <button mat-raised-button (click)="discontinuedSelectData()" color="accent" matTooltip="Update" class="m-1">
        Yes</button>
      <button (click)="closeDialog()" mat-raised-button matTooltip="close" class="m-1">
        No</button>
    </div>
  </div>
</ng-template>


<ng-template #invalidDataDialog>
  <div class="registration-form py-2 px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span>Invalid Data</span>
    </div>
    <div class="m-3">
      <div *ngFor="let data of checkDataValidation; let i = index;">
        <div class="mb-3">
          <div class="d-flex mb-1">
            {{i+1}}. <b>{{data.key | titlecase}}</b> - {{data.value ? data.value : 'null'}}
          </div>
          <div>
            Expected Value - {{data.expectedValue}}
          </div>
        </div>
      </div>
    </div>
    <div class="text-end">
      <button (click)="closeDialog()" color="warn" mat-raised-button matTooltip="close" class="m-1">
        Close</button>
    </div>
  </div>
</ng-template>