  .section{
    width: 100%;
    overflow-y: auto;
  }

  .no-outline:disabled {
    outline: none;
  }

  ::ng-deep .mat-step-icon-selected {
    background-color: rgba(211, 211, 211, 0.3607843137) !important;
   }
  
  ::ng-deep .mat-step-icon-state-done {
    background-color: rgba(0, 101, 129, 0.8) !important;
  }
  
  .disabledBtn{
    color: grey;
  }

.createClass{
  margin: 2.5rem auto;
  text-align: center;
}

.createTextClass{
  font-size: large;
}

.createBtnClass{
  margin: 15px auto;
  display: table;
}

.createBtn{
  margin: 0 auto;
}

mat-error {
  color: red; 
}

.material-symbols-outlined {
  font-size: 40px; 
  line-height: 48px;
  color: green;
  cursor: pointer;
}

.bottom-titles {
  // font-size: 1.5em;
  // font-weight: bold;
  // color: #444;
  // border-bottom: 2px solid #343a40; /* Dark Gray */
  // padding-bottom: 10px;
  color: rgba(0, 0, 0, 0.6);
  font-size: larger;
  font-weight: bolder;
  background-color: #e5e5e5;
}

.portion-info {
  margin-top: 20px;
  padding: 10px;
  border-top: 1px solid #e0e0e0;
}

.portion-info1{
  margin-top: 20px;
  padding: 10px;
}

.info-item {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.info-content {
  margin: 0;
  font-size: 1.1em;
  color: #004175; /* Navy Blue */
}

.info-content strong {
  color: #004175; /* Navy Blue */
}

.golden-yellow-chip {
  background-color: #ffc107 !important; 
  color: rgba(0, 0, 0, 0.87) !important; 
  max-height: 1.2rem;
}

.close-btn {
  text-align: right;
}

.close-btn-icon {
  color: #ff0000; /* or any color for the close button */
}

.registration-form {
  background-color: #f9f9f9; /* Background color for the form */
  border-radius: 8px; /* Rounded corners */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Shadow for better appearance */
}

// .bottom-titles span {
//   font-size: 1.2em; /* Increase the font size for titles */
//   font-weight: bold; /* Make title bold */
// }

.portion-info {
  margin-top: 10px;
}

.info-table {
  width: 100%;
  border-collapse: collapse;
}

.info-table td {
  padding: 8px;
  border-bottom: 1px solid #ddd; /* Light gray border for rows */
}

.info-key {
  text-align: left;
  font-weight: bold;
}

.info-value {
  text-align: right;
  color: #333; /* Darker color for values */
}

.info-unit {
  text-align: right;
  color: #666; /* Lighter color for units */
}


.non-editable-field {
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
}

.non-editable-field label {
  font-weight: bold;
  margin-bottom: 4px;
}

.readonly-field {
  background-color: #e0e0e0;
}
.mappingBtn {
  margin-right: 5px;
}

.discButton{
  background-color: white !important;
  color: black !important;
  border: 1px solid rgba(8, 87, 151, 0.685) !important;
}

.sub-data-class {
  color: rgb(119, 12, 12); /* Change this to your desired color */
   /* Optional: make the text bold */

   color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;

}