
// .edit-icon{
//     height: 150px;
//     width: 150px;
//     font-size: 150px;
//     color: rgb(28, 150, 221);
// }

// .edit-card{
//     height: 83%;
//     display: flex;
//     justify-content: center;
//     align-items: center;
// }

.mat-mdc-tab-group {
  margin: 5px !important;
}

::ng-deep .mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab {
    flex-grow: 0.1 !important;
  }