import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatRadioModule } from '@angular/material/radio';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSelectModule } from '@angular/material/select';
import { ShareDataService } from 'src/app/services/share-data.service';
import { NotificationService } from 'src/app/services/notification.service';
import { InventoryService } from 'src/app/services/inventory.service';
import { MasterDataService } from 'src/app/services/master-data.service';
import { AuthService } from 'src/app/services/auth.service';
import { ChatBotComponent } from 'src/app/components/chat-bot/chat-bot.component';
import { catchError, startWith, switchMap, takeWhile } from 'rxjs/operators';
import { of, interval } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-account-setup',
  standalone: true,
  templateUrl: './account-setup.component.html',
  styleUrls: ['./account-setup.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    MatIconModule,
    MatInputModule,
    MatTooltipModule,
    FormsModule,
    ReactiveFormsModule,
    MatRadioModule,
    MatButtonModule,
    MatCardModule,
    MatSelectModule,
    MatProgressBarModule,
    MatTabsModule,
    ChatBotComponent,
    RouterModule
  ]
})

export class AccountSetupComponent {

  registrationForm!: FormGroup;
  isUpdateButtonDisabled = false;
  isDuplicate: any;
  baseData: any;
  user: any;
  isUpdateActive: boolean = false;
  loadSpinnerForLogo: boolean = false;
  loadSpinnerForApi: boolean = false;
  selectedFiles: { url: string }[] = [];
  logoUrl: string | null = null;
  hidePassword: boolean = true;
  isEditMode: boolean = false;
  selectedTabIndex: number = 0;
  tenantCreated: boolean = false;
  aiDataAvailable: boolean = false;
  isLoading: boolean = false;


  downloadSteps = [
    {"name": "Starting your menu analysis", "completed": false, "description": "Reviewing all items on your restaurant menu"},
    {"name": "Identifying and matching ingredients", "completed": false, "description": "Intelligently categorizing ingredients from your recipes"},
    {"name": "Creating your final data", "completed": false, "description": "Generating detailed inventory and packaging recommendations"}
]

  statusPolling: any;
  processingId: string;
  showDataDownload: boolean = true;
  isDownloading: boolean = false;
  downloadProgress: number = 0;
  downloadComplete: boolean = false;
  downloadFailed: boolean = false;
  activeStep: number = 0;
  estimatedTimeRemaining = 0;
  showChatBot: boolean = true; 
  selectedAITab: number = 0; 


  constructor(
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private sharedData: ShareDataService,
    private notify : NotificationService,
    private masterDataService: MasterDataService,
    private api: InventoryService,
    private auth: AuthService,
    private cd: ChangeDetectorRef,
    @Optional() @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private snackBar: MatSnackBar
  ){
    this.user = this.auth.getCurrentUser();
    this.baseData = this.sharedData.getBaseData().value;

    if (this.dialogData) {
      this.isDuplicate = this.dialogData.key;
    } else {
      this.isDuplicate = false;
    }
    this.registrationForm = this.fb.group({
      tenantId: new FormControl<string>('', Validators.required,),
      tenantName: new FormControl<string>('', Validators.required),
      emailId: new FormControl<string>('', Validators.required),
      gSheet: new FormControl<string>('', Validators.required),
      accountNo: new FormControl<string>('', Validators.required),
      password: new FormControl<string>('', Validators.required),
      account: new FormControl<string>('no', Validators.required),
      forecast: new FormControl<string>('no', Validators.required),
      sales: new FormControl<string>('no', Validators.required),
      logo: new FormControl<string>('', Validators.required),
    }) as FormGroup;

  }

  ngOnInit() {
    this.isEditMode = false;

    if (this.dialogData && this.dialogData.key == false) {
      this.isEditMode = true;
      this.prefillData(this.dialogData.elements);
    } else {
      this.route.queryParams.subscribe(params => {
        if (params['id']) {
          this.isEditMode = true;
          this.isLoading = true;
          this.cd.detectChanges();

          this.api.getAccountById(params['id']).subscribe({
            next: (res) => {
              if (res.success && res.data) {
                this.prefillData(res.data);
              } else {
                this.isEditMode = false;
                this.notify.snackBarShowError('Account not found');
                this.router.navigate(['/dashboard/account']);
              }
              this.isLoading = false;
              this.cd.detectChanges();
            },
            error: (err) => {
              this.isEditMode = false;
              console.error('Error fetching account data:', err);
              this.notify.snackBarShowError('Failed to load account data');
              this.router.navigate(['/dashboard/account']);
              this.isLoading = false;
              this.cd.detectChanges();
            }
          });
        }
      });
    }
  }

  prefillData(data) {
    this.registrationForm.patchValue({
      tenantName: data.tenantName,
      tenantId: data.tenantId,
      emailId: data.emailId,
      gSheet: data.gSheet,
      accountNo: data.accountNo,
      password: data.password,
      account: data.status.account ? 'yes' : 'no',
      forecast: data.status.forecast ? 'yes' : 'no',
      sales: data.status.sales ? 'yes' : 'no',
      logo: data.tenantDetails?.logo || ''
    })
    if (data.tenantDetails?.logo) {
      this.logoUrl = data.tenantDetails.logo;
      this.selectedFiles = [{
        url: data.tenantDetails.logo
      }];
    }
    this.cd.detectChanges();
  }

  close() {
    this.router.navigate(['/dashboard/account']);
  }

  save() {
    if (this.registrationForm.invalid) {
      this.registrationForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields');
    } else {
      let detail = this.registrationForm.value;
      let obj: any = {
            tenantId: detail.tenantId,
            tenantName: detail.tenantName,
            accountNo: detail.accountNo,
            emailId: detail.emailId,
            password: detail.password,
            gSheet: detail.gSheet,
            status: {
              account: detail.account == 'yes',
              forecast: detail.forecast == 'yes',
              sales: detail.sales == 'yes'
            },
            tenantDetails: {
            logo: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null
            }
      };
      this.api.saveAccount(obj).subscribe({
        next: (res) => {
          if (res.success) {
            this.notify.snackBarShowSuccess(this.isEditMode ? 'Account Updated Successfully' : 'Account Created Successfully');
            this.tenantCreated = true;
            this.aiDataAvailable = true;
            this.router.navigate(['/dashboard/account']);
            this.masterDataService.triggerRefreshTable();
          } else {
            this.notify.snackBarShowError('Something went wrong!');
          }
        },
        error: (err) => {
          console.log(err);
        }
      });
    }
  }


  checkTenantId(filterValue){
    filterValue = filterValue.target.value;
    let data = this.sharedData.getBaseData().value;
    const isItemAvailable = data.some(item => item.tenantId  === filterValue);
  if (isItemAvailable) {
    this.registrationForm.get('tenantId').setErrors({ 'tenantIdExists': true });
  } else {
    this.registrationForm.get('tenantId').setErrors(null);
  }
  }

  checkAccountNo(filterValue){
    filterValue = filterValue.target.value;
    let data = this.sharedData.getBaseData().value;
    const isItemAvailable = data.some(item => item.accountNo  === filterValue);
    if (isItemAvailable) {
      this.registrationForm.get('accountNo').setErrors({ 'accountNoExists': true });
    } else {
      this.registrationForm.get('accountNo').setErrors(null);
    }
  }

  checkGSheet(filterValue){
    filterValue = filterValue.target.value;
    let data = this.sharedData.getBaseData().value;
    const isItemAvailable = data.some(item => item.gSheet  === filterValue);
    if (isItemAvailable) {
      this.registrationForm.get('gSheet').setErrors({ 'gSheetExists': true });
    } else {
      this.registrationForm.get('gSheet').setErrors(null);
    }
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      this.selectedFiles = [];
      this.loadSpinnerForLogo = true;
      Array.from(input.files).forEach(file => {
        if (!file.type.startsWith('image/')) return;
        const reader = new FileReader();
        reader.onload = (e: any) => {
          const img = new Image();
          img.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
            canvas.width = 140;
            canvas.height = 140;
            ctx.drawImage(img, 0, 0, 140, 140);
            const pngUrl = canvas.toDataURL('image/png');
            this.logoUrl = pngUrl;
            this.registrationForm.patchValue({ logo: this.logoUrl });
            this.selectedFiles.push({ url: pngUrl });
            this.loadSpinnerForLogo = false;
            this.cd.detectChanges();
          };
          img.src = e.target.result;
        };
        reader.readAsDataURL(file);
      });
    }
  }


  resetSteps(): void {
    this.downloadSteps.forEach(step => step.completed = false);
    this.activeStep = -1;
  }


  startAIProcessing(): void {
    this.isDownloading = true;
    this.downloadProgress = 0;
    this.estimatedTimeRemaining = 0;
    this.downloadComplete = false;
    this.downloadFailed = false;
    this.resetSteps();

    this.api.startProcessing(this.registrationForm.value.tenantId).pipe(
      catchError(_error => {
        this.handleDownloadError('Failed to start processing. Please try again.');
        return of(null);
      })
    ).subscribe((response: any) => {
      if (response && response.success) {
        this.processingId = response.processingId;
        this.startStatusPolling(this.registrationForm.value.tenantId);
      } else {
        this.handleDownloadError('Failed to initialize processing. Please try again.');
      }
    });
  }

  startStatusPolling(tenantId: string): void {
    this.statusPolling = interval(10000).pipe(
      startWith(0), 
      switchMap(() => this.api.getStatus(tenantId)),
      takeWhile((response: any) => {
        return response && response.status !== 'complete' && response.status !== 'failed';
      }, true)
    ).subscribe((response: any) => {
      if (!response) {
        this.handleDownloadError('Lost connection to server. Please try again.');
        return;
      }

      if (response.status === 'failed') {
        this.handleDownloadError(response.message || 'Processing failed. Please try again.');
        return;
      }

      this.downloadProgress = response.progress || 0;
      this.estimatedTimeRemaining = response.estimated_time_remaining || 0;

      if (response.currentStep !== undefined && response.currentStep >= 0) {
        this.activeStep = response.currentStep;

        for (let i = 0; i <= response.currentStep; i++) {
          if (i < this.downloadSteps.length) {
            this.downloadSteps[i].completed = true;
          }
        }
      }

      this.cd.detectChanges();

      if (response.status === 'complete') {
        this.completeDownload();
      }
    }, _error => {
      this.handleDownloadError('Error communicating with server. Please try again.');
    });
  }

  onTabChanged(event: MatTabChangeEvent): void {
    if (event.index === 1) {
      this.startStatusPolling(this.registrationForm.value.tenantId);
    }
  }

  completeDownload(): void {
    if (this.statusPolling) {
      this.statusPolling.unsubscribe();
    }

    this.isDownloading = false;
    this.downloadComplete = true;
    this.cd.detectChanges();
    this.snackBar.open('Tenant data is ready for download!', 'Close', {
      duration: 5000,
      horizontalPosition: 'center',
      verticalPosition: 'bottom'
    });
  }

  handleDownloadError(message: string): void {
    if (this.statusPolling) {
      this.statusPolling.unsubscribe();
    }

    this.isDownloading = false;
    this.downloadFailed = true;
    this.snackBar.open(message, 'Retry', {
      duration: 10000,
      horizontalPosition: 'center',
      verticalPosition: 'bottom'
    }).onAction().subscribe(() => {
      this.startAIProcessing();
    });
  }


  downloadAll(): void {
    this.api.downloadData('all', this.registrationForm.value.tenantId).subscribe(
      (base64Data: string) => {
        try {
          const cleanBase64Data = base64Data.replace(/[^A-Za-z0-9+/=]/g, '');
          const binaryString = atob(cleanBase64Data);
          const bytes = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }

          const blob = new Blob([bytes], { type: 'application/zip' });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${this.registrationForm.value.tenantId}_inventory.zip`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);

          this.snackBar.open(
            "All files downloaded successfully!",
            "Close",
            { duration: 3000 }
          );
        } catch (error) {
          console.error("Base64 Decoding or ZIP Error:", error);
          this.snackBar.open(
            "Failed to download files. Please try again.",
            "Close",
            { duration: 3000 }
          );
        }
      },
      (error) => {
        console.error("API Error:", error);
        this.snackBar.open(
          "Failed to download files. Please try again.",
          "Close",
          { duration: 3000 }
        );
      }
    );
  }

  ngOnDestroy(): void {
    if (this.statusPolling) {
      this.statusPolling.unsubscribe();
    }
  }

  }

