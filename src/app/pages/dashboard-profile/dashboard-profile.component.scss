.list-item-content {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
}

.bold {
  font-weight: bold;
}

.cursor-pointer {
  cursor: pointer;
}

.error-message {
  margin-top: 1rem;
  color: red;
}

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.form-check-input[type="radio"] {
  width: 22px; 
  height: 22px; 
}

.form-check-input[type="radio"] {
  border: 2px solid #000; 
  margin-right: 8px; 
  margin-bottom: 5px;
}

.ipText{
  text-align: center;
  font-size: medium;
  font-weight: bold;
}

.emailAccessIpText{
  width: 200px;
  font-size: medium;
  font-weight: bold;
}

.example-spacer {
  flex: 1 1 auto;
}

mat-form-field {
  width: 100%;
}
.custom-select .mat-select-trigger {
  position: relative !important;
}
.custom-dropdown .mat-select-trigger {
  top: unset !important;
  bottom: 0 !important;
}
.center-label {
  margin-left: 10px; 
  margin-top: 0px; /* Adjust as needed */
  margin-bottom: 10px; /* Horizontally center the content */
}

.mat-form-field-wrapper {
  display: flex;
  max-width: 100%; 
  align-items: center; /* Vertically center the content */
  justify-content: center; /* Horizontally center the content */
}
.placeholder-label {
  color: rgba(0, 0, 0, 0.6); /* Adjust placeholder color if needed */
  pointer-events: none; /* Ensure the label doesn't intercept clicks */
}

.cardContent{
  display: block;
  padding: 0px;
}

.tabGroup{
  padding: 5px;
}

.section {
  width: 100%;
  overflow-y: auto;
}

.datePicker {
  width: 300px;
  padding: 30px 10px 5px 0px;
}

.refreshButton{
  padding-top: 20px;
  padding-bottom: 10px;
  float: right;
}

.refresh{
  height: 3rem !important;
}

.gap{
  margin-left: 10px;
  margin-bottom: 10px;
  margin-top: 20px;
}

.end{
  float: right;
  margin-top: 38px !important;
}