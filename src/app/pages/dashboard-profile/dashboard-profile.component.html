<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header card-header-danger">
            <app-background-image-card-header icon="settings" title="Profile Setting" />
          </div>
          <div class="card-body">
            <mat-tab-group mat-stretch-tabs="false" #tabGroup mat-align-tabs="start" (selectedTabChange)="onTabChange($event)">
              <mat-tab label="PERSONAL INFO">
                <mat-card appearance="outlined">
                  <mat-card-content class="mat-card-content">
                    <br>
                      <mat-list>
                        <mat-list-item>
                          <div class="list-item-content">
                            <mat-icon>person</mat-icon>
                            <div>
                              <span class="bold">Name: </span>
                              <span>{{user.name}}</span>
                            </div>
                          </div>
                        </mat-list-item>
                        <mat-list-item>
                          <div class="list-item-content">
                            <mat-icon>bookmark</mat-icon>
                            <div>
                              <span class="bold">Role: </span>
                              <span>{{user.role}}</span>
                            </div>
                          </div>
                        </mat-list-item>
                        <mat-list-item>
                          <div class="list-item-content">
                            <mat-icon>alternate_email</mat-icon>
                            <div>
                              <span class="bold">Email: </span>
                              <span>{{user.email}}</span>
                            </div>
                          </div>
                        </mat-list-item>
                      </mat-list>
                  </mat-card-content>
                </mat-card>
              </mat-tab>
              <mat-tab label="IP CONFIG">
                <mat-card appearance="outlined">
                  <mat-card-content class="mat-card-content">
                    <br>
                    <div class="col">
                      <label class="ipText">Do you want to enable IP restriction ?</label>
                      <mat-radio-group [(ngModel)]="IpType" (change)="radioChange()">
                        <mat-radio-button value="withIp">Yes</mat-radio-button>
                        <mat-radio-button value="withoutIp">No</mat-radio-button>
                      </mat-radio-group>
                    </div>

                    <form (ngSubmit)="onSubmit()" class="my-4 mx-auto" *ngIf="showIpProcess">
                      <div class="mb-3">
                        <mat-card-title> Your current IP -<strong> {{currentIp}}</strong> </mat-card-title>
                        <mat-card-title class="mt-3"> <strong>Choose an option </strong> </mat-card-title>
                        <div class="row mt-3">
                          <div class="col-md-4">
                            <div class="form-check">
                              <input class="form-check-input" type="radio" name="option" id="option1" value="individual"
                                [(ngModel)]="selectedOption">
                              <label class="form-check-label fw-normal cursor-pointer" for="option1">This IP address or
                                subnet</label>
                            </div>
                          </div>
                          <div class="col-md-4">
                            <div class="form-check">
                              <input class="form-check-input" type="radio" name="option" id="option2" value="range"
                                [(ngModel)]="selectedOption">
                              <label class="form-check-label fw-normal cursor-pointer" for="option2">This IP address
                                range</label>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div *ngIf="selectedOption === 'individual'" class="mb-3">
                        <label for="individual" class="form-label">This IP address or subnet:</label>
                        <input type="text" id="individual" name="individual" [(ngModel)]="ipOrSubnet"
                          class="form-control" (ngModelChange)="validateInput()">
                        <div *ngIf="!isIPValid">
                          <p class="error-message">Invalid IP address</p>
                        </div>
                      </div>

                      <div *ngIf="selectedOption === 'range'" class="row mb-3 mt-3">
                        <div class="col-md-6">
                          <label for="startIP" class="form-label">From:</label>
                          <input type="text" id="startIP" name="startIP" [(ngModel)]="startIP" class="form-control"
                            (ngModelChange)="validateInput()">
                          <div *ngIf="!ipPattern.test(this.startIP)">
                            <p class="error-message">Invalid IP address</p>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <label for="endIP" class="form-label">To:</label>
                          <input type="text" id="endIP" name="endIP" [(ngModel)]="endIP" class="form-control"
                            (ngModelChange)="validateInput()">
                          <div *ngIf="!ipPattern.test(this.endIP)">
                            <p class="error-message">Invalid IP address</p>
                          </div>
                        </div>
                      </div>
                      <button type="submit" class="btn btn-primary float-end mb-3" [disabled]="!isIPValid"
                        matTooltip="Invalid IP Address"
                        *ngIf="selectedOption === 'individual' || selectedOption === 'range'">Submit</button>
                    </form>
                    <div *ngIf="!showIpProcess">
                      <button type="submit" class="btn btn-primary float-end mb-3" matTooltip="Invalid IP Address"
                        (click)="onSubmit()">Submit</button>
                    </div>
                  </mat-card-content>
                </mat-card>
              </mat-tab>
              <mat-tab label="SALES">
                <mat-card appearance="outlined">
                  <mat-card-content class="mat-card-content">
                    <br>
                    <form (ngSubmit)="addConfig()" class="my-4 mx-auto" style="max-width: 500px;">
                      <!-- <div class="row mb-3 mt-3">
                        <div class="col-md-6">
                          <label class="form-label">Profit Target(%)</label>
                          <input type="number" name="Profit Target" [(ngModel)]="profitTarget" class="form-control"
                            (ngModelChange)="validateSales()" min="1">
                        </div>
                      </div> -->
                      <div *ngIf="salesLocation" class="row mb-3 mt-3">
                        <div class="col-md-6">
                          <mat-form-field appearance="outline" class="custom-form-field">
                            <mat-label>Location</mat-label>
                            <mat-select [formControl]="branchData" (selectionChange)="getPriceTires()" panelClass="custom-dropdown">
                              <!-- <mat-label class="center-label"*ngIf="showPlaceholderLabel">Search ....</mat-label>
                              <input matInput [formControl]="vendorFilterCtrl" #inputField (keydown.space)="$event.stopPropagation()" (input)="togglePlaceholderLabel(inputField.value)"style="z-index: 1; padding-left: 10px;"> -->
                              <mat-option *ngFor="let option of filteredBranches" [value]="option.restaurantIdOld">
                                {{ option.branchName | uppercase}}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>
                      </div>

                      <div  class="row mb-3 mt-3">
                        <div class="col-md-6">
                          <mat-form-field appearance="outline">
                            <mat-label>Price Tier</mat-label>
                            <mat-select ng [formControl]="priceData">
                              <mat-option *ngFor="let option of priceTierList" [value]="option" >
                                {{option['name'] | titlecase}}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>
                      </div>
                      <button type="submit" class="btn btn-primary float-end mb-3">Submit</button>
                    </form>
                  </mat-card-content>
                </mat-card>
              </mat-tab>
              <mat-tab label="ACCESS" >
                <mat-card appearance="outlined">
                  <mat-card-content class="mat-card-content">
                    <br>
                    <form (ngSubmit)="submitPermission()" [formGroup]="accessForm" class="my-4 mx-auto" style="max-width: 500px;">
                        <div>
                          <h4>
                            Setting
                          </h4>
                          <mat-form-field appearance="outline">
                            <mat-label>Roles for setting</mat-label>
                            <mat-select formControlName="selectedSettingRoles" [(ngModel)]="selectedSettingRoles" multiple (ngModelChange)="validateSales()">
                              <mat-option *ngFor="let option of roles" [value]="option" [disabled]="option === 'superadmin'">
                                {{option | titlecase}}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>

                        <div>
                          <h4>
                            Excel Upload
                          </h4>
                          <mat-form-field appearance="outline">
                            <mat-label>Roles for Excel Upload</mat-label>
                            <mat-select formControlName="selectedExcelRoles" [(ngModel)]="selectedExcelRoles" multiple (ngModelChange)="validateSales()">
                              <mat-option *ngFor="let option of roles" [value]="option" [disabled]="option === 'superadmin'">
                                {{option | titlecase}}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>
                      <button type="submit" class="btn btn-primary float-end mb-3">Submit</button>
                    </form>

                  </mat-card-content>
                </mat-card>
              </mat-tab>
              <mat-tab label="PERMISSION">
                <mat-card appearance="outlined">
                  <mat-tab-group>
                    <mat-tab label="Module Access">
                      <mat-card-content class="mat-card-content">
                        <form (ngSubmit)="updateModule()" [formGroup]="moduleForm" class="my-4 mx-auto" style="max-width: 500px;">
                          <h1><b>Inventory Management</b></h1>
                          <div class="col">
                            <label class="ipText">Inventory Status</label>
                            <mat-radio-group formControlName="inventoryStatusButton" class="col" (change)="inventoryStatusShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showInventoryStatusField">
                            <mat-form-field appearance="outline">
                              <mat-label>Inventory Access</mat-label>
                              <mat-select formControlName="inventory_Access" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="inventoryFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllInventory()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of inventory | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                          <br>
                          <h1><b>User & Branch Management</b></h1>
                          <div class="col">
                            <label class="ipText">User Status</label>
                            <mat-radio-group formControlName="userStatusButton" class="col" (change)="userStatusShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showUserStatusField">
                            <mat-form-field appearance="outline">
                              <mat-label>User Access</mat-label>
                              <mat-select formControlName="user_Access" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="userFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllUser()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of users | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                          <br>
                          <h1><b>Recipe Management</b></h1>
                          <div class="col">
                            <label class="ipText">Recipe Status</label>
                            <mat-radio-group formControlName="recipeStatusButton" class="col" (change)="recipeStatusShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showRecipeStatusField">
                            <mat-form-field appearance="outline">
                              <mat-label>Recipe Access</mat-label>
                              <mat-select formControlName="recipe_Access" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="recipeFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllRecipe()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of recipe | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                          <br>
                          <h1><b>Party Management</b></h1>
                          <div class="col">
                            <label class="ipText">Party Status</label>
                            <mat-radio-group formControlName="partyStatusButton" class="col" (change)="partyStatusShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showPartyStatusField">
                            <mat-form-field appearance="outline">
                              <mat-label>Party Access</mat-label>
                              <mat-select formControlName="party_Access" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="partyFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllParty()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of party | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                          <button type="submit" class="btn btn-primary float-end mb-3">Submit</button>
                        </form>
                      </mat-card-content>
                    </mat-tab>

                    <mat-tab label="GRN Access">
                      <mat-card-content class="mat-card-content">
                        <form (ngSubmit)="updateGRN()" [formGroup]="grnForm" class="my-4 mx-auto" style="max-width: 500px;">
                          <div class="col">
                            <label class="ipText">Delete</label>
                            <mat-radio-group formControlName="deleteButtonGRN" class="col" (change)="isDeleteGrnShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showDeleteGRNField">
                            <mat-form-field appearance="outline">
                              <mat-label>Delete Access</mat-label>
                              <mat-select formControlName="delete_GRN" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="deleteGRNFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllDeleteGRN()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of deleteGRN | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                          <div class="col">
                            <label class="ipText">Edit</label>
                            <mat-radio-group formControlName="editButtonGRN" class="col" (change)="isEditGrnShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showEditGRNField">
                            <mat-form-field appearance="outline">
                              <mat-label>Edit Access</mat-label>
                              <mat-select formControlName="edit_GRN" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="editGRNFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllEditGRN()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of editGRN | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>

                          <div class="col">
                            <label class="ipText">RTV</label>
                            <mat-radio-group formControlName="closeButtonGRN" class="col" (change)="isCloseGrnShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showCloseGRNField">
                            <mat-form-field appearance="outline">
                              <mat-label>RTV Access</mat-label>
                              <mat-select formControlName="close_GRN" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="closeGRNFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllCloseGRN()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of closeGRN | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                          <button type="submit" class="btn btn-primary float-end mb-3">Submit</button>
                        </form>
                      </mat-card-content>
                    </mat-tab>

                    <mat-tab label="PO Access">
                      <mat-card-content class="mat-card-content">
                        <form (ngSubmit)="updatePO()" [formGroup]="poForm" class="my-4 mx-auto" style="max-width: 500px;">
                          <div class="col">
                            <label class="ipText">Delete</label>
                            <mat-radio-group formControlName="deleteButtonPO" class="col" (change)="isDeletePoShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showDeletePOField">
                            <mat-form-field appearance="outline">
                              <mat-label>Delete Access</mat-label>
                              <mat-select formControlName="delete_PO" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="deletePOFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllDeletePO()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of deletePO | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                          <div class="col">
                            <label class="ipText">Edit</label>
                            <mat-radio-group formControlName="editButtonPO" class="col" (change)="isEditPoShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showEditPOField">
                            <mat-form-field appearance="outline">
                              <mat-label>Edit Access</mat-label>
                              <mat-select formControlName="edit_PO" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="editPOFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllEditPO()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of editPO | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>

                          <div class="col">
                            <label class="ipText">Close</label>
                            <mat-radio-group formControlName="closeButtonPO" class="col" (change)="isClosePoShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showClosePOField">
                            <mat-form-field appearance="outline">
                              <mat-label>Close Access</mat-label>
                              <mat-select formControlName="close_PO" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="closePOFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllClosePO()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of closePO | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>

                          <button type="submit" class="btn btn-primary float-end mb-3">Submit</button>
                        </form>
                      </mat-card-content>
                    </mat-tab>

                    <mat-tab label="Indent Access">
                      <mat-card-content class="mat-card-content">
                        <form (ngSubmit)="updateIndent()" [formGroup]="indentForm" class="my-4 mx-auto" style="max-width: 500px;">
                          <div class="col">
                            <label class="ipText">Delete</label>
                            <mat-radio-group formControlName="deleteButtonIndent" class="col" (change)="isDeleteIndentShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showDeleteIndentField">
                            <mat-form-field appearance="outline">
                              <mat-label>Delete Access</mat-label>
                              <mat-select formControlName="delete_Indent" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="deleteIndentFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllDeleteIndent()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of deleteIndent | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                  <!-- do not remove -->
                          <!-- <div class="col">
                            <label class="ipText">Edit</label>
                            <mat-radio-group formControlName="editButtonIndent" class="col" (change)="isEditIndentShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showEditIndentField">
                            <mat-form-field appearance="outline">
                              <mat-label>Edit Access</mat-label>
                              <mat-select formControlName="edit_Indent" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="editIndentFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllEditIndent()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of editIndent | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>                  -->
                          <div class="col">
                            <label class="ipText">Close</label>
                            <mat-radio-group formControlName="closeButtonIndent" class="col" (change)="isCloseIndentShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div *ngIf="showCloseIndentField">
                            <mat-form-field appearance="outline">
                              <mat-label>Close Access</mat-label>
                              <mat-select formControlName="close_Indent" multiple (ngModelChange)="validateSales()">
                                <mat-option>
                                  <ngx-mat-select-search [formControl]="closeIndentFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                </mat-option>
                                <mat-option class="hide-checkbox" (click)="toggleSelectAllCloseIndent()">
                                  <mat-icon matSuffix>check_circle</mat-icon>
                                  Select All / Deselect All
                                </mat-option>
                                <mat-option *ngFor="let option of closeIndent | async" [value]="option">
                                  {{option | titlecase}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                          <div class="col">
                            <label class="ipText">Partial Indent</label>
                            <mat-radio-group formControlName="partialButtonIndent" class="col" (change)="isPartialIndentShown($event)">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <button type="submit" class="btn btn-primary float-end mb-3">Submit</button>
                        </form>
                      </mat-card-content>
                    </mat-tab>

                    <mat-tab label="Email Access">
                      <mat-card-content class="mat-card-content">
                        <form (ngSubmit)="updateEmail()" [formGroup]="emailForm" class="my-4 mx-auto" style="max-width: 500px;">
                          <div class="col">
                            <label class="emailAccessIpText">Purchase Request</label>
                            <mat-radio-group formControlName="purchaseRequestButton" class="col">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div class="col">
                            <label class="emailAccessIpText">Post Grn Status</label>
                            <mat-radio-group formControlName="postGRNStatusButton" class="col">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div class="col">
                            <label class="emailAccessIpText">Indent Approval</label>
                            <mat-radio-group formControlName="indentApprovalButton" class="col">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div class="col">
                            <label class="emailAccessIpText">Purchase Approval</label>
                            <mat-radio-group formControlName="purchaseApprovalButton" class="col">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
                          <div class="col">
                            <label class="emailAccessIpText">Purchase Order</label>
                            <mat-radio-group formControlName="purchaseOrderButton" class="col">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <br>
      
                          <div class="col">
                            <label class="emailAccessIpText">PI Approval</label>
                            <mat-radio-group formControlName="piApprovalButton" class="col">
                              <mat-radio-button value="Yes">Yes</mat-radio-button>
                              <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                          </div>
                          <button type="submit" class="btn btn-primary float-end mb-3">Submit</button>
                        </form>
                      </mat-card-content>
                    </mat-tab>

                    <mat-tab label="Report Access">
                      <mat-tab-group class="tabGroup" >
                        <mat-tab label="Report Based">
                          <mat-card-content class="mat-card-content">
                            <form (ngSubmit)="updateReport()" [formGroup]="reportForm" class="my-4 mx-auto" style="max-width: 500px;">
                              <mat-form-field appearance="outline">
                                <mat-label>Select Report</mat-label>
                                <mat-select formControlName="reportAccess" (ngModelChange)="reportChange($event)">
                                  <mat-option>
                                    <ngx-mat-select-search [formControl]="reportFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                  </mat-option>
                                  <mat-option *ngFor="let option of report | async" [value]="option">
                                    {{option | titlecase}}
                                  </mat-option>
                                </mat-select>
                              </mat-form-field>

                              <mat-accordion multi="true">
                                <mat-expansion-panel *ngFor="let report of filteredReports">
                                  <mat-expansion-panel-header>
                                    <mat-panel-title>
                                      {{ report.displayName | uppercase }} REPORT
                                    </mat-panel-title>
                                  </mat-expansion-panel-header>
                                  <mat-form-field appearance="outline">
                                    <mat-label>Select Roles</mat-label>
                                    <mat-select formControlName="report_Access" multiple >
                                      <mat-option>
                                        <ngx-mat-select-search [formControl]="report_FilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                      </mat-option>
                                      <mat-option class="hide-checkbox" (click)="selectAllRolesForReport()">
                                        <mat-icon matSuffix>check_circle</mat-icon>
                                        Select All / Deselect All
                                      </mat-option>
                                      <mat-option *ngFor="let option of reports | async" [value]="option">
                                        {{option | titlecase}}
                                      </mat-option>
                                    </mat-select>
                                  </mat-form-field>
                                </mat-expansion-panel>
                              </mat-accordion>
                              <br>
                              <button type="submit" class="btn btn-primary float-end mb-3">Submit</button>
                            </form>
                          </mat-card-content>
                        </mat-tab>

                        <mat-tab label="Role Based">
                          <mat-card-content class="mat-card-content">
                            <form (ngSubmit)="updateReport(false)" [formGroup]="roleForm" class="my-4 mx-auto" style="max-width: 500px;">
                              <mat-form-field appearance="outline">
                                <mat-label>Select Role</mat-label>
                                <mat-select formControlName="report_Access" (ngModelChange)="roleChange($event)">
                                  <mat-option>
                                    <ngx-mat-select-search [formControl]="report_FilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                  </mat-option>
                                  <mat-option *ngFor="let option of reports | async" [value]="option">
                                    {{option | titlecase}}
                                  </mat-option>
                                </mat-select>
                              </mat-form-field>

                              <mat-accordion multi="true" *ngIf="roleForm.get('report_Access').value">
                                <mat-expansion-panel>
                                  <mat-expansion-panel-header>
                                    <mat-panel-title>
                                      {{ filteredRole | uppercase }}
                                    </mat-panel-title>
                                  </mat-expansion-panel-header>
                                  <mat-form-field appearance="outline">
                                    <mat-label>Select Report</mat-label>
                                    <mat-select formControlName="reportAccess" multiple >
                                      <mat-option>
                                        <ngx-mat-select-search [formControl]="reportFilterCtrl" placeholderLabel="Search..."></ngx-mat-select-search>
                                      </mat-option>
                                      <mat-option class="hide-checkbox" (click)="selectAllReportsForRoles()">
                                        <mat-icon matSuffix>check_circle</mat-icon>
                                        Select All / Deselect All
                                      </mat-option>
                                      <mat-option *ngFor="let option of report | async" [value]="option">
                                        {{option | titlecase}}
                                      </mat-option>
                                    </mat-select>
                                  </mat-form-field>
                                </mat-expansion-panel>
                              </mat-accordion>
                              <br>
                              <button type="submit" class="btn btn-primary float-end mb-3">Submit</button>
                            </form>
                          </mat-card-content>
                        </mat-tab>
                      </mat-tab-group>
                    </mat-tab>

                  </mat-tab-group>
                </mat-card>
              </mat-tab>
              <mat-tab label="FREEZE CLOSING OPERATIONS">
                <mat-card appearance="outlined">
                  <mat-card-content class="mat-card-content">
                    <form (ngSubmit)="submit()" class="my-4 mx-auto" style="max-width: 500px;">
                      <!-- Location selection -->
                      <div>
                        <div class="row mb-3">
                          <div class="col-md-6">
                            <mat-form-field appearance="outline" class="custom-form-field">
                              <mat-label>Select a Location</mat-label>

                              <mat-select [formControl]="branchData" (selectionChange)="onLocationChange($event)" panelClass="custom-dropdown">
                                <mat-label class="center-label" *ngIf="showPlaceholderLabel">Search ....</mat-label>
                                <input matInput [formControl]="vendorFilterCtrl" #inputField (keydown.space)="$event.stopPropagation()" (input)="togglePlaceholderLabel(inputField.value)" style="z-index: 1; padding-left: 10px;">
                                <mat-option *ngFor="let option of filteredBranches" [value]="option.restaurantIdOld">
                                  {{ option.branchName | uppercase }}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                          </div>
                        </div>
                      </div>

                      <!-- Date option selection -->
                      <div *ngIf="selectedLocation">
                        <div class="col mb-3">
                          <label class="ipText">Do you want to pick a date?</label>
                          <mat-radio-group [(ngModel)]="pickDateOption" name="pickDateOption">
                            <mat-radio-button value="yes">Yes</mat-radio-button>
                            <mat-radio-button value="no">No</mat-radio-button>
                          </mat-radio-group>
                        </div>
                      </div>

                      <!-- Monthly closing date dropdown -->
                      <div *ngIf="pickDateOption === 'yes'" class="mb-3">
                        <label for="monthlyDate" class="form-label">Select a monthly closing date:</label>
                        <mat-form-field appearance="outline" class="custom-form-field">
                          <mat-label>Monthly Date</mat-label>
                          <mat-select [(ngModel)]="selectedDateOption" name="monthlyDate" (selectionChange)="onDateSelectionChange($event.value)">
                            <mat-option [value]="null">--Select--</mat-option>
                            <mat-option value="startOfMonth">Starting of the Month</mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>

                      <!-- Submit button only visible at final step -->
                      <div *ngIf="isFinalStep()" class="btn btn-primary float-end mb-3">
                        <button type="submit" class="btn btn-primary float-end">Submit</button>
                            </div>
                          </form>
                        </mat-card-content>
                </mat-card>
              </mat-tab>
              <mat-tab label="RETRIGGER">
                <mat-card appearance="outlined">
                  <mat-tab-group class="tabGroup" (selectedTabChange)="tabChange($event)">

                    <mat-tab label="SALES">
                      <mat-card-content class="cardContent">
                        <button (click)="refreshSales()" mat-raised-button color="warn" class="refresh end">Refresh</button>
                        <form (ngSubmit)="salesRerun()" [formGroup]="salesForm">
                          <div>
                            <mat-form-field appearance="outline" class="datePicker">
                              <mat-label>Start Date</mat-label>
                              <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                              <mat-datepicker-toggle matIconSuffix [for]="startPicker"></mat-datepicker-toggle>
                              <mat-datepicker touchUi #startPicker></mat-datepicker>
                            </mat-form-field>

                            <mat-form-field appearance="outline" class="datePicker">
                              <mat-label>End Date</mat-label>
                              <input matInput [matDatepicker]="endPicker" formControlName="endDate"
                              [disabled]="!salesForm.get('startDate').value" [min]="salesForm.get('startDate').value">
                              <mat-datepicker-toggle matIconSuffix [for]="endPicker"></mat-datepicker-toggle>
                              <mat-datepicker touchUi #endPicker></mat-datepicker>
                            </mat-form-field>

                            <button type="submit" mat-raised-button color="primary" class="refresh">Submit</button>
                          </div>

                          <div class="section" #section #widgetsContent>
                            <div class="tableDiv" *ngIf="isSalesDataReady">
                                <mat-table [dataSource]="dataSourceSales" matSort>

                                  <ng-container matColumnDef="position">
                                    <mat-header-cell *matHeaderCellDef class="tableSnoCol"> S.No </mat-header-cell>
                                    <mat-cell *matCellDef="let element; let i = index;" class="tableSnoCol"> {{i+1}} </mat-cell>
                                  </ng-container>

                                  <ng-container matColumnDef="createdDate">
                                    <mat-header-cell class="custom-header" *matHeaderCellDef> Sales Date </mat-header-cell>
                                    <mat-cell class="custom-cell" *matCellDef="let element"> {{element.createTs | date: 'dd-MM-yyyy'}} </mat-cell>
                                  </ng-container>

                                  <ng-container matColumnDef="createdTime">
                                    <mat-header-cell class="custom-header" *matHeaderCellDef> Created At </mat-header-cell>
                                    <mat-cell class="custom-cell" *matCellDef="let element">
                                      <ng-container *ngIf="element.triggeredTs || element.modTs; else noDate">
                                        {{ element.triggeredTs ? (element.triggeredTs | date: 'dd-MM-yyyy') : (element.modTs | date: 'dd-MM-yyyy') }}
                                        {{ element.triggeredTs ? convertToIST(element.triggeredTs) : convertToIST(element.modTs) }}
                                      </ng-container>
                                      <ng-template #noDate> - </ng-template>
                                    </mat-cell>
                                  </ng-container>

                                  <ng-container matColumnDef="status">
                                    <mat-header-cell class="custom-header" *matHeaderCellDef> Status </mat-header-cell>
                                    <mat-cell class="custom-cell" *matCellDef="let element">
                                      {{ getJobStatus(element) }}
                                    </mat-cell>
                                  </ng-container>

                                  <mat-header-row *matHeaderRowDef="salesColumns"></mat-header-row>
                                  <mat-row *matRowDef="let row; columns: salesColumns;"
                                    [ngClass]="{'highlighted-row': row.Discontinued === 'yes'}"></mat-row>
                                </mat-table>
                            </div>

                            <div *ngIf="!isSalesDataReady">
                              <ngx-skeleton-loader count="5" animation="progress-dark"
                                [theme]="{ 'border-radius': '5px', height: '30px' }"></ngx-skeleton-loader>
                            </div>

                            <div class="text-center m-3" *ngIf="dataSourceSales.data.length == 0 && isSalesDataReady">
                              Data Not Found
                            </div>
                            <mat-paginator class="mat-paginator-sticky" [pageSize]="10" [pageSizeOptions]="[5, 10, 25, 50, 100]" #salesPaginator></mat-paginator>
                          </div>

                        </form>
                      </mat-card-content>
                    </mat-tab>
                    <mat-tab label="WAC">
                      <mat-card-content class="cardContent">
                        <button (click)="refreshWac()" mat-raised-button color="warn" class="refresh float-end gap">Refresh</button>
                        <form (ngSubmit)="weightedAvg()" [formGroup]="wacForm">
                          <button type="submit" mat-raised-button color="primary" class="refresh float-end gap">Submit</button>
                          <div class="section" #section #widgetsContent>
                            <div class="tableDiv" *ngIf="isWacDataReady">
                                <mat-table [dataSource]="dataSourceWeightedAvg" matSort >
                                  <ng-container matColumnDef="position">
                                    <mat-header-cell *matHeaderCellDef class="tableSnoCol"> S.No </mat-header-cell>
                                    <mat-cell *matCellDef="let element; let i = index;" class="tableSnoCol"> {{i+1}} </mat-cell>
                                  </ng-container>

                                  <ng-container matColumnDef="createdTime">
                                    <mat-header-cell class="custom-header" *matHeaderCellDef> Created At </mat-header-cell>
                                    <mat-cell class="custom-cell" *matCellDef="let element">
                                      {{ element.createTs  | date: 'dd-MM-yyyy' }}
                                      {{ convertToIST(element.createTs) }}
                                    </mat-cell>
                                  </ng-container>

                                  <ng-container matColumnDef="status">
                                    <mat-header-cell class="custom-header" *matHeaderCellDef> Status </mat-header-cell>
                                    <mat-cell class="custom-cell" *matCellDef="let element">
                                      {{ getJobStatus(element) }}
                                    </mat-cell>
                                  </ng-container>

                                  <mat-header-row *matHeaderRowDef="weightedAvgColumns"></mat-header-row>
                                  <mat-row *matRowDef="let row; columns: weightedAvgColumns;"
                                    [ngClass]="{'highlighted-row': row.Discontinued === 'yes'}"></mat-row>
                                </mat-table>
                            </div>

                            <div *ngIf="!isWacDataReady">
                              <ngx-skeleton-loader count="5" animation="progress-dark"
                                [theme]="{ 'border-radius': '5px', height: '30px' }"></ngx-skeleton-loader>
                            </div>

                            <div class="text-center m-3" *ngIf="dataSourceWeightedAvg.data.length == 0 && isWacDataReady">
                              Data Not Found
                            </div>
                            <mat-paginator class="mat-paginator-sticky" [pageSize]="10" [pageSizeOptions]="[5, 10, 25, 50, 100]" #wacPaginator></mat-paginator>
                          </div>
                        </form>
                      </mat-card-content>
                    </mat-tab>
                    <mat-tab label="FORECAST">
                      <mat-card-content class="cardContent">
                        <button (click)="refreshForecast()" mat-raised-button color="warn" class="refresh float-end gap">Refresh</button>
                        <form (ngSubmit)="forecast()" [formGroup]="forecastForm">

                          <button type="submit" mat-raised-button color="primary" class="refresh float-end gap">Submit</button>

                          <div class="section" #section #widgetsContent>
                            <div class="tableDiv" *ngIf="isForecastReady">
                                <mat-table [dataSource]="dataSourceForecast" matSort >
                                  <ng-container matColumnDef="position">
                                    <mat-header-cell *matHeaderCellDef class="tableSnoCol"> S.No </mat-header-cell>
                                    <mat-cell *matCellDef="let element; let i = index;" class="tableSnoCol"> {{i+1}} </mat-cell>
                                  </ng-container>

                                  <ng-container matColumnDef="createdTime">
                                    <mat-header-cell class="custom-header" *matHeaderCellDef> Created At </mat-header-cell>
                                    <mat-cell class="custom-cell" *matCellDef="let element">
                                      {{ element.createTs  | date: 'dd-MM-yyyy' }}
                                      {{ convertToIST(element.createTs) }}
                                    </mat-cell>
                                  </ng-container>

                                  <ng-container matColumnDef="status">
                                    <mat-header-cell class="custom-header" *matHeaderCellDef> Status </mat-header-cell>
                                    <mat-cell class="custom-cell" *matCellDef="let element">
                                      {{ getJobStatus(element) }}
                                    </mat-cell>
                                  </ng-container>

                                  <mat-header-row *matHeaderRowDef="forecastColumns"></mat-header-row>
                                  <mat-row *matRowDef="let row; columns: forecastColumns;"
                                    [ngClass]="{'highlighted-row': row.Discontinued === 'yes'}"></mat-row>
                                </mat-table>
                            </div>

                            <div *ngIf="!isForecastReady">
                              <ngx-skeleton-loader count="5" animation="progress-dark"
                                [theme]="{ 'border-radius': '5px', height: '30px' }"></ngx-skeleton-loader>
                            </div>

                            <div class="text-center m-3" *ngIf="dataSourceForecast.data.length == 0 && isForecastReady">
                              Data Not Found
                            </div>
                            <mat-paginator class="mat-paginator-sticky" [pageSize]="10" [pageSizeOptions]="[5, 10, 25, 50, 100]" #forecastPaginator></mat-paginator>
                          </div>
                        </form>
                      </mat-card-content>
                    </mat-tab>

                  </mat-tab-group>
                </mat-card>
              </mat-tab>
            </mat-tab-group>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #showDialog>
  <div class="registration-form m-3 py-2 px-3">
    <div>
      <div class="row my-3">
        <div class="form-check col-6 justify-content-center">
          <input class="form-check-input" type="radio" name="option" id="option1" value="withoutIp"
            [(ngModel)]="IpType">
          <label class="form-check-label fw-normal cursor-pointer" for="option1">use Without IP</label>
        </div>
        <div class="form-check col-6 justify-content-center">
          <input class="form-check-input" type="radio" name="option" id="option2" value="withIp" [(ngModel)]="IpType">
          <label class="form-check-label fw-normal cursor-pointer" for="option2">Use With IP</label>
        </div>
      </div>
    </div>
    <div class="d-flex justify-content-end">
      <button mat-raised-button color="accent" matTooltip="update" (click)="submitIp()"> Ok </button>
    </div>
  </div>
</ng-template>