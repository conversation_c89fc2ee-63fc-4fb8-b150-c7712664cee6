import { ChangeDetectionStrategy, ChangeDetectorRef, Component, TemplateRef, ViewChild, inject , OnInit} from '@angular/core';
import { CommonModule , formatDate} from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from 'src/environments/environment';

import { BackgroundImageCardComponent } from 'src/app/components/background-image-card/background-image-card.component';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { MatCardModule } from '@angular/material/card';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Observable, ReplaySubject, Subject, first, map, startWith, takeUntil } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap  } from 'rxjs/operators';
import { AuthService } from 'src/app/services/auth.service';
import { MatTabChangeEvent, MatTabGroup, MatTabsModule } from '@angular/material/tabs';
import { AbstractControl, FormBuilder, FormControl,FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSliderModule } from '@angular/material/slider';
import { NgZone } from '@angular/core';	
import { BackgroundImageCardHeaderComponent } from 'src/app/components/background-image-card-header/background-image-card-header.component';
import { MatInputModule } from '@angular/material/input';
import { InventoryService } from 'src/app/services/inventory.service';
import { NotificationService } from 'src/app/services/notification.service';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MasterDataService } from 'src/app/services/master-data.service';
import { Router } from '@angular/router';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { GlobalsService } from 'src/app/services/globals.service';
import { ShareDataService } from 'src/app/services/share-data.service';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';

export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'DD/MM/YYYY',
  },
  display: {
    dateInput: 'DD/MM/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'DD/MM/YYYY',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-dashboard-profile',
  standalone: true,
  imports: [
    CommonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSliderModule,
    MatTabsModule,
    BackgroundImageCardComponent,
    MatButtonModule,
    FormsModule ,
    MatIconModule,
    BackgroundImageCardHeaderComponent, 
    MatDividerModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatListModule,
    MatRadioModule,
    MatFormFieldModule,
    MatInputModule,
    MatCardModule,
    MatDialogModule,
    MatSelectModule,
    NgxMatSelectSearchModule,
    MatExpansionModule,
    MatTableModule,
    MatPaginatorModule,
    NgxSkeletonLoaderModule
  ],
  providers: [
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS },
  ],
  templateUrl: './dashboard-profile.component.html',
  styleUrls: ['./dashboard-profile.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardProfileComponent {
  monthlyClosingDatesArray: { date: string, month: string }[] = [];
  submissionMessage: string;
  startingDate: Date;
  engineUrl: string = environment.engineUrl;

  // selectedDateOption: string; // Tracks selected date option
  startingDates: { [key: string]: Date } = {}; 
  showLocationSelection: boolean = true;
  selectedLocation: string = ''; 
  selectedDateOption: string | null = null;
  monthlyClosingDates: any[] = []; 
  selectedMonthDates: Date[] = [];
  selectedDay: Date | null = null;
  selectedDays: { [key: string]: Date | null } = {}; 

  pickDateOption: string = 'no';
  closingType: string = '';
  selectedDate: string = '';
  monthlyDate: string = ''; 
  fifteenDaysDate: string = ''; 
  // selectedLocation: boolean = false; // Indicates if a location has been selected
  @ViewChild('salesPaginator') salesPaginator: MatPaginator;
  @ViewChild('wacPaginator') wacPaginator: MatPaginator;
  @ViewChild('forecastPaginator') forecastPaginator: MatPaginator;
  @ViewChild('showDialog') dialogRef: TemplateRef<any>;
  public isReadOnly = false;
  public user: any;
  public userRole: any; 
  public currentIp :string;
	salesLocation:boolean=false		
  breakpointObserver = inject(BreakpointObserver);
  isSmallDevice$;
  ipPattern: RegExp = new RegExp("(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)");
  ipOrSubnet: string = '';
  startIP: string = '';
  endIP: string = '';
  selectedOption: string = 'individual';
  profitTarget: number = 1;
  priceTier: any ;
  isIPValid : boolean = false;
  priceData = new FormControl();
  branchData=new FormControl(null);	
  IpType: string = 'withoutIp'
  showIpProcess : boolean = false;
  showIpBtn : boolean = false;
  isSalesDataReady : boolean = false;
  isWacDataReady : boolean = false;
  isForecastReady : boolean = false;
  selectedSettingRoles = [] 
  selectedExcelRoles = []
  accessForm: any; 
  access: any;
  priceTierList: any[];
  branches: any[];	
  form: FormGroup;		
  object: any;
  vendorFilterCtrl: FormControl<string>;
  filteredBranches: any[];
  showPlaceholderLabel: boolean=true;
  roles: string[] = [];
  rolesIndent: string[] = [];		
	rolesGRN: string[] = [];		
	rolesPO: string[] = [];
  grnForm: any;
  poForm: any;
  indentForm: any;
  emailForm: any;
  moduleForm: any;
  reportForm: any;
  roleForm: any;
  salesForm: any;
  wacForm: any;
  forecastForm: any;
  inventory_Access = []
  user_Access = []
  dashboard_Access = []
  delete_GRN = []
  edit_GRN = []
  close_GRN = []
  delete_PO = []
  edit_PO = []
  close_PO = []
  delete_Indent = []
  edit_Indent = []
  close_Indent = []
  report_Access = []
  reportAccess = []
  showInventoryStatusField = false;
  showUserStatusField = false;
  showRecipeStatusField = false;
  showPartyStatusField = false;
  showDashboardStatusField = false;
  showDeleteGRNField = false;
  showEditGRNField = false;
  showCloseGRNField = false;
  showDeletePOField = false;
  showEditPOField = false;
  showClosePOField = false;
  showDeleteIndentField = false;
  showEditIndentField = false;
  showCloseIndentField = false;
  showPartialIndentField = false;
  responseGRN: any;
  responsePO: any;
  responseIndent: any;
  responseEmail: any;
  responseUIAccess: any;
  responseReport: any;
  displayNames: any;
  filteredReports: any[] = [];
  filteredRole: any;
  updatedReportAccess: { [key: string]: any } = {};
  salesColumns = ['position', 'createdDate','status','createdTime']
  dataSourceSales = new MatTableDataSource<any>([]);
  weightedAvgColumns = ['position','status','createdTime']
  dataSourceWeightedAvg = new MatTableDataSource<any>([]);
  forecastColumns = ['position','status','createdTime']
  dataSourceForecast = new MatTableDataSource<any>([]);

  public inventoryBank: any[] = [];
  public inventoryFilterCtrl: FormControl = new FormControl();
  public inventory: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  public userBank: any[] = [];
  public userFilterCtrl: FormControl = new FormControl();
  public users: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  public recipeBank: any[] = [];
  public recipeFilterCtrl: FormControl = new FormControl();
  public recipe: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  public partyBank: any[] = [];
  public partyFilterCtrl: FormControl = new FormControl();
  public party: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  public dashboardBank: any[] = [];
  public dashboardFilterCtrl: FormControl = new FormControl();
  public dashboard: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  public grnBank: any[] = [];
  public deleteGRNFilterCtrl: FormControl = new FormControl();
  public deleteGRN: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public editGRNFilterCtrl: FormControl = new FormControl();
  public editGRN: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public closeGRNFilterCtrl: FormControl = new FormControl();
  public closeGRN: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  public poBank: any[] = [];
  public deletePOFilterCtrl: FormControl = new FormControl();
  public deletePO: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public editPOFilterCtrl: FormControl = new FormControl();
  public editPO: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public closePOFilterCtrl: FormControl = new FormControl();
  public closePO: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  
  public indentBank: any[] = [];
  public deleteIndentFilterCtrl: FormControl = new FormControl();
  public deleteIndent: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public editIndentFilterCtrl: FormControl = new FormControl();
  public editIndent: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public closeIndentFilterCtrl: FormControl = new FormControl();
  public closeIndent: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  public reportBank: any[] = [];
  public reportFilterCtrl: FormControl = new FormControl();
  public report: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  public report_Bank: any[] = [];
  public report_FilterCtrl: FormControl = new FormControl();
  public reports: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  protected _onDestroy = new Subject<void>();
  InventoryService: any;
  // http: any;
  @ViewChild('tabGroup') tabGroup: MatTabGroup;
    		
    constructor(private shareDataService: ShareDataService,		
    private formBuilder: FormBuilder,		
    private ngZone: NgZone,
    private http: HttpClient,
    private api: InventoryService,
    private notify: NotificationService,
    private cd: ChangeDetectorRef,
    private sharedData: ShareDataService,
    private auth: AuthService,
    private dialog: MatDialog,
    private masterDataService: MasterDataService,
    private router: Router,
    private fb: FormBuilder,

    ) {
      this.accessForm = this.fb.group({
        selectedSettingRoles: [''],
        selectedExcelRoles: ['']
      });
      this.moduleForm = this.fb.group({
        inventoryStatusButton: [''],
        userStatusButton: [''],
        recipeStatusButton: [''],
        partyStatusButton: [''],
        dashboardStatusButton: [''],
        inventory_Access: [''],
        user_Access: [''],
        recipe_Access: [''],
        party_Access: [''],
        dashboard_Access: ['']
      });
      this.salesForm = this.fb.group({
        startDate: ['', Validators.required],
        endDate: ['', Validators.required]    
      });
      this.wacForm = this.fb.group({
        // startDate: [''],
      });
      this.forecastForm = this.fb.group({
        startDate: [''],
        endDate: ['']
      });
      this.grnForm = this.fb.group({
        delete_GRN: [''],
        edit_GRN: [''],
        close_GRN: [''],
        formType: ['grnAccess'],
        deleteButtonGRN: [''],
        editButtonGRN: [''],
        closeButtonGRN: ['']
      });
      this.poForm = this.fb.group({
        delete_PO: [''],
        edit_PO: [''],
        close_PO: [''],
        formType: ['POAccess'],
        deleteButtonPO: [''],
        editButtonPO: [''],
        closeButtonPO: ['']
      });
      this.indentForm = this.fb.group({
        delete_Indent: [''],
        edit_Indent: [''],
        close_Indent: [''],
        formType: ['indentAccess'],
        deleteButtonIndent: [''],
        editButtonIndent: [''],
        closeButtonIndent: [''],
        partialButtonIndent: ['']
      });
      this.emailForm = this.fb.group({
        purchaseRequestButton: [''],
        postGRNStatusButton: [''],
        indentApprovalButton: [''],
        purchaseApprovalButton: [''],
        purchaseOrderButton: [''],
        reportFailedButton: [''],
        errorLogButton: [''],
        approvedButton: [''],
        rejectedButton: [''],
        reportButton: [''],
        piApprovalButton: ['']
      });
      this.reportForm = this.fb.group({
        report_Access: [''],
        reportAccess: ['']
      });
      this.roleForm = this.fb.group({
        report_Access: [''],
        reportAccess: ['']
      });
      this.user = this.auth.getCurrentUser();      
      const accessData = sessionStorage.getItem('access');
      this.access = accessData ? JSON.parse(accessData) : {};
      this.userRole = this.auth.getCurrRole();
      this.readIPConfig();
      this.getIPAddress();
      this.getPriceTires();
      this.getRoles();
      this.getReportData();
      if(this.access.settings){
        this.selectedSettingRoles.push(...this.access.settings)
      }
      if(this.access.bulkExcel){
        this.selectedExcelRoles.push(...this.access.bulkExcel)
      }
      
      this.isSmallDevice$ = this.breakpointObserver.observe([Breakpoints.XSmall, Breakpoints.Small]).pipe(
        map((result) => {
          return result.matches;
        })
      );
  }

  onTabChange(event: any): void {
    if (event.index === 2) {
      this.getRoles();
    }
  }

  togglePlaceholderLabel(inputValue: string): void {
    this.showPlaceholderLabel = inputValue.length === 0;
    this.startingDate = new Date(); // or set a default value if needed

  }

  ngOnInit(): void { 
    // this.initializeMonthlyClosingDates();
    this.monthlyClosingDates = this.getMonthlyClosingDates();
    this.vendorFilterCtrl = new FormControl('', Validators.pattern('[a-zA-Z0-9\\s]*'));
    this.vendorFilterCtrl.valueChanges.pipe(debounceTime(200), distinctUntilChanged())
    .subscribe(newValue => {
      this.vendorFilterBanks(newValue);
    });
    this.shareDataService.selectedBranchesSource.subscribe(data => {
      this.branches=data
      this.filteredBranches= this.branches

      if (this.branches.length === 1) {
        this.branchData.setValue(this.branches[0].restaurantIdOld);
        this.selectedLocation = this.branches[0].restaurantIdOld;
      } else {
        this.branchData.setValue(null);
        this.selectedLocation = null;  
      }
      
    });
  }

  ngAfterViewInit() {
     this.dataSourceSales.paginator = this.salesPaginator;
     this.dataSourceWeightedAvg.paginator = this.wacPaginator;
     this.dataSourceForecast.paginator = this.forecastPaginator;
     this.salesTab();
     if (this.sharedData.checkMenuNavigate() === true) {
      this.tabGroup.selectedIndex = 2;
      }
    // this.dataSource.sort = this.sort;
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  vendorFilterBanks(searchTerm: string) {
    if (!searchTerm) {
      this.filteredBranches = this.branches;
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredBranches = this.branches.filter(branch =>
        branch.branchName.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }  

  convertToIST(createTs: string): string {
    const date = new Date(createTs);
    const offset = 6.5 * 60 * 60 * 1000; 
    const istTime = new Date(date.getTime() + offset);
    let hours = istTime.getHours();    
    if (hours >= 12) {
        hours = hours - 12; 
    } else {
        hours = hours + 12; 
    }
    const updatedTime = new Date(istTime);
    updatedTime.setHours(hours);
    return updatedTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true });
  }

  getRoles() {
    this.api.getRoles(this.user.tenantId).subscribe({
      next: (res) => {        
        if (res['success'] == true) { 
          this.processRoles(res['roles'], 'GRN');
          this.processRoles(res['roles'], 'PO');
          this.processRoles(res['roles'], 'Indent');
  
          this.roles = res['roles'];
          this.inventoryBank = this.roles.map(role => role);
          this.inventory.next(this.inventoryBank.slice());
          this.inventoryFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.Filter(this.inventoryBank, this.inventoryFilterCtrl, this.inventory);
          });
          this.roles = res['roles'];
          this.userBank = this.roles.map(role => role);
          this.users.next(this.userBank.slice());
          this.userFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.Filter(this.userBank, this.userFilterCtrl, this.users);
          });          
          this.roles = res['roles'];
          this.recipeBank = this.roles.map(role => role);
          this.recipe.next(this.recipeBank.slice());
          this.recipeFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.Filter(this.recipeBank, this.recipeFilterCtrl, this.recipe);
          });
          this.roles = res['roles'];
          this.partyBank = this.roles.map(role => role);
          this.party.next(this.partyBank.slice());
          this.partyFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.Filter(this.partyBank, this.partyFilterCtrl, this.party);
          });
          this.roles = res['roles'];
          this.dashboardBank = this.roles.map(role => role);
          this.dashboard.next(this.dashboardBank.slice());
          this.dashboardFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.Filter(this.dashboardBank, this.dashboardFilterCtrl, this.dashboard);
          });
          this.roles = res['roles'];
          this.report_Bank = this.roles.map(role => role);
          this.reports.next(this.report_Bank.slice());
          this.report_FilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.Filter(this.report_Bank, this.report_FilterCtrl, this.reports);
          });
          let superAdminIndex = this.roles.indexOf("superAdmin");
          if (superAdminIndex !== -1) {
            this.roles.unshift(this.roles.splice(superAdminIndex, 1)[0]);
          }
        }
      },
      error: (err) => {
      },
    });
  }

  protected Filter(bank:any, form:any, data:any) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(
      bank.filter(data => data.toLowerCase().indexOf(search) > -1)
    );
  }
  
  processRoles(roles: any[], type: string) {
    const bankName = `${type.toLowerCase()}Bank`;
    const deleteCtrl = `delete${type}`;
    const editCtrl = `edit${type}`;
    const closeCtrl = `close${type}`;
    const deleteFilterCtrl = `${deleteCtrl}FilterCtrl`;
    const editFilterCtrl = `${editCtrl}FilterCtrl`;
    const closeFilterCtrl = `${closeCtrl}FilterCtrl`;

    this[bankName] = roles.map(area => area);
  
    this[deleteCtrl].next(this[bankName].slice());
    this[deleteFilterCtrl].valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.Filter(this[bankName], this[deleteFilterCtrl], this[deleteCtrl]);
    });
  
    this[editCtrl].next(this[bankName].slice());
    this[editFilterCtrl].valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.Filter(this[bankName], this[editFilterCtrl], this[editCtrl]);
    });

    this[closeCtrl].next(this[bankName].slice());
    this[closeFilterCtrl].valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.Filter(this[bankName], this[closeFilterCtrl], this[closeCtrl]);
    });
  }
  
  toggleSelectAll(formType: string, action: string) {
    const control = this[`${formType.toLowerCase()}Form`].controls[`${action}_${formType}`];
    const bankName = `${formType.toLowerCase()}Bank`;
  
    if (control.value.length - 1 === this[bankName].length) {
      control.setValue([]);
    } else {
      control.setValue(this[bankName]);
    }
  
    this.validateSales(control.value);
  }
  
  toggleSelectAllDeleteGRN() {
    this.toggleSelectAll('GRN', 'delete');
  }
  
  toggleSelectAllEditGRN() {
    this.toggleSelectAll('GRN', 'edit');
  }

  toggleSelectAllCloseGRN() {
    this.toggleSelectAll('GRN', 'close');
  }
  
  toggleSelectAllDeletePO() {
    this.toggleSelectAll('PO', 'delete');
  }
  
  toggleSelectAllEditPO() {
    this.toggleSelectAll('PO', 'edit');
  }

  toggleSelectAllClosePO() {
    this.toggleSelectAll('PO', 'close');
  }
  
  toggleSelectAllDeleteIndent() {
    this.toggleSelectAll('Indent', 'delete');
  }
  
  // toggleSelectAllEditIndent() {
  //   this.toggleSelectAll('Indent', 'edit');
  // }

  toggleSelectAllCloseIndent() {
    this.toggleSelectAll('Indent', 'close');
  }

  toggleSelectAllInventory() {
    const control = this.moduleForm.controls['inventory_Access'];
    if (control.value.length - 1 === this.inventoryBank.length) {
      control.setValue([]);
      this.validateSales(this.moduleForm.value.inventory_Access);
    } else {
      control.setValue(this.inventoryBank);
      this.validateSales(this.moduleForm.value.inventory_Access);
    }
  }

  toggleSelectAllUser() {
    const control = this.moduleForm.controls['user_Access'];
    if (control.value.length - 1 === this.userBank.length) {
      control.setValue([]);
      this.validateSales(this.moduleForm.value.user_Access);
    } else {
      control.setValue(this.userBank);
      this.validateSales(this.moduleForm.value.user_Access);
    }
  }
  
  toggleSelectAllRecipe() {
    const control = this.moduleForm.controls['recipe_Access'];
    if (control.value.length - 1 === this.recipeBank.length) {
      control.setValue([]);
      this.validateSales(this.moduleForm.value.recipe_Access);
    } else {
      control.setValue(this.recipeBank);
      this.validateSales(this.moduleForm.value.recipe_Access);
    }
  }

  toggleSelectAllParty() {
    const control = this.moduleForm.controls['party_Access'];
    if (control.value.length - 1 === this.partyBank.length) {
      control.setValue([]);
      this.validateSales(this.moduleForm.value.party_Access);
    } else {
      control.setValue(this.partyBank);
      this.validateSales(this.moduleForm.value.party_Access);
    }
  }

  toggleSelectAllDashboard() {
    const control = this.moduleForm.controls['dashboard_Access'];
    if (control.value.length - 1 === this.dashboardBank.length) {
      control.setValue([]);
      this.validateSales(this.moduleForm.value.dashboard_Access);
    } else {
      control.setValue(this.dashboardBank);
      this.validateSales(this.moduleForm.value.dashboard_Access);
    }
  }

  selectAllRolesForReport() {
    const control = this.reportForm.controls['report_Access'];
    if (control.value.length - 1 === this.report_Bank.length) {
      control.setValue([]);
      this.validateSales(this.reportForm.value.report_Access);
    } else {
      control.setValue(this.report_Bank);
      this.validateSales(this.reportForm.value.report_Access);
    }
  }

  selectAllReportsForRoles() {
    const control = this.roleForm.controls['reportAccess'];
    if (control.value.length - 1 === this.reportBank.length) {
      control.setValue([]);
      this.validateSales(this.roleForm.value.reportAccess);
    } else {
      control.setValue(this.reportBank);
      this.validateSales(this.roleForm.value.reportAccess);
    }
  }

  openDialog(): void {
    this.dialog.open(this.dialogRef,{
      autoFocus: false,
      disableClose: true,
      minWidth:'40vw'
    });
  }

  closeDialog(): void {
    this.dialog.closeAll();
  }

  validateInput() {
    if(this.selectedOption == "individual"){
      this.isIPValid = this.ipPattern.test(this.ipOrSubnet);
    }else{
      let start = this.ipPattern.test(this.startIP);
      let end = this.ipPattern.test(this.endIP);
      if(start && end){
        this.isIPValid = true;
      }else{
        this.isIPValid = false;
      }
    } 
  }
  
  validateSales(delete_GRN?: any) {
    this.profitTarget < 1 ? this.profitTarget = 1 : undefined;
    this.cd.detectChanges();
  }

  roleChange(selectedRole: string) {
    this.filteredRole = this.roles.find(role => role === selectedRole);
    const requiredReports = this.responseReport
    .filter(report => report.access.includes(selectedRole))
    .map(report => report.displayName);
    const control = this.roleForm.controls['reportAccess'];
    control.setValue(requiredReports);
    this.validateSales(requiredReports);
    this.cd.detectChanges();
  }

  reportChange(selectedReport: string) {
    this.filteredReports = this.responseReport.filter(report => report.displayName === selectedReport);    
    if (this.filteredReports.length > 0) {
      const reportAccess = this.updatedReportAccess[selectedReport] || this.filteredReports[0].access;
      this.reportForm.patchValue({
        report_Access: reportAccess
      });
    }
    this.cd.detectChanges();
  }

  getPriceTires(){
    this.api.getPOSPriceTires(this.user.tenantId, this.branchData.value).subscribe({
      next: (res) => {
        if (Array.isArray(res)) {
          this.priceTierList = res.map(({ id, name }) => ({ id, name }));          
        let decorationPackage
        if(this.object && this.object[this.branchData.value]){
          decorationPackage = this.priceTierList.find(item => item.name === this.object[this.branchData.value].priceTierName || item.id  === this.object[this.branchData.value].defaultPriceTier );
        }       
        this.priceData.setValue(decorationPackage || null);
      }
      },
      error: (err) => {
        console.error(err);
      },
    });
  }

  readIPConfig() {
    this.api.readIPConfig(this.user.tenantId).subscribe({
      next: (res) => {
        if (res?.['success']) {
          const data = res['data'];
          const permissions = data?.['permission'] || {};          
          this.responseGRN = permissions['grnAccess'] || [];
          this.responsePO = permissions['POAccess'] || [];
          this.responseIndent = permissions['indentAccess'] || [];
          this.responseEmail = permissions['emailConfiguration'] || [];          
          this.responseUIAccess = permissions['UIAccess'] || [];          
          this.salesLocation = data['multiPosUser'] || [];
  
          const defaultPriceTier = data['defaultPriceTier'] || {};
          if (defaultPriceTier && typeof defaultPriceTier === 'object') {
            const keys = Object.keys(defaultPriceTier);
            if (keys.length > 0) {
              const lastKey = keys[keys.length - 1];
              this.branchData.setValue(lastKey);
              this.object = defaultPriceTier;
              this.getPriceTires();
            }
          } else {
            console.error('defaultPriceTier is either undefined or not an object');
          }
  
          if (permissions.hasOwnProperty('sales') && permissions['sales'].hasOwnProperty('profitTarget')) {
            this.profitTarget = permissions['sales']['profitTarget']; 
          } else {
            this.profitTarget = 1;
          }  
          this.isIPValid = true;      
          this.selectedOption = data['ip_type'];
          
          if (this.selectedOption == 'range') {
            this.startIP = data['ips_range']?.['start'] || '0.0.0.0';
            this.endIP = data['ips_range']?.['end'] || '0.0.0.0';
            if (this.startIP == '0.0.0.0') {
              this.IpType = 'withoutIp';
            } else {
              this.IpType = 'withIp';
              this.showIpProcess = true;
            }
          } else {
            this.ipOrSubnet = data['allowed_ips']?.[0] || '';
            this.IpType = 'withIp';
            this.showIpProcess = true;
          }         
          this.cd.detectChanges();
          this.initializeFormFields();
        }
      },
      error: (err) => { console.log(err) }
    });
  }

  getReportData() {
    this.api.getReportData(this.user.tenantId).subscribe({
      next: (res) => {
        if (res?.['success']) {
          this.responseReport = res['data']['types'];                    
          this.displayNames = this.responseReport.map(report => report.displayName);
          this.reportBank = this.responseReport.map(report => report.displayName);
          this.report.next(this.reportBank.slice());
          this.reportFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.Filter(this.reportBank, this.reportFilterCtrl, this.report);
          });
          this.cd.detectChanges();
        }
      },
      error: (err) => { console.log(err) }
    });
  }
  
  initializeFormFields() {
    if (this.responseGRN) {
        this.grnForm.patchValue({
            delete_GRN: this.responseGRN.deleteAccess,
            edit_GRN: this.responseGRN.editAccess,
            close_GRN: this.responseGRN.rtvAccess,
            deleteButtonGRN: this.responseGRN.delete ? 'Yes' : 'No',
            editButtonGRN: this.responseGRN.edit ? 'Yes' : 'No',
            closeButtonGRN: this.responseGRN.rtv ? 'Yes' : 'No'
        });
        this.showDeleteGRNField = this.responseGRN.delete;
        this.showEditGRNField = this.responseGRN.edit;
        this.showCloseGRNField = this.responseGRN.rtv;
    }

    if (this.responsePO) {
        this.poForm.patchValue({
            delete_PO: this.responsePO.deleteAccess,
            edit_PO: this.responsePO.editAccess,
            close_PO: this.responsePO.closeAccess,
            deleteButtonPO: this.responsePO.delete ? 'Yes' : 'No',
            editButtonPO: this.responsePO.edit ? 'Yes' : 'No',
            closeButtonPO: this.responsePO.close ? 'Yes' : 'No'
        });
        this.showDeletePOField = this.responsePO.delete;
        this.showEditPOField = this.responsePO.edit;
        this.showClosePOField = this.responsePO.close;
    }

    if (this.responseIndent) {
        this.indentForm.patchValue({
            delete_Indent: this.responseIndent.deleteAccess,
            // edit_Indent: this.responseIndent.editAccess,
            close_Indent: this.responseIndent.closeAccess,
            deleteButtonIndent: this.responseIndent.delete ? 'Yes' : 'No',
            // editButtonIndent: this.responseIndent.edit ? 'Yes' : 'No',
            closeButtonIndent: this.responseIndent.close ? 'Yes' : 'No',
            partialButtonIndent: this.responseIndent.partial ? 'Yes' : 'No'
        });
        this.showDeleteIndentField = this.responseIndent.delete;
        // this.showEditIndentField = this.responseIndent.edit;
        this.showCloseIndentField = this.responseIndent.close;
    }

    if (this.responseEmail) {
        this.emailForm.patchValue({
            purchaseRequestButton: this.responseEmail.purchaseRequest ? 'Yes' : 'No',
            postGRNStatusButton: this.responseEmail.postGrnStatus ? 'Yes' : 'No',
            indentApprovalButton: this.responseEmail.indentApproval ? 'Yes' : 'No',
            purchaseApprovalButton: this.responseEmail.purchaseApproval ? 'Yes' : 'No',
            purchaseOrderButton: this.responseEmail.purchaseOrder ? 'Yes' : 'No',
            // reportFailedButton: this.responseEmail.reportFailed ? 'Yes' : 'No',
            // errorLogButton: this.responseEmail.systemErrorLog ? 'Yes' : 'No',
            // approvedButton: this.responseEmail.approved ? 'Yes' : 'No',
            // rejectedButton: this.responseEmail.rejected ? 'Yes' : 'No',
            // reportButton: this.responseEmail.report ? 'Yes' : 'No',
            piApprovalButton: this.responseEmail.piApproval ? 'Yes' : 'No'
        });
    }

    if (this.responseUIAccess) {
      this.moduleForm.patchValue({
        inventory_Access: this.responseUIAccess.inventory.access,
        user_Access: this.responseUIAccess.user.access,
        recipe_Access: this.responseUIAccess.recipe.access,
        // party_Access: this.responseUIAccess.party.access,
        inventoryStatusButton: this.responseUIAccess.inventory.status ? 'Yes' : 'No',
        userStatusButton: this.responseUIAccess.user.status ? 'Yes' : 'No',
        recipeStatusButton: this.responseUIAccess.recipe.status ? 'Yes' : 'No',
        // partyStatusButton: this.responseUIAccess.party.status ? 'Yes' : 'No',
      });
      this.showInventoryStatusField = this.responseUIAccess.inventory.status;
      this.showUserStatusField = this.responseUIAccess.user.status;
      this.showRecipeStatusField = this.responseUIAccess.recipe.status;
      // this.showPartyStatusField = this.responseUIAccess.party.status;
    } 
    if (this.responseUIAccess.party) {
      this.moduleForm.patchValue({
        party_Access: this.responseUIAccess.party.access,
        partyStatusButton: this.responseUIAccess.party.status ? 'Yes' : 'No'
      });
      this.showPartyStatusField = this.responseUIAccess.party.status;
    } else {
      this.moduleForm.patchValue({
        partyStatusButton: 'No'
      });
    }
    if (this.responseUIAccess.dashboard) {
      this.moduleForm.patchValue({
        dashboard_Access: this.responseUIAccess.dashboard.access,
        dashboardStatusButton: this.responseUIAccess.dashboard.status ? 'Yes' : 'No'
      });
      this.showDashboardStatusField = this.responseUIAccess.dashboard.status;
    } else {
      this.moduleForm.patchValue({
        dashboardStatusButton: 'No'
      });
    }
    }

  onSubmit() {
    let obj;
    if(this.IpType == "withoutIp"){
      obj={
        "tenantId" : this.user.tenantId,
        "email" : this.user.userEmail,
        "name" : this.user.name,
        "ip_type" : 'range',
        "ips_range":{ "start" : '0.0.0.0', "end" : '***************' },
        "allowed_ips" :[this.ipOrSubnet]
      }
    }else{
      obj={
        "tenantId" : this.user.tenantId,
        "email" : this.user.userEmail,
        "name" : this.user.name,
        "ip_type" : this.selectedOption,
        "ips_range":{ "start" : this.startIP, "end" : this.endIP },
        "allowed_ips" :[this.ipOrSubnet]
      }
    }
    this.api.updateIPConfig(obj).subscribe({
      next: (res) => {
        if (res['success']) {
          this.notify.snackBarShowSuccess('Updated successfully!');
        } else {
          this.notify.snackBarShowError('Something went wrong!');
        }
        this.cd.detectChanges();
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

  addConfig() {
    let obj = {}
    obj['branch']=this.branchData.value
    obj['tenantId'] = this.user.tenantId;
    obj['priceTierData'] = this.priceData.value.id;
    obj['priceTierName'] = this.priceData.value.name;
    obj['salesConfig'] = true;

    this.api.updateIPConfig(obj).subscribe({
      next: (res) => {
        if (res['success']) {
          this.notify.snackBarShowSuccess('Updated successfully!');
          const defaultPriceTier = res['data']['defaultPriceTier'] || {};
          if (defaultPriceTier && typeof defaultPriceTier === 'object') {
            this.object = defaultPriceTier;
          }
        } else {
          this.notify.snackBarShowError('Something went wrong!');
        }
        this.cd.detectChanges();
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

  getIPAddress(){
    this.api.getIPAddress().subscribe((res: any) => {
      this.currentIp = res['ipString'];
      this.cd.detectChanges();
    });
  }

  submitIp(){
    if(this.IpType === 'withoutIp'){
      this.startIP;
      this.endIP;
      this.onSubmit()
      this.masterDataService.setNavigation('inventoryList');
      this.router.navigate(['/dashboard/home']);
      this.dialog.closeAll();
    }else if(this.IpType === 'withIp'){
      this.dialog.closeAll();
    }else{
      this.notify.snackBarShowInfo('select any of one IP type')
    }
  }

  radioChange(){
    if(this.IpType == "withIp"){
      this.showIpProcess = true;
    }else{
      this.showIpProcess = false;
      this.startIP = '0.0.0.0'
      this.endIP = '***************'
      this.selectedOption ='range'
    }
  }
 
  getAccess() {
    this.auth.getRolesList({ tenantId: this.user.tenantId }).subscribe((data) => {
      if ('access' in data) {
        this.sharedData.setAccess(data['access']);
        const accessString = JSON.stringify(data['access']);
        sessionStorage.setItem(GlobalsService.accessData, accessString);
        sessionStorage.setItem('access', accessString);
        this.access = data['access'];
        if (this.access.settings) {
          this.selectedSettingRoles.push(...this.access.settings);
        }
        if (this.access.bulkExcel) {
          this.selectedExcelRoles.push(...this.access.bulkExcel);
        }
      }
    });
  }

  submitPermission(){
    let settingRole = []
    let bulkExcelRole = []
    settingRole = this.accessForm.value.selectedSettingRoles
    bulkExcelRole = this.accessForm.value.selectedExcelRoles
    settingRole.push('superAdmin')
    bulkExcelRole.push('superAdmin')
    let obj = {}
    let access = {
      settings :  Array.from(new Set(settingRole)) ,
      bulkExcel : Array.from(new Set(bulkExcelRole))
    }
    obj['tenantId'] = this.user.tenantId ;  
    obj['access'] = access
    this.api.updateAccess(obj).subscribe({
      next: (res) => {
        if (res['success']) {
          this.notify.snackBarShowSuccess('Updated successfully')
          this.getRoles();
          this.getAccess();
        } else {
          this.notify.snackBarShowError('Something went wrong!');
        }   
      },
      error: (err) => {
        console.log(err);
      },
    });   
  }

  updateGRN() {
    this.updatePermissions('GRN', this.grnForm, this.showDeleteGRNField, this.showEditGRNField, this.showCloseGRNField);
  }
  
  updatePO() {
    this.updatePermissions('PO', this.poForm, this.showDeletePOField, this.showEditPOField, this.showClosePOField);
  }
  
  updateIndent() {
    this.updatePermissions('Indent', this.indentForm, this.showDeleteIndentField, this.showEditIndentField, this.showCloseIndentField, this.showPartialIndentField);
  }

  refreshForecast(){
    this.forecastTab();
  }

  refreshWac(){
    this.wacTab();
  }

  refreshSales(){
    this.salesTab();
  }

  tabChange(event: MatTabChangeEvent) {
    if (event.tab.textLabel === 'SALES') {
      this.salesTab();
    }
    if (event.tab.textLabel === 'WAC') {
      this.wacTab();
    }
    if (event.tab.textLabel === 'FORECAST') {
      this.forecastTab();
    }
  }
  
  salesTab() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId ;  
    obj['event'] = 'sales' ;
    this.api.salesRetrigger(obj).subscribe({
      next: (res) => {
        const sales = res['data'];
        this.dataSourceSales.data = res['data'];
        this.dataSourceSales.paginator = this.salesPaginator;
        this.isSalesDataReady = true;
      },
      error: (err) => {
        console.log(err);
      },
    });      
  }

  wacTab(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId ;  
    obj['event'] = 'weightedAverage' ;    
    this.api.wacRetrigger(obj).subscribe({
      next: (res) => {
        const wac = res['data'];
        this.dataSourceWeightedAvg.data = res['data'];
        this.dataSourceWeightedAvg.paginator = this.wacPaginator;
        this.isWacDataReady = true;
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  forecastTab(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId ;  
    obj['event'] = 'forecast' ;    
    this.api.forecastRetrigger(obj).subscribe({
      next: (res) => {
        const wac = res['data'];
        this.dataSourceForecast.data = res['data'];
        this.dataSourceForecast.paginator = this.forecastPaginator;
        this.isForecastReady = true;
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  dateCorrection(date){
    const changedDate = new Date(date);
    changedDate.setHours(changedDate.getHours() + 5, changedDate.getMinutes() + 30);
    return changedDate.toISOString()
  }
  
  salesRerun() {   
    if (this.salesForm.invalid) {
      this.salesForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields');
    } else { 
    let obj = {
      tenantId: this.user.tenantId,
      event: 'sales',
      createdBy: this.user.email,
      startDate: this.dateCorrection(this.salesForm.get('startDate').value),
      endDate: this.dateCorrection(this.salesForm.get('endDate').value)
    };
    
    this.api.salesRerun(obj).subscribe({
      next: (res) => {
        if (res.result === 'success') {
          this.notify.snackBarShowSuccess('Submitted Successfully');
          this.salesForm.reset(); 
          this.refreshSales()
        } else {
          this.notify.snackBarShowError('Something went wrong!');
        }      },
      error: (err) => {
        console.log(err);
      },
    });  
  }
  }

  weightedAvg(){
    const istDate = new Date(new Date().getTime() + 5.5 * 60 * 60 * 1000).toISOString().slice(0, -1)+ 'Z';        
    let obj = {}
    obj['tenantId'] = this.user.tenantId ;  
    obj['event'] = 'weightedAverage' ;  
    obj['createdBy'] = this.user.email ; 
    obj['currentDate'] = istDate ; 
    this.api.weightedAvg(obj).subscribe({
      next: (res) => {
        if (res.result === 'success') {
          this.notify.snackBarShowSuccess('Submitted Successfully');
          this.refreshWac() ;
        } else {
          this.notify.snackBarShowError('Something went wrong!');
        }
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  forecast(){
    const istDate = new Date(new Date().getTime() + 5.5 * 60 * 60 * 1000).toISOString().slice(0, -1)+ 'Z';        
    let obj = {}
    obj['tenantId'] = this.user.tenantId ;  
    obj['event'] = 'forecast' ; 
    obj['createdBy'] = this.user.email ; 
    obj['currentDate'] = istDate ; 
      // startDate: this.dateCorrection(this.forecastForm.get('startDate').value),
      // endDate: this.dateCorrection(this.forecastForm.get('endDate').value)
    this.api.forecastData(obj).subscribe({
      next: (res) => {
        if (res.result === 'success') {
          this.refreshForecast() ;
          this.notify.snackBarShowSuccess('Submitted Successfully');
        } else {
          this.notify.snackBarShowError('Something went wrong!');
        }
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  updateModule() {
    let module = this.moduleForm.value;
    let obj: any = {
      accessPermission:{
      inventory: {
        status: module.inventoryStatusButton === 'Yes',
        access: module.inventory_Access
      },
      user: {
        status: module.userStatusButton === 'Yes',
        access: module.user_Access
      },
      recipe: {
        status: module.recipeStatusButton === 'Yes',
        access: module.recipe_Access
      },
      party: {
        status: module.partyStatusButton === 'Yes',
        access: module.party_Access
      },
      dashboard: {
        status: module.dashboardStatusButton === 'Yes',
        access: module.dashboard_Access
      }
    },
      tenantId: this.user.tenantId,
    };
    this.api.updateConfigAccess(obj).subscribe({
      next: (res) => {
        if (res.success) {
          this.notify.snackBarShowSuccess('Updated Successfully');
          this.getRoles();
          this.getAccess();
        } else {
          this.notify.snackBarShowError('Something went wrong!');
        }
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

  updateEmail() {
    let emailValues = this.emailForm.value;
    let obj: any = {
        purchaseRequest: emailValues.purchaseRequestButton === 'Yes',
        postGrnStatus: emailValues.postGRNStatusButton === 'Yes',
        indentApproval: emailValues.indentApprovalButton === 'Yes',
        purchaseApproval: emailValues.purchaseApprovalButton === 'Yes',
        purchaseOrder: emailValues.purchaseOrderButton === 'Yes',
        piApproval: emailValues.piApprovalButton === 'Yes',
        // reportFailed: emailValues.reportFailedButton === 'Yes',
        // systemErrorLog: emailValues.errorLogButton === 'Yes',
        // approved: emailValues.approvedButton === 'Yes',
        // rejected: emailValues.rejectedButton === 'Yes',
        // report: emailValues.reportButton === 'Yes',
        reportFailed: false,
        systemErrorLog: false,
        approved: false,
        rejected: false,
        report: false,     
        tenantId: this.user.tenantId,
    };
    this.api.updateConfigAccess(obj).subscribe({
      next: (res) => {
        if (res.success) {
          this.notify.snackBarShowSuccess('Updated Successfully');
          this.getRoles();
          this.getAccess();
        } else {
          this.notify.snackBarShowError('Something went wrong!');
        }
      },
      error: (err) => {
        console.log(err);
      }
    });
  }  

  getBackendName(reportAccess: string): string {
    const report = this.responseReport.find(report => report.displayName === reportAccess);
    return report ? report.backendName : '';
  }

  updateReport(reportBased = true) {
    let report = this.reportForm.value;
    let backendName = this.getBackendName(report.reportAccess);
    let obj: any = {}
    if (reportBased) {
      obj = {
        tenantId: this.user.tenantId,
        reportName : backendName,
        access: report.report_Access,
        reportBased: true,
      };
    } else {
      obj = {
        tenantId: this.user.tenantId,
        role: this.roleForm.get('report_Access').value,
        reports: this.roleForm.get('reportAccess').value,
        reportBased: false,
      };
    }
    this.api.updateReport(obj).subscribe({
      next: (res) => {
        if (res.success) {
          this.updatedReportAccess[report.reportAccess] = report.report_Access; 
          this.getReportData();
          reportBased ? this.roleForm.patchValue({
            report_Access: undefined
          }) : undefined
          this.notify.snackBarShowSuccess('Updated Successfully');
        } else {
          this.notify.snackBarShowError('Something went wrong!');
        }
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

  updatePermissions(formType: string, formGroup: FormGroup, showDeleteField: boolean, showEditField: boolean, showCloseField: boolean, showPartialIndentField?: boolean) {
    let deleteRole = formGroup.value[`delete_${formType}`] || [];
    let editRole = formGroup.value[`edit_${formType}`] || []; 
    let closeRole = formGroup.value[`close_${formType}`] || []; 
    let obj: any = {
      delete: showDeleteField,
      edit: showEditField,
      close: showCloseField,
      formType: formGroup.value.formType,
      tenantId: this.user.tenantId,
      deleteAccess: Array.from(new Set(deleteRole)),
      editAccess: Array.from(new Set(editRole)),
      closeAccess: Array.from(new Set(closeRole))
    };      
    if (formGroup.contains('partialButtonIndent')) {
      obj.partial = formGroup.value['partialButtonIndent'] === 'Yes';
    }
    this.api.updateConfigAccess(obj).subscribe({
      next: (res) => {
        if (res.success) {
          this.notify.snackBarShowSuccess('Updated Successfully');
          this.getRoles();
          this.getAccess();
        } else {
          this.notify.snackBarShowError('Something went wrong!');
        }
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

  inventoryStatusShown(event: any) {
    this.showInventoryStatusField = event.value === 'Yes';
  }

  userStatusShown(event: any) {
    this.showUserStatusField = event.value === 'Yes';
  }

  recipeStatusShown(event: any) {
    this.showRecipeStatusField = event.value === 'Yes';
  }

  partyStatusShown(event: any) {
    this.showPartyStatusField = event.value === 'Yes';
  }

  dashboardStatusShown(event: any) {
    this.showDashboardStatusField = event.value === 'Yes';
  }

  isDeleteGrnShown(event: any) {
    this.showDeleteGRNField = event.value === 'Yes';
  }

  isEditGrnShown(event: any) {
    this.showEditGRNField = event.value === 'Yes';
  }

  isCloseGrnShown(event: any) {
    this.showCloseGRNField = event.value === 'Yes';
  }

  isDeletePoShown(event: any) {
    this.showDeletePOField = event.value === 'Yes';
  }

  isEditPoShown(event: any) {
    this.showEditPOField = event.value === 'Yes';
  }

  isClosePoShown(event: any) {
    this.showClosePOField = event.value === 'Yes';
  }

  isDeleteIndentShown(event: any) {
    this.showDeleteIndentField = event.value === 'Yes';
  }

  isCloseIndentShown(event: any) {
    this.showCloseIndentField = event.value === 'Yes';
  }

  onLocationSubmit(): void {
  }


  isPartialIndentShown(event: any) {
    this.showPartialIndentField = event.value === 'Yes';
  }  
 
   onDateSelectionChange(value: string | null): void {
    if (value === 'startOfMonth') {
      this.monthlyClosingDates.forEach(month => {
        this.startingDates[month.month] = this.getStartOfMonthDate(month);
      });
    } else {
      this.startingDates = {}; 
    }
    this.selectedDateOption = value; 
  }

  goBackToLocationSelection(): void {
    this.selectedDateOption = null; 
    this.showLocationSelection = true; 
  }


  getMonthlyClosingDates(): { month: string, dates: Date[] }[] {
    return [
      { month: 'January', dates: [new Date()] },
      { month: 'February', dates: [new Date()] },
      { month: 'March', dates: [new Date()] },
      { month: 'April', dates: [new Date()] },
      { month: 'May', dates: [new Date()] },
      { month: 'June', dates: [new Date()] },
      { month: 'July', dates: [new Date()] },
      { month: 'August', dates: [new Date()] },
      { month: 'September', dates: [new Date()] },
      { month: 'October', dates: [new Date()] },
      { month: 'November', dates: [new Date()] },
      { month: 'December', dates: [new Date()] }
    ];
  }
  onLocationChange(event: any): void {
    this.selectedLocation = event.value;
    this.showLocationSelection = false; 
  }

submit(): void {
  if (!this.selectedLocation) {
    alert('Please select a location.');
    return;
  }

  const closingDetails: any = {};

  if (this.pickDateOption === 'no') {
    closingDetails[this.selectedLocation] = {
      status: false,
      selectedClosingDates: {}
    };
  } else if (this.pickDateOption === 'yes' && this.selectedDateOption === 'startOfMonth') {
    closingDetails[this.selectedLocation] = {
      status: true,
      selectedClosingDates: {}
    };

    this.monthlyClosingDates.forEach(month => {
      const startOfMonthDate = this.getStartOfMonthDate(month).toISOString().split('T')[0];
      closingDetails[this.selectedLocation].selectedClosingDates[month.month.toLowerCase()] = startOfMonthDate;
    });
  } else {
    closingDetails[this.selectedLocation] = {
      status: true,
      selectedClosingDates: {}
    };
  }

  const payload = {
    location: this.selectedLocation,
    closingDetails: closingDetails
  };
  const headers = new HttpHeaders({
    'accept': 'application/json',
    'Content-Type': 'application/json'
  });

  this.http.post(`${this.engineUrl}master_data/update-closing-dates`, payload, { headers })
  .subscribe(
    response => {
      this.notify.snackBarShowSuccess('Closing dates have been successfully updated.');
    },
    error => {
      console.error('Error:', error);
      this.notify.snackBarShowError('Failed to update closing dates. Please try again.');
    }
  );
    
}
closeMessage(): void {
  this.submissionMessage = null;
}
  getStartOfMonthDate(month: any): Date {
   
    const monthIndex = new Date(Date.parse(month.month +" 2, 2024")).getMonth(); // Get the month index
    return new Date(2024, monthIndex, 2); 
  }
  isFinalStep(): boolean {
    if (!this.selectedLocation) {
      return false;
    }
  
    if (this.pickDateOption === 'no') {
      return true;
    }
  
    return this.pickDateOption === 'yes' && !!this.selectedDateOption;
  }

  getJobStatus(element) {
    return element.error 
      ? 'Something Went Wrong' 
      : element.pssi 
        ? 'Completed' 
        : 'In-Progress';
  }
  
}
