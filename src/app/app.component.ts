import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterOutlet } from '@angular/router';
import {MatSidenavModule} from '@angular/material/sidenav';
import {MatListModule} from '@angular/material/list';
import {MatIconModule} from '@angular/material/icon';
import { SidebarHeaderComponent } from './components/sidebar/sidebar-header/sidebar-header.component';
import {MatDividerModule} from '@angular/material/divider';
import { ToolbarComponent } from './components/toolbar/toolbar.component';
import { SidenavComponent } from './components/sidebar/sidenav/sidenav.component';
import { FooterComponent } from './components/footer/footer.component';
import { FixedFabButtonComponent } from "./components/fixed-fab-button/fixed-fab-button.component";
import { TimeOutService } from './services/time-out.service';
import { Observable, Subject, timer } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AuthService } from './services/auth.service';

@Component({
    selector: 'app-root',
    standalone: true,
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss'],
    imports: [CommonModule,
              RouterOutlet ,
              MatSidenavModule, 
              MatListModule,
              MatIconModule, 
              SidebarHeaderComponent, 
              MatDividerModule, 
              ToolbarComponent, 
              SidenavComponent,
              FooterComponent, 
              FixedFabButtonComponent,
              ],
  })
export class AppComponent {
  title = 'Digitory';
  timer: number;
  showNotice = false;
  rxjsTimer = timer(1000, 1000);
  private destroyed$ = new Subject<void>();

  constructor(private sessionTimeoutService: TimeOutService, private authService: AuthService) {}

  ngOnInit(): void {
    this.sessionTimeoutService.logoutTimer
      .pipe(takeUntil(this.destroyed$))
      .subscribe((timeLeft) => {
        if (timeLeft === 0) {
          this.authService.logout();
        }
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
