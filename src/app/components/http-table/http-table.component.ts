import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Inject, Input, OnInit, Output, Renderer2, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { CommonModule, DOCUMENT } from '@angular/common';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { Observable, Subject, first, map } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { <PERSON><PERSON><PERSON>er, FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { ActionComponent as ActionComponentInventory } from '../../pages/inventory-management/inventory/action/action.component';
import { ActionComponent as ActionComponentVendor } from '../../pages/inventory-management/vendor/action/action.component';
import { ActionComponent as ActionComponentSubrecipeMaster } from '../../pages/inventory-management/subrecipe-master/action/action.component';
import { MappingComponent } from '../../pages/recipe-management/mapping/mapping.component';
import { MatSelectModule } from '@angular/material/select';
import { ShareDataService } from 'src/app/services/share-data.service';
import { MasterDataService } from 'src/app/services/master-data.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatToolbarModule } from '@angular/material/toolbar';
import { EmptyStateComponent } from '../empty-state/empty-state.component';
import { UserComponent } from 'src/app/pages/user-management/user/user.component';
import { BranchesComponent } from 'src/app/pages/user-management/branches/branches.component';
import { RoleComponent } from 'src/app/pages/user-management/role/role.component';
import { MenuMasterComponent } from 'src/app/pages/recipe-management/menu-master/menu-master.component';
import { ServingSizeComponent } from 'src/app/pages/recipe-management/serving-size/serving-size.component';
import { InventoryService } from 'src/app/services/inventory.service';
import { AuthService } from 'src/app/services/auth.service';
import { NotificationService } from 'src/app/services/notification.service';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Router } from '@angular/router';
import {FlatTreeControl} from '@angular/cdk/tree';
// import {MatTreeFlatDataSource, MatTreeFlattener} from '@angular/material/tree';
import {MatTreeFlatDataSource, MatTreeFlattener, MatTreeModule} from '@angular/material/tree';
import { AccountSetupComponent } from 'src/app/pages/crm-management/account-setup/account-setup.component';
import { CreatePartyComponent } from 'src/app/pages/party-management/create-party/create-party.component';
import { ThemeService } from 'ng2-charts';
import { MatDividerModule } from '@angular/material/divider';

interface FlatNode {
  expandable: boolean;
  name: string;
  role: string;
  module: string;
  modified: string,
  discontinued: string,
  level: number;
  children: number;
}
@Component({
  selector: 'app-http-table',
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    CommonModule,
    MatSortModule,
    MatMenuModule,
    MatToolbarModule,
    MatTableModule,
    MatInputModule,
    MatFormFieldModule,
    MatPaginatorModule,
    MatButtonModule,
    MatChipsModule,
    MatIconModule,
    MatDialogModule,
    FormsModule,
    MatSelectModule,
    ReactiveFormsModule,
    MatTooltipModule,
    MatTreeModule,
    MatDividerModule,
    EmptyStateComponent
  ],
  templateUrl: './http-table.component.html',
  styleUrls: ['./http-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HttpTableComponent implements OnInit {
  @ViewChild('widgetsContent', { read: ElementRef }) public widgetsContent;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  @Input({ required: true }) page!: string;
  @Input({ required: true }) data!: any;
  displayedColumns: string[] = [];
  dataSource: MatTableDataSource<any>;
  invItemCount: number = 0;
  aboveTargetItemCount: number = 0;
  belowTargetItemCount: number = 0;
  component: any;
  isDataReady = false;
  query: object;
  showData: { value: string; displayName: string; }[];
  searchControl = new FormControl('');
  filteredVendors: any;
  vendorObject: any;
  showTableDialogRef: MatDialogRef<unknown, any>;
  showErrorDialogRef: MatDialogRef<unknown, any>;
  filteredData: any;
  dropDownData: any;
  headings: any;
  user: any;
  invFilter: boolean = false;
  aboveTargetFilter: boolean = false;
  belowTargetFilter: boolean = false;
  updatedData: any[];
  profitTarget: any;
  loaderException: boolean = false;
  showCheckExceptionButton: boolean = true;
  baseData: any;
  POSOnly: any;
  syncedData: any;
  recipeCount: any;
  smallDialog = 'smallCustomDialog'
  mediumDialog = 'mediumCustomDialog'
  largeDialog = 'largeCustomDialog'
  isSmallScreen$: Observable<boolean>;
  tabId: number = 1;

  treeControl: FlatTreeControl<FlatNode>;
  treeFlattener: MatTreeFlattener<Node, FlatNode>;
  dataSourceTree: MatTreeFlatDataSource<Node, FlatNode>;
  closeInfoRef: MatDialogRef<unknown, any>;
  childNode: any;
  protected _onDestroy = new Subject<void>();
  pages: any;
  childTab: any
  nodeNames: any;
  removeData: any[] = [];
  showRemovedItems: boolean = false;
  allData: any[];
  invCount: number;
  invOnly: any[];
  recipesHeading: any;
  POSDataOnly: any;
  invDataOnly: any[];
  recipeData: any[];
  filterValue: any;
  filterRecipeValue: any;
  previousParties: any = [];
  upcomingParties: any = [];
  discontinuedParties: any = [];
  activeParties: any = [];
  partiesName: any[];
  draftParties: any=[];
  @ViewChild('draftDialog') draftDialog: TemplateRef<any>;
  @ViewChild('cloneDialog') cloneDialog: TemplateRef<any>;
  tempParties: any[];
  partyStatus: any;
  partyIndexStatus: any;
  checkBulkMapping: boolean;
  dialogRef: MatDialogRef<any>;
  baseName: any;

  constructor(
    public dialog: MatDialog,
    private sharedData: ShareDataService,
    private router: Router,
    private masterDataService: MasterDataService,
    private cd: ChangeDetectorRef,
    private auth: AuthService,
    private api: InventoryService,
    public notify: NotificationService,
    private masterDtaService: MasterDataService,
    private renderer: Renderer2,
    private breakpointObserver: BreakpointObserver,
    @Inject(DOCUMENT) private document: Document) {
    this.user = this.auth.getCurrentUser();
    this.baseData = this.sharedData.getBaseData().value
    this.isSmallScreen$ = this.breakpointObserver.observe([Breakpoints.Small, Breakpoints.XSmall])
    .pipe(
      map(result => result.matches)
    );

    this.sharedData.getCheckMapping.subscribe(obj => {
      this.checkBulkMapping = obj
    })


    this.treeControl = new FlatTreeControl<FlatNode>(
      node => node.level,
      node => node.expandable,
    );
    this.treeFlattener = new MatTreeFlattener<Node, FlatNode>(
      (node: Node, level: number) => ({
        expandable: !!node['children'] && node['children'].length > 0,
        name: node['name'],
        role: node['role'],
        module: node['module'],
        modified: node['modified'],
        level: level,
        children: node['children'],
        discontinued: node['discontinued'],
      }),
      node => node.level,
      node => node.expandable,
      node => node['children'],
    );
    this.dataSourceTree = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);
  }

  ngOnInit(): void {
    this.query = { isEdit: true }
    this.dataSource = new MatTableDataSource<any>([]);
    this.dataSource.sort = this.sort;
    this.setupTable(this.page);
    this.showCheckExceptionButton = true ;
    this.cd.detectChanges();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  private setupTable(page: string) {
    switch (page) {
      case 'inventory master':
        this.tabId = 1
        this.component = ActionComponentInventory;
        this.displayedColumns = this.masterDtaService.inventoryMapping.map(display => display.value);
        this.showData = this.masterDtaService.inventoryMapping;
        break;
      case 'vendors':
        this.tabId = 2
        this.component = ActionComponentVendor;
        this.displayedColumns = this.masterDtaService.vendorMapping.map(display => display.value);
        this.showData = this.masterDtaService.vendorMapping
        break;
      case 'Subrecipe Master':
        this.tabId = 3
        this.component = ActionComponentSubrecipeMaster;
        this.displayedColumns = this.masterDtaService.subrecipeMasterMapping.map(display => display.value);
        this.showData = this.masterDtaService.subrecipeMasterMapping;
        break;
      case 'menu master':
        this.tabId = 4
        this.component = MenuMasterComponent;
        this.displayedColumns = this.masterDtaService.menuMasterMapping.map(display => display.value);
        this.showData = this.masterDtaService.menuMasterMapping;
        break;
      case 'recipe':
        this.tabId = 5
        this.displayedColumns = this.masterDtaService.indexMapping.map(display => display.value);
        this.showData = this.masterDtaService.indexMapping;
        break;
      case 'users':
        this.tabId = 6
        this.component = UserComponent
        this.displayedColumns = this.masterDtaService.userMapping.map(display => display.value);
        this.showData = this.masterDtaService.userMapping;
        break;
      case 'Roles':
        this.getPages();
        this.tabId = 7
        this.component = RoleComponent;
        this.displayedColumns = this.masterDtaService.rolesMapping.map(display => display.value);
        this.showData = this.masterDtaService.rolesMapping;
        break;
      case 'branches':
        this.tabId = 8
        this.component = BranchesComponent;
        this.displayedColumns = this.masterDtaService.branchMapping.map(display => display.value);
        this.showData = this.masterDtaService.branchMapping;
        break;
      case 'servingsize conversion':
        this.tabId = 9
        this.component = ServingSizeComponent;
        this.displayedColumns = this.masterDtaService.servingSizeMapping.map(display => display.value);
        this.showData = this.masterDtaService.servingSizeMapping;
        break;
      case 'account':
        this.tabId = 10
        this.component = AccountSetupComponent;
        this.displayedColumns = this.masterDtaService.accountMapping.map(display => display.value);
        this.showData = this.masterDtaService.accountMapping
        break;
      case 'Party Order':
        this.tabId = 11
        this.component = CreatePartyComponent;
        this.displayedColumns = this.masterDtaService.partyMapping.map(display => display.value);
        this.showData = this.masterDtaService.partyMapping
        if(this.data.length > 0){
          this.partyIndexStatus = this.data[1]
          this.data = [...this.data[0]]
        }
        // this.getPartyDraft();
        break;

      default:
        break;
    }

    this.data.sort((a, b) => {
      if (a.modified === 'yes' && b.modified !== 'yes') {
        return -1;
      }
      if (b.modified === 'yes' && a.modified !== 'yes') {
        return 1;
      }
      return 0;
    });
    this.data.sort((a, b) => b - a);
    this.dataSource.data = this.data.map((el, index) => ({ ...el, s_no: index + 1 }));
    let exception = this.sharedData.exceptionResult();
    if (page === exception['page'] && exception['check']) {
      this.checkException();
    }
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.isDataReady = true;
    this.getInvCount();

    if(this.page == 'Roles'){
      let data = this.dataSource.data
      let transformedData = [];
      data.forEach((roleItem) => {
      const existingRole = transformedData.find(role => role.name === roleItem.role);
      if (existingRole) {
        const existingModule = existingRole.children.find(module => module.name === roleItem.module);
        if (existingModule) {
          existingModule.children.push({ name: roleItem.Page ,role: roleItem.role ,module: roleItem.module ,modified: roleItem.modified,discontinued: roleItem.Discontinued });
        } else {
          existingRole.children.push({
            name: roleItem.module,
            children: [{ name: roleItem.Page,role: roleItem.role ,module: roleItem.module ,modified: roleItem.modified,discontinued: roleItem.Discontinued }]
          });
        }
      } else {
        transformedData.push({
          name: roleItem.role,
          children: [{
            name: roleItem.module,
            children: [{ name: roleItem.Page,role: roleItem.role ,module: roleItem.module ,modified: roleItem.modified,discontinued: roleItem.Discontinued }]
          }]
        });
      }
    });
    this.dataSourceTree.data = transformedData
    let module = this.dataSource.data.map(item => item.module)
    const uniqueModule = Array.from(new Set(module));
    this.sharedData.sendRolesModule(uniqueModule);
  }

  if(this.page == 'users'){
    const uniqueData = this.dataSource.data.filter((user, index, self) =>
      index === self.findIndex((t) => (
        t.email === user.email
      ))
    );
    this.dataSource.data = uniqueData;
  }

  if(this.page == 'branches'){
    const uniqueData = this.dataSource.data.filter((user, index, self) =>
      index === self.findIndex((t) => (
        t.restaurantId === user.restaurantId
      ))
    );
    this.dataSource.data = uniqueData;
  }

  if(this.page == 'menu master'){
    this.sharedData.getViewRecipe.subscribe(obj => {
      if(Object.keys(obj).length > 0 && Object.keys(obj.element).length > 0){
        this.editFun(obj.element)
        this.cd.detectChanges();
      }
    })
  }

  if(this.page == 'Party Order'){
    this.getParties();
    this.sharedData.getViewRecipe.subscribe(obj => {
      if(Object.keys(obj).length > 0 && Object.keys(obj.event).length > 0){
        this.editFun(obj.event)
        // this.checkNavigation = obj.event
      }
    })
  }
  }

  getParties() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (this.partyIndexStatus === 'Active') {
      this.dataSource.data = this.data.filter(party => {
        const startDate = new Date(party.startDate);
        const endDate = new Date(party.endDate);
        return (
          party.partyClosed === false &&
          startDate <= today &&
          endDate >= today
        );
      });
    } else if (this.partyIndexStatus === 'UpComing') {
      this.dataSource.data = this.data.filter(party => {
        const startDate = new Date(party.startDate);
        return (
          party.partyClosed === false &&
          startDate > today
        );
      });
    } else if (this.partyIndexStatus === 'Completed') {
      this.dataSource.data = this.data.filter(party => {
        const endDate = new Date(party.endDate);
        return (
          party.partyClosed === false &&
          endDate < today
        );
      });
    } else if (this.partyIndexStatus === 'Closed') {
      this.dataSource.data = this.data.filter(party => party.partyClosed === true);
    }
    this.dataSource.data = this.dataSource.data.map((el, index) => ({
      ...el,
      s_no: index + 1
    }));
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  hasChild = (_: number, node: any) => node.expandable;

  applyFilter(filterValue: any) {
    this.dataSource.filter = (filterValue.target.value).trim().toLowerCase();
  }

  addOption(val){
    const customDialog = this.largeDialog;
    this.dialog.open(this.component, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '90vh',
      panelClass: customDialog,
      data: { recipeName : val, createNew: true, key: false , page : this.pages ? this.pages : [] }
    });
  }

  navigate(row) {
    if (this.tabId === 10) {
      this.router.navigate(['/dashboard/account-setup']);
      return;
    }

    const customDialog = ([7 ,9].includes(this.tabId)) ? this.smallDialog : ([1, 3, 4, 11].includes(this.tabId))? this.largeDialog: this.mediumDialog;
    this.dialog.open(this.component, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '90vh',
      panelClass: customDialog,
      data: { elements: row, key:this.pages == 'Party Order' ? false :true , page : this.pages ? this.pages : [] }
    });
  }

  editFun(row: any) {
    if (this.tabId === 10) {
      this.router.navigate(['/dashboard/account-setup'], {
        queryParams: { id: row.tenantId }
      });
      return;
    }

    const customDialog  = [1, 3, 4, 11].includes(this.tabId) ? this.largeDialog : ([7 ,9].includes(this.tabId)) ? this.smallDialog : this.mediumDialog;
    this.dialog.open(this.component, {
      maxHeight: '90vh',
      autoFocus: false,
      disableClose: true,
      panelClass: customDialog,
      data: { elements: row, key: false , page : this.pages ? this.pages : [] }
    });
  }

  checkNodeName(nodeName: string): boolean {
    return this.pages.includes(nodeName);
  }

  issuedToFunc(row) {
    let array
    if (Array.isArray(row)) {
      array = row
    } else {
      array = row.split(',')
    }
    return array
  }

  vendorFunc(row) {
    let array
    if (Array.isArray(row)) {
      array = row
    } else {
      array = row.split(',')
    }
    return array
  }

  protected Filter(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(
      bank.filter(data => data.toLowerCase().indexOf(search) > -1)
    );
  }

  showDataDialog(value, headings) {
    if (headings === 'Vendors' && Array.isArray(value)) {
      this.sharedData.getItemNames.subscribe(obj => {
        this.vendorObject = obj.vendorObject
      })

      value = value.map(item => {
        const foundVendor = this.vendorObject.find(v => v.vendorId === item);
        return foundVendor ? foundVendor.vendorName : null;
      });
    }

    let array: any[]
    if (Array.isArray(value)) {
      array = value
    } else {
      array = value.split(',')
    }
    this.dialog.open(this.component, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '90vh',
      data: { dropDownData: array, key: null, headings: headings }
    });
  }

  isEmpty(value: any): boolean {
    return value === null || value === undefined || value === '';
  }

  public scrollRight(): void {
    this.widgetsContent.nativeElement.scrollTo({ left: (this.widgetsContent.nativeElement.scrollLeft + 150), behavior: 'smooth' });
  }

  public scrollLeft(): void {
    this.widgetsContent.nativeElement.scrollTo({ left: (this.widgetsContent.nativeElement.scrollLeft - 150), behavior: 'smooth' });
  }

  openTableDialog(showTableDialog, value, headings) {
    this.headings = headings
    if ((headings === 'Vendors') && (value instanceof Array)) {
      value = value.join(',')
    }
    if (headings === 'Vendors' && Array.isArray(value)) {
      this.sharedData.getItemNames.subscribe(obj => {
        this.vendorObject = obj.vendorObject
      })
      value = value.map(item => {
        const foundVendor = this.vendorObject.find(v => v.vendorId === item);
        return foundVendor ? foundVendor.vendorName : null;
      });
    }

    let array: any[]
    if (Array.isArray(value)) {
      array = value
    } else {
      array = value.split(',')
    }
    this.filteredData = array;
    this.dropDownData = array;
    this.showTableDialogRef = this.dialog.open(showTableDialog, { maxHeight: '90vh' , panelClass : this.smallDialog});

  }

  showCostDialog(elements){
    this.dialog.open(this.component, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '90vh',
      data: { costDialogkey : true ,elements :elements }
    });
  }

  filterDialog(filterValue) {
    filterValue = filterValue.target.value;
    filterValue = filterValue.trim().toLowerCase();
    this.filteredData = this.dropDownData.filter(item => item.toLowerCase().includes(filterValue));
  }

  closeAddStepDialog() {
    this.showTableDialogRef.close()
  }

  disabledUuid(row) {
    if (row.row_uuid) {
      return true;
    } else {
      return false;
    }

  }

  deleteRowConfirmation(row): void {
    const confirmDelete = confirm(`Are you sure you want to delete ?`);
    if (confirmDelete) {
      const index = this.dataSource.data.indexOf(row);
      if (index > -1) {
        this.dataSource.data.splice(index, 1);
      }
    }
  }

  getInvCount() {
    if (this.page === 'menu master') {
      let posItems = this.sharedData.getPOSItems().value;

      this.POSOnly = posItems['Items'].filter((item) => item.category === 'POS ONLY').length
      this.syncedData = posItems['Items'].filter((item) => item.category === 'BOTH').length
      this.recipeCount = posItems['invCount']
      this.dataSource.data.forEach(element => {
        let requiredData = posItems['res'].find((el) => element.menuItemCode === el.pluCode);
        if (requiredData) {
          element['linkedStatus'] = 'BOTH'
        } else {
          element['linkedStatus'] = 'INVENTORY ONLY'
        }
      })

      this.updatedData = this.dataSource.data;
      this.invItemCount = (this.dataSource.data.filter((el) => el['linkedStatus'] === 'INVENTORY ONLY')).length;
      this.aboveTargetItemCount = (this.dataSource.data.filter((el) => el['linkedStatus'] === 'INVENTORY ONLY')).length;
      this.belowTargetItemCount = (this.dataSource.data.filter((el) => el['linkedStatus'] === 'INVENTORY ONLY')).length;
      this.invCount = (this.dataSource.data.filter((el) => el['linkedStatus'] === 'INVENTORY ONLY')).length;
      this.invDataOnly = (this.dataSource.data.filter((el) => el['linkedStatus'] === 'INVENTORY ONLY'));
      this.POSDataOnly = posItems['Items'].filter((item) => item.category === 'POS ONLY')
      this.cd.detectChanges();
    }
  }

  getPartyCount() {
    if (this.page === 'Party Order') {
            this.dataSource.data.forEach(event => {
        const status = this.getEventStatus(event);
        if (status === 'current') {
          this.activeParties.push(event)
          // this.dataSource.data = this.activeParties
          this.activeParties.sort((a, b) => b - a);
          this.dataSource.data = this.activeParties.map((el, index) => ({ ...el, s_no: index + 1 }));
        } else if (status === 'previous') {
          this.previousParties.push(event)
          this.dataSource.data = this.previousParties
        } else if (status === 'upcoming') {
          this.upcomingParties.push(event)
          this.dataSource.data = this.upcomingParties
        }else if(status === 'discontinued'){
          this.discontinuedParties.push(event)
          this.dataSource.data = this.discontinuedParties
        }
      });
      this.cd.detectChanges();
    }
  }

  getEventStatus(event: any): string {
    // const currentDate = new Date(); // Get today's date
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    const startDate = new Date(event.startDate);
    const endDate = new Date(event.endDate);
    if (event.discontinued === 'yes') {
      return 'discontinued'; // Event is happening today
    }else if ((currentDate >= startDate && currentDate <= endDate) && event.discontinued === 'no') {
      return 'current'; // Event is happening today
    } else if (currentDate > endDate && event.discontinued === 'no') {
      return 'previous'; // Event has finished
    } else if (currentDate < startDate && event.discontinued === 'no') {
      return 'upcoming'; // Event is yet to come
    }
    return '';
  }

  getModifiedFunc(data) {
    const modifiedYesEntries = data.filter(entry => entry.modified === "yes");
    if (modifiedYesEntries.length > 0) {
      return true
    } else {
      return false
    }
  }

  getPosData() {
    this.sharedData.getRecipeNames.subscribe(obj => {
      if (Array.isArray(obj)) {
        let items = obj
        this.invItemCount = (items.filter((el) => el['category'] === 'INVENTORY ONLY')).length;
        this.cd.detectChanges();
      }
    })
  }

  filterInvData() {
    this.invFilter = !this.invFilter;
    if (this.invFilter) {
      this.dataSource.data = this.updatedData.filter((item) => item.linkedStatus === 'INVENTORY ONLY');
    } else {
      this.dataSource.data = this.updatedData;
    }
  }

  filterTargetData(action) {
    if (action === 'Above') {
      this.aboveTargetFilter = !this.aboveTargetFilter;
    } else {
      this.belowTargetFilter = !this.belowTargetFilter;
    }
  }

  showAll() {
    this.showCheckExceptionButton = !this.showCheckExceptionButton;
    this.data.sort((a, b) => {
      if (a.modified === 'yes' && b.modified !== 'yes') {
        return -1;
      }
      if (b.modified === 'yes' && a.modified !== 'yes') {
        return 1;
      }
      return 0;
    });
    this.masterDtaService.inventoryMapping = this.masterDtaService.inventoryMapping.filter(item => item.value !== "error");
    this.displayedColumns = this.masterDtaService.inventoryMapping.map(display => display.value);
    this.showData = this.masterDtaService.inventoryMapping;
    this.data.sort((a, b) => b - a);
    this.dataSource.data = this.data.map((el, index) => ({ ...el, s_no: index + 1 }));
  }

  showError(showErrorDialog) {
    this.showErrorDialogRef = this.dialog.open(showErrorDialog, { maxHeight: '90vh' , panelClass: this.smallDialog});
  }

  closeErrorDialog() {
    this.showErrorDialogRef.close()
  }

  isMandatoryPackagePresent(item: any, pkgs: any) {
    for (const pkg of pkgs) {
      if (pkg.InventoryCode == item.itemCode && pkg.Discontinued != "yes") {
        return true;
      }
    }
    return false;
  }

  isMandatoryMenuRecipePresent(item: any, childItem: any) {
  return childItem.length === 0 ? true : false ;
    // for (const data of childItem) {
    //   if (data.menuItemName == item.menuItemName && data.Discontinued != "yes") {
    //     return true;
    //   } else {
    //     console.log("🚀 ~ HttpTableComponent ~ isMandatoryMenuRecipePresent ~ data:", data)
    //   }
    // }
  }

  isMandatorySubRecipePresent(item: any, childItem: any) {
    for (const data of childItem) {
      if (data.menuItemCode == item.subRecipeCode && data.Discontinued != "yes") {
        return true;
      }
    }
    return false;
  }

  checkException() {
    this.loaderException = true;
    let obj = {};
    var currentUrl = window.location.href;
    var urlData = currentUrl.split('/');
    var currentPage = urlData[urlData.length - 1];
    const hasQuestionMark = currentPage.includes('?');
    const pageType = hasQuestionMark ? currentPage.split('?')[0] : currentPage;

    obj['tenantId'] = this.user.tenantId;
    obj['userEmail'] = this.user.email;
    if (pageType == 'recipe' || pageType == 'Subrecipe Master') {
      obj['type'] = "recipe"
    } else if (pageType == 'user') {
      obj['type'] = "user"
    } else {
      obj['type'] = "inventory"
    }
          this.showCheckExceptionButton = !this.showCheckExceptionButton;
          this.baseData = this.sharedData.getBaseData().value;
          let data = this.baseData
          let unmatchedItemsWithError
          if (this.page === 'menu master') {
            unmatchedItemsWithError = data['menu master'].filter(item => this.isMandatoryMenuRecipePresent(item, data['menu recipes'].filter(childItem => childItem.menuItemCode == item['menuItemCode'])));
          } else if (this.page === 'Subrecipe Master') {
            unmatchedItemsWithError = data['Subrecipe Master'].filter(item => !this.isMandatorySubRecipePresent(item, data['Subrecipe Recipe'].filter(childItem => childItem.subRecipeCode == item['menuItemCode'])));
          } else if (this.page === 'inventory master'){
            unmatchedItemsWithError = data['inventory master'].filter(item => !this.isMandatoryPackagePresent(item, data['packagingmasters'].filter(pkg => pkg.InventoryCode == item['itemCode'])));
          }
          unmatchedItemsWithError = unmatchedItemsWithError.filter(item => item.Discontinued === 'no');
          unmatchedItemsWithError = unmatchedItemsWithError.map((obj: any) => ({ ...obj, error: true }))
          let isDuplicate
          const newColumn = { 'value': 'error', 'displayName': 'Error' }
          let checkObj = { page: this.page,  check: true }
          if (this.page === 'menu master') {
            this.sharedData.clickedException(checkObj);
            this.sharedData.checkSync(true);
            this.component = MenuMasterComponent;
            isDuplicate = this.masterDtaService.indexMapping.some(column => column.value === newColumn.value);
            if (!isDuplicate) {
              this.masterDtaService.indexMapping.splice(2, 0, newColumn);
            }
            this.displayedColumns = this.masterDtaService.indexMapping.map(display => display.value);
            this.showData = this.masterDtaService.indexMapping;
          } else if (this.page === 'Subrecipe Master') {
            this.sharedData.clickedException(obj);
            this.sharedData.checkSync(true);
            this.component = ActionComponentSubrecipeMaster;
            isDuplicate = this.masterDtaService.subrecipeMasterMapping.some(column => column.value === newColumn.value);
            if (!isDuplicate) {
              this.masterDtaService.subrecipeMasterMapping.splice(2, 0, newColumn);
            }
            this.displayedColumns = this.masterDtaService.subrecipeMasterMapping.map(display => display.value);
            this.showData = this.masterDtaService.subrecipeMasterMapping;
          } else  if (this.page === 'inventory master'){
            this.sharedData.clickedException(obj);
            this.sharedData.checkSync(true);
            this.component = ActionComponentInventory;
            isDuplicate = this.masterDtaService.inventoryMapping.some(column => column.value === newColumn.value);
            if (!isDuplicate) {
              this.masterDtaService.inventoryMapping.splice(2, 0, newColumn);
            }
            this.displayedColumns = this.masterDtaService.inventoryMapping.map(display => display.value);
            this.showData = this.masterDtaService.inventoryMapping;
          }
          unmatchedItemsWithError.sort((a, b) => b - a);
          this.dataSource.data = unmatchedItemsWithError.map((el, index) => ({ ...el, s_no: index + 1 }));
          this.dataSource.sort = this.sort;
          this.loaderException = false;
  }

  addMapping(){
    this.dialog.open(MappingComponent, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '90vh',
      panelClass : this.smallDialog
    });
  }

  getPages(){
    this.api.getPages(this.user.tenantId).pipe(first()).subscribe({
      next: (res) => {
        if (res['success']) {
          this.pages = res['pages']
        }
      },
      error: (err) => { console.log(err) }
    });
  }

  addRecipe(val){

  }
  editTree(value){
    const foundData = this.baseData['Roles'].find(item => item.role === value.role && item.module === value.module && item.Page === value.name);
    const customDialog  = [1 ,3 , 4].includes(this.tabId) ? this.largeDialog : ([7 ,9].includes(this.tabId)) ? this.smallDialog : this.mediumDialog;
    this.dialog.open(this.component, {
      maxHeight: '90vh',
      autoFocus: false,
      disableClose: true,
      panelClass: customDialog,
      data: { elements: foundData, key: false, page : this.pages }
    });
  }

  updateRoleTrees(node){
    let foundData = this.baseData['Roles'].find(item => item.role === node.name);
    // let data = node.ch.filter(item => item.role === node.name);
    var filteredModule = node.children.map(item => item.name)
    filteredModule = [...new Set(filteredModule)]
    let obj = {}
    if(foundData){
      obj['role'] = foundData.role
      obj['module'] = ''
      obj['Page'] = ''
      obj['Discontinued'] = 'no'
      obj['modified'] = 'yes'
      obj['tenantId'] = ''
      obj['row_uuid'] = ''

    }else{
      let foundData = this.baseData['Roles'].filter(item => item.role === node.children[0].role);
      foundData = foundData.find(item => item.module === node.name);
      let data = this.baseData['Roles'].filter(item => item.role === foundData.role && item.module === node.name );
      var filteredPages = data.map(item => item.Page)
      filteredPages = [...new Set(filteredPages)]
      obj['role'] = foundData.role
      obj['module'] = foundData.module
      obj['Page'] = ''
      obj['Discontinued'] = 'no'
      obj['modified'] = 'yes'
      obj['tenantId'] = ''
      obj['row_uuid'] = ''
      var disableModule = true
    }
    const customDialog  = [1 ,3 , 4].includes(this.tabId) ? this.largeDialog : ([7 ,9].includes(this.tabId)) ? this.smallDialog : this.mediumDialog;
    this.dialog.open(this.component, {
      maxHeight: '90vh',
      autoFocus: false,
      disableClose: true,
      panelClass: customDialog,
      data: {
        elements: obj,
        key: null ,
        filteredPages : filteredPages ? filteredPages : [] ,
        filteredModule : filteredModule ? filteredModule : [],
        disableModule : disableModule ? disableModule : false,
        page : this.pages
      }
    });
  }

  openInfoDialogs(openInfoDialog , node) {
    this.childNode = node
    this.closeInfoRef = this.dialog.open(openInfoDialog, { maxHeight: '95vh', maxWidth: '500px' });
  }

  closeInfoDialog(){
    this.closeInfoRef.close();
  }

  deleteTree() {
    const updatedData = this.baseData['Roles'].find(item => item.role === this.childNode.role && item.module === this.childNode.module && item.Page === this.childNode.name);
    // if (index !== -1) {
    //   this.baseData['Roles'].splice(index, 1);
    // }
    updatedData['tenantId'] = this.user.tenantId.toString()
    updatedData['modified'] = 'yes'
    updatedData['Discontinued'] = 'yes'
    if (Object.keys(this.baseData).length > 0) {
      let temp = {}
      temp['Roles'] = this.baseData['Roles']
      let required = temp['Roles'].find((el) => el.row_uuid == updatedData['row_uuid'])
      let index = temp['Roles'].indexOf(required)
      temp['Roles'][index] = updatedData;

      this.api.updateData({
        'tenantId' :  this.user.tenantId,
        'userEmail' : this.user.email,
        'data' : temp,
        'type' : 'user'
      }).pipe(first()).subscribe({
        next: (res) => {
          if (res['success']) {
            this.closeRoles()
            this.cd.detectChanges();
          }
        },
        error: (err) => { console.log(err) }
      });
    }
  }

  shouldHighlightNode(data){
    if(data && data.children){
      for (const item of data.children) {
        if(item.children){
          for (const child of item.children) {
            if (child.modified === 'yes') {
              return true;
            }
          }
        }
      }
    }
    return false;
  }

  shouldHighlightchildNode(data){
    for (const child of data.children) {
      if (child.modified === 'yes') {
        return true;
      }
    }
    return false;
  }

  shouldHighlightChildNode(data){
    return data.modified === 'yes'
  }

  shouldHighlightDicNode(data){
    return data.discontinued === 'yes'
  }

  closeRoles() {
    this.dialog.closeAll();
    this.masterDataService.setNavigation('Roles');
    this.router.navigate(['/dashboard/home']);
    this.dialog.closeAll();
  }


  deleteFun(el){
    let indexToRemove
    if(this.page == 'menu master'){
      indexToRemove = this.dataSource.data.findIndex(
        (recipe) => recipe.menuItemCode === el['menuItemCode']
      );
    }else if(this.page == 'Subrecipe Master'){
      indexToRemove = this.dataSource.data.findIndex(
        (recipe) => recipe.menuItemCode === el['menuItemCode']
      );
    }

    if (indexToRemove !== -1) {
      const removedItem = this.dataSource.data[indexToRemove]; // Store the removed item
      this.dataSource.data = this.dataSource.data
        .slice(0, indexToRemove)
        .concat(this.dataSource.data.slice(indexToRemove + 1));
        removedItem.delete = true;
        removedItem.Discontinued = 'yes'

        this.removeData.push(removedItem)
    }
    this.allData = this.dataSource.data
  }



  closeInventory() {
    this.dataSource.data = [];
    this.router.navigate(['/dashboard/home']);
    this.dialog.closeAll();
  }

  ngOnDestroy() {
    this.dialog.closeAll();
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  isNumber(value: any): boolean {
    return !isNaN(this.notify.truncateAndFloor(value)) && isFinite(value);
  }

  viewRecipeData(val , openRecipeDataDialog){
    this.recipesHeading = val
    if(this.recipesHeading == 'INVENTORY'){
      this.recipeData = this.invDataOnly
    }else if(this.recipesHeading == 'POS'){
      this.recipeData = this.POSDataOnly
    }
    this.closeInfoRef = this.dialog.open(openRecipeDataDialog, { maxHeight: '95vh', maxWidth: '500px' });
  }

  closeRecipeDialog(){
    this.closeInfoRef.close();
  }

  filterRecipe(event){
    this.filterRecipeValue = event.target.value;
    this.filterRecipeValue = this.filterRecipeValue.trim().toLowerCase();
    if(this.recipesHeading == 'INVENTORY'){
      this.recipeData = this.invDataOnly.filter(item => item.menuItemName.toLowerCase().includes(this.filterRecipeValue));
    }else if(this.recipesHeading == 'POS'){
      this.recipeData = this.POSDataOnly.filter(item => item.itemName.toLowerCase().includes(this.filterRecipeValue));
    }
    // this.recipeData = this.dropDownData
  }

  isFloat(value: any): boolean {
    return this.isNumber(value) && value % 1 !== 0;
  }

  showParties(party , openPartyDataDialog){
    if(party === 'activeParties'){
      this.partiesName =  this.dataSource.data
      // .map(name => name.partyName);
    }else if(party === 'upcomingParties'){
      this.partiesName =  this.upcomingParties
      // .map(name => name.partyName);
    }else if(party === 'previousParties'){
      this.partiesName =  this.previousParties
      // .map(name => name.partyName);
    }else if(party === 'discontinuedParties'){
      this.partiesName =  this.discontinuedParties
      // .map(name => name.partyName);
    }
    this.partyStatus = party
    this.tempParties = this.partiesName
    this.closeInfoRef = this.dialog.open(openPartyDataDialog, { maxHeight: '95vh', minWidth: '40vw' });
  }

  // filterParties(event){
  //   this.filterRecipeValue = event.target.value;
  //   this.filterRecipeValue = this.filterRecipeValue.trim().toLowerCase();
  //   this.partiesName = this.partiesName.filter(item => item.partyName.toLowerCase().includes(this.filterRecipeValue));
  // }


  filterParties(event) {
    this.filterRecipeValue = event.target.value.trim().toLowerCase();
    if (!this.filterRecipeValue) {
      this.partiesName = [...this.tempParties];
    } else {
      this.partiesName = this.tempParties.filter(item => item.partyName.toLowerCase().includes(this.filterRecipeValue));
    }
  }

  getPartyDraft(){
    this.api.getPartyDraft(this.user.tenantId).subscribe({
      next: (res) => {
        if(this.draftParties.length > 0 && this.partyIndexStatus == 0){
          // this.dialog.open(this.draftDialog, {
          //   autoFocus: false,
          //   disableClose: true,
          //   maxHeight: '90vh',
          //   minWidth: '40vw',
          //   panelClass: customDialog,
          // });
        }
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  closeDialog(){
    this.dialog.closeAll();
  }

  continueDraft(party){
    const customDialog = ([7 ,9].includes(this.tabId)) ? this.smallDialog : ([1, 3, 4, 11].includes(this.tabId))? this.largeDialog: this.mediumDialog;
    this.dialog.open(this.component, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '90vh',
      panelClass: customDialog,
      data: { elements: party, partyDraft: true }
    });
  }

  deleteDraft(party , condition){
    const customDialog = this.largeDialog;
    this.dialogRef = this.dialog.open(this.draftDialog, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '90vh',
      minWidth: '40vw',
      panelClass: customDialog,
    });

    this.dialogRef.afterClosed().subscribe(result => {
      if (result === 'ok') {

      let obj ={
        'tenantId' : this.user.tenantId,
        'partyName' : party.partyName
      }

      if(condition == 0){
        this.api.deletePartyDraft(obj).subscribe({
          next: (res) => {
            if(res['success'] == true){
              this.notify.snackBarShowSuccess('draft deleted successfully');
              // this.getPartyDraft();
              this.sharedData.setDraftClear('draftParty');
              this.closeDialog();
            }
            },
            error: (err) => {
              console.log(err);
            },
          });
      }else{
        this.api.deleteParty(obj).subscribe({
          next: (res) => {
            if(res['success'] == true){
              this.notify.snackBarShowSuccess('party deleted successfully');
              this.sharedData.setDraftClear('cloneParty');
              this.closeDialog();
            }
            },
            error: (err) => {
              console.log(err);
            },
          });
      }
        }
      });
  }

  onOk() {
    this.dialogRef.close('ok');
  }

  deleteAllDraft(){
    const customDialog = this.largeDialog;
    this.dialogRef = this.dialog.open(this.draftDialog, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '90vh',
      minWidth: '40vw',
      panelClass: customDialog,
    });
    this.dialogRef.afterClosed().subscribe(result => {
      if (result === 'ok') {
        let obj ={
          'tenantId' : this.user.tenantId,
        }
        this.api.deleteAllPartyDraft(obj).subscribe({
          next: (res) => {
            if(res['success'] == true){
              this.notify.snackBarShowSuccess('drafts deleted successfully');
              this.sharedData.setDraftClear('draftParty');
              this.closeDialog();
            }
          },
          error: (err) => {
            console.log(err);
          },
        });

        }
      });
  }

  adjustedCreateTs(createTs): Date {
    const date = new Date(createTs);
    date.setMinutes(date.getMinutes() + 330); // Add 330 minutes (5 hours and 30 minutes)
    return date;
  }

  viewParty(party , partyStatus){
    if(partyStatus === 'previous Parties'){
      party['partyClosed'] =  true;
    }
    const customDialog  = [1, 3, 4, 11].includes(this.tabId) ? this.largeDialog : ([7 ,9].includes(this.tabId)) ? this.smallDialog : this.mediumDialog;
    this.dialog.open(this.component, {
      maxHeight: '90vh',
      // minHeight: '90vh',
      autoFocus: false,
      disableClose: true,
      panelClass: customDialog,
      data: { elements: party, key: false , page : this.pages ? this.pages : [] }
    });
  }

  getTransformedPartyStatus(){
    return this.partyStatus.replace(/([Pp])/g, ' $1').trim();
  }

  checkParty(){
    if(this.partyIndexStatus == 'Active'){
      return true
    }else{
      return false
    }
  }

  isValidDate(value: any): Date | null {
    const date = new Date(value);
    return isNaN(date.getTime()) ? null : date; // Check if valid
  }


  cloneParty(row) {
    let partyNames = this.dataSource.data.map(item => item.partyName);

    let baseName = row.partyName;
    let clonedName = baseName;
    let cloneCount = 1;

    const isBaseNew = baseName.toLowerCase() === 'new';
    const isBaseClone = /clone/i.test(baseName);
    const cloneRegex = new RegExp(`^${baseName}(?: clone (\\d+))?$`, 'i');

    partyNames.forEach(existingName => {
      const match = existingName.match(cloneRegex);
      if (match) {
        const existingCloneCount = match[1] ? parseInt(match[1], 10) : 0;
        cloneCount = Math.max(cloneCount, existingCloneCount + 1);
      }
    });

    if (isBaseNew || isBaseClone) {
      clonedName = `${baseName} clone ${cloneCount}`;
    } else {
      if (partyNames.includes(baseName)) {
        clonedName = `${baseName} clone ${cloneCount}`;
      }
    }

    const customDialog = this.largeDialog;
    this.dialogRef = this.dialog.open(this.cloneDialog, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '90vh',
      minWidth: '40vw',
      panelClass: customDialog,
    });

    this.dialogRef.afterClosed().subscribe(result => {
      if (result === 'ok') {
        let startDate = new Date();
        startDate.setHours(0, 0, 0, 0);
        let endDate = new Date(row.endDate);
        endDate.setHours(0, 0, 0, 0);
        let startDateISO = startDate.toLocaleDateString('en-GB', { timeZone: 'Asia/Kolkata' }).split('/').reverse().join('-') + "T00:00:00";
        let endDateISO = endDate.toLocaleDateString('en-GB', { timeZone: 'Asia/Kolkata' }).split('/').reverse().join('-') + "T00:00:00";

        this.api.getPartyCode(this.user.tenantId).subscribe({
          next: (res) => {
            // res['success'] ? (partyCode = res['partyCode']) : this.notify.snackBarShowError('Something Went Wrong!');
            if(res['success']){
              let inputObj = {}
              inputObj['tenantId'] = row.tenantId
              inputObj['restaurantId'] = row.restaurantId
              inputObj['priceTier'] = row.priceTier
              inputObj['partyName'] = clonedName
              inputObj['partyCode'] = res['partyCode']
              inputObj['partyCreator'] = row.partyCreator
              inputObj['phoneNumber'] = row.phoneNumber
              inputObj['address'] = row.address
              inputObj['email'] = row.email
              inputObj['startDate'] = startDateISO
              inputObj['endDate'] = endDateISO
              inputObj['venue'] = row.venue
              inputObj['minPax'] = row.minPax
              inputObj['maxPax'] = row.maxPax
              inputObj['partyDiscount'] = row.partyDiscount
              inputObj['session'] = row.session
              inputObj['extraSupplies'] = row.extraSupplies
              inputObj['price'] = row.price
              inputObj['discontinued'] = row.discontinued
              inputObj['recipes'] = row.recipes
              inputObj['totalSuppliesPrice'] = row.totalSuppliesPrice
              inputObj['totalMenuItemsPrice'] = row.totalMenuItemsPrice
              inputObj['totalMenuItemsReturnsPrice'] = row.totalMenuItemsReturnsPrice
              inputObj['actualPrice'] = row.actualPrice
              inputObj['partyClosed'] = false
              // this.sharedData.copyParty(inputObj);
              // this.notify.snackBarShowSuccess('Party Cloned Successfully');

              this.api.createPartyOrder(inputObj).subscribe({
                next: (res) => {
                  this.notify.snackBarShowSuccess('Party Cloned Successfully');
                  this.sharedData.setDraftClear('cloneParty');
                    this.closeDialog();
                },
                error: (err) => {
                  console.log(err);
                },
              });
            }
          },
          error: (err) => {
            console.log(err);
          },
        });
      }
    });
  }

}