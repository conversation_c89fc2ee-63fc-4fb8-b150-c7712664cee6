<div>
  <div *ngIf="this.page == 'menu master'">
    <div class="container mt-4">
      <div class="row g-4">
        <div class="col-md-6 col-lg-3">
          <div class="card shadow-sm border-light">
            <div class="card-header" style="background-color: #e3f2fd;">
              <div class="mb-0 fw-bold" style="font-size: 1rem;">Total Available Recipes</div>
            </div>
            <div class="card-body d-flex justify-content-between align-items-center">
              <div class="fs-4 fw-bold">{{ recipeCount }}</div>
            </div>
          </div>
        </div>

        <div class="col-md-6 col-lg-3">
          <div class="card shadow-sm border-light">
            <div class="card-header" style="background-color: #e8f5e9;">
              <div class="mb-0 fw-bold" style="font-size: 1rem;">InterLinked Recipes</div>
            </div>
            <div class="card-body d-flex justify-content-between align-items-center">
              <div class="fs-4 fw-bold">{{ syncedData }}</div>
            </div>
          </div>
        </div>

        <div class="col-md-6 col-lg-3">
          <div class="card shadow-sm border-light">
            <div class="card-header" style="background-color: #fce4ec;">
              <div class="mb-0 fw-bold" style="font-size: 1rem;">Unlinked POS Recipes</div>
            </div>
            <div class="card-body d-flex justify-content-between align-items-center" style="margin: -2.5px !important;">
              <div class="d-flex align-items-center gap-3">
                <span class="fs-4 fw-bold">{{ POSOnly }}</span>
                <button class="btn btn-link d-flex align-items-center gap-2 p-0"
                  (click)="viewRecipeData('POS', openRecipeDataDialog)" aria-label="View Detailed Information"
                  style="color: #d81b60;">
                  <span class="ms-2">View Details</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6 col-lg-3">
          <div class="card shadow-sm border-light">
            <div class="card-header" style="background-color: #e8eaf6;">
              <div class="mb-0 fw-bold" style="font-size: 1rem;">Unlinked Inventory Recipes</div>
            </div>
            <div class="card-body d-flex justify-content-between align-items-center" style="margin: -2.5px !important;">
              <div class="d-flex align-items-center gap-3">
                <span class="fs-4 fw-bold">{{ invCount }}</span>
                <button class="btn btn-link d-flex align-items-center gap-2 p-0"
                  (click)="viewRecipeData('INVENTORY', openRecipeDataDialog)" aria-label="View Detailed Information"
                  style="color: #536f97;">
                  <span class="ms-2">View Details</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>


  <div class="warningText" *ngIf="this.page == 'branches' && getModifiedFunc(this.dataSource.data)">
    Any changes made in the branch sheet need to be synced first before using them in other sheets
  </div>

  <div class="my-2 table-actions-container">
    <div class="search-container">
      <input *ngIf="this.page != 'Roles'" type="text" class="search-input" placeholder="Search..."
        (keyup)="applyFilter($event)">
      <mat-icon matSuffix class="search-icon">search</mat-icon>
    </div>

    <div class="action-buttons">
      <button mat-flat-button color="accent" class="mt-1 ms-1" (click)="navigate({})" matTooltip="add">
        <mat-icon *ngIf="!(isSmallScreen$ | async)">library_add</mat-icon> Add
      </button>
      <button mat-flat-button color="accent" class="mt-1 ms-1" (click)="addMapping()" matTooltip="mapping"
        *ngIf="this.page == 'inventory master' && this.checkBulkMapping == true">
        <mat-icon *ngIf="!(isSmallScreen$ | async)">library_add</mat-icon> Mapping
      </button>
      <button mat-flat-button *ngIf="(this.page == 'inventory master') && showCheckExceptionButton" color="accent"
        class="mt-1 ms-1" (click)="checkException()" matTooltip="check quality">
        <div *ngIf="loaderException" class="spinner-border" role="status" class="mr-1">
          <span class="sr-only">Loading...</span>
        </div>
        Check Quality
      </button>
      <button mat-flat-button *ngIf="(this.page == 'inventory master' ) && !showCheckExceptionButton" color="accent"
        class="mt-1 ms-1" (click)="showAll()" matTooltip="add">
        Show All
      </button>
      <button mat-flat-button color="warn" (click)="deleteAllDraft()" *ngIf="this.partyIndexStatus == 'Draft'" class="mt-1 ms-1" matTooltip="delete All">
      <mat-icon>delete</mat-icon> Delete All</button>
    </div>
  </div>

  <div class="tableDiv" #widgetsContent>
    <mat-table [dataSource]="dataSource" matSort *ngIf="isDataReady && this.page != 'Roles'">
      <ng-container *ngFor="let column of this.showData;let i = index" [matColumnDef]="column['value']">
        <div *ngIf="column['value'] == 'position'">
          <mat-header-cell *matHeaderCellDef class="tableSnoCol"> S.No </mat-header-cell>
          <mat-cell *matCellDef="let element;" class="tableSnoCol"> {{element.s_no}} </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'forecast'">
          <mat-header-cell *matHeaderCellDef class="custom-header" mat-sort-header>Forecast</mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <ng-container>{{ row.status.forecast ? 'Active' : 'InActive' }}</ng-container>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'sales'">
          <mat-header-cell *matHeaderCellDef class="custom-header" mat-sort-header>Sales</mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <ng-container>{{ row.status.sales ? 'Active' : 'InActive' }}</ng-container>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'account'">
          <mat-header-cell *matHeaderCellDef class="custom-header" mat-sort-header>Account Status</mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <ng-container>{{ row.status.account ? 'Active' : 'InActive' }}</ng-container>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'action'">
          <mat-header-cell *matHeaderCellDef
            [ngClass]="{ 'subrecClass': this.page == 'Subrecipe Master', 'tableActionColdel': this.page != 'Subrecipe Master', 'partyClass': this.page == 'Party Order'}">
            <!-- 'partyClass': this.partyIndexStatus == 0, -->
            Action </mat-header-cell>
          <mat-cell *matCellDef="let row"
            [ngClass]="{ 'subrecClass': this.page == 'Subrecipe Master', 'tableActionColdel': this.page != 'Subrecipe Master', 'partyClass': this.page == 'Party Order' }">
            <button (click)="editFun(row)" backgroundColor="primary" class="editIconBtn" matTooltip="Edit">
              <mat-icon class="mt-1">edit</mat-icon></button>
              <button *ngIf="this.page == 'Party Order'" matTooltip="Clone" style="margin-left: 10px;"
              backgroundColor="primary" class="editIconBtn" (click)="cloneParty(row)">
              <mat-icon class="mt-1">control_point_duplicate</mat-icon>
              <!-- file_copy  -->
            </button>
              <button (click)="deleteDraft(row , this.partyIndexStatus)" *ngIf="this.page == 'Party Order'" style="margin-left: 10px;"
               backgroundColor="primary" class="editIconBtn" matTooltip="delete">
                <mat-icon class="mt-1">delete</mat-icon></button>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'rate' && (this.page == 'Subrecipe Master' || this.page == 'menu master')">
          <mat-header-cell *matHeaderCellDef class="custom-header"> Cost </mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <div (click)="showCostDialog(row)">
              <button mat-button class="dataHover" matTooltip="View Cost"> <mat-icon>visibility</mat-icon>
                View Cost
              </button>
            </div>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'issuedTo'">
          <mat-header-cell *matHeaderCellDef class="custom-header"> Issued To </mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <div (click)="this.openTableDialog(showTableDialog,row.issuedTo, 'issuedTo')">
              <button mat-button class="dataHover" matTooltip="View IssuedTo"> <mat-icon>visibility</mat-icon>
                issuedTo</button>
            </div>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'vendor'">
          <mat-header-cell *matHeaderCellDef class="custom-header"> Vendors </mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <div (click)="this.openTableDialog(showTableDialog, row.vendor , 'Vendors')">
              <button mat-button class="dataHover" matTooltip="View Vendors"><mat-icon>visibility</mat-icon>
                Vendors</button>
            </div>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'procuredAt'">
          <mat-header-cell *matHeaderCellDef class="custom-header"> Procured At </mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <div (click)="this.openTableDialog(showTableDialog,row.procuredAt, 'procuredAt')">
              <button mat-button class="dataHover" matTooltip="View Procured At"><mat-icon>visibility</mat-icon>
                procuredAt</button>
            </div>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'preparedAt'">
          <mat-header-cell *matHeaderCellDef class="custom-header"> Preparatory Location </mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <div (click)="this.openTableDialog(showTableDialog,row.preparedAt, 'Prepared At')">
              <button mat-button class="dataHover"
                matTooltip="View Preparatory Location"><mat-icon>visibility</mat-icon>
                PreparedAt</button>
            </div>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'email'">
          <mat-header-cell *matHeaderCellDef class="custom-header" mat-sort-header>Email</mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell emailClass">
            <ng-container>{{ row[column['value']] }}</ng-container>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'weight'">
          <mat-header-cell *matHeaderCellDef class="custom-header" mat-sort-header>Weight</mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell emailClass">
            <ng-container>{{ row[column['value']] | number: '1.2-2' }}</ng-container>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'yield'">
          <mat-header-cell *matHeaderCellDef class="custom-header" mat-sort-header>Yield</mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell emailClass">
            <ng-container>{{ row[column['value']] | number: '1.2-2' }}</ng-container>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'leadTime(days)'">
          <mat-header-cell *matHeaderCellDef class="custom-header"> Lead Time (days) </mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">{{ row[column['value']] | number: '1.1-1' }} </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'usedInWorkArea'">
          <mat-header-cell *matHeaderCellDef style="min-width: 12rem;">Used In Work Area </mat-header-cell>
          <mat-cell *matCellDef="let row" style="min-width: 12rem;">
            <div (click)="this.openTableDialog(showTableDialog,row.usedInWorkArea, 'Used In Work Area')">
              <button mat-button class="dataHover" matTooltip="View Used in WorkArea"><mat-icon>visibility</mat-icon>
                Used Work Area</button>
            </div>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'usedAtOutlet'">
          <mat-header-cell *matHeaderCellDef style="min-width: 12rem;">Sales Outlet</mat-header-cell>
          <mat-cell *matCellDef="let row" style="min-width: 12rem;">
            <div (click)="this.openTableDialog(showTableDialog,row.usedAtOutlet, 'used At Outlet')">
              <button mat-button class="dataHover" matTooltip="View Sales Outlet"><mat-icon>visibility</mat-icon> Used
                At Outlet</button>
            </div>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'workAreas'">
          <mat-header-cell *matHeaderCellDef style="min-width: 12rem;">{{column['displayName'] |
            titlecase}}</mat-header-cell>
          <mat-cell *matCellDef="let row" style="min-width: 12rem;">
            <div (click)="this.openTableDialog(showTableDialog,row.workAreas, 'workAreas')">
              <button mat-button class="dataHover"
                matTooltip="View WorkAreas"><mat-icon>visibility</mat-icon>WorkAreas</button>
            </div>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'workArea'">
          <mat-header-cell *matHeaderCellDef class="custom-header">{{column['displayName'] |
            titlecase}}</mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <div (click)="this.openTableDialog(showTableDialog,row.workArea, 'workAreas')">
              <button mat-button class="dataHover"
                matTooltip="View WorkAreas"><mat-icon>visibility</mat-icon>WorkAreas</button>
            </div>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'branchId'">
          <mat-header-cell *matHeaderCellDef class="custom-header">{{column['displayName'] |
            titlecase}}</mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <div (click)="this.openTableDialog(showTableDialog,row.branchId, 'branchId')">
              <button mat-button class="dataHover"
                matTooltip="View BranchId"><mat-icon>visibility</mat-icon>Branches</button>
            </div>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'startDate'">
          <mat-header-cell *matHeaderCellDef class="custom-header" mat-sort-header> {{column['displayName'] | titlecase}} </mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <!-- {{ row[column['value']] | date: 'dd MMMM y' }}  -->
            <ng-container *ngIf="isValidDate(row[column['value']]) as dateValue; else noData">
              {{ dateValue | date: 'dd MMMM y' }}
            </ng-container>
            <ng-template #noData>
              <span>-</span>
            </ng-template>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'endDate'">
          <mat-header-cell *matHeaderCellDef class="custom-header" mat-sort-header> {{column['displayName'] | titlecase}} </mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <!-- {{ row[column['value']] | date: 'dd MMMM y' }} -->
            <ng-container *ngIf="isValidDate(row[column['value']]) as dateValue; else noData">
              {{ dateValue | date: 'dd MMMM y' }}
            </ng-container>
            <ng-template #noData>
              <span>-</span>
            </ng-template>
           </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'Discontinued' || column['value'] == 'discontinued'">
          <mat-header-cell *matHeaderCellDef class="custom-header" mat-sort-header> Status </mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell justify-content-start">
            <div *ngIf="row[column['value']] == 'yes'" class="d-flex align-items-center">
              <mat-icon class="cancelIcon">cancel</mat-icon> &nbsp; Discontinued
            </div>
            <div *ngIf="row[column['value']] == 'no'" class="d-flex align-items-center">
              <mat-icon class="checkIcon">check_circle</mat-icon> &nbsp; Active
            </div>
            <div *ngIf="row[column['value']] != 'no' && row[column['value']] != 'yes'"
              class="d-flex align-items-center">
              <mat-icon class="checkIcon">check_circle</mat-icon> &nbsp; Active
            </div>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] == 'modified'">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="tableModCol"> Modified
          </mat-header-cell>
          <mat-cell *matCellDef="let row" class="tableModCol">
            <div *ngIf="row[column['value']] == 'yes'">
              <mat-chip color="primary">NOT SYNCED</mat-chip>
            </div>
            <div *ngIf="row[column['value']] == 'no' || row[column['value']] == '-'">
              -
            </div>
          </mat-cell>
        </div>
        <div
          *ngIf="column['value'] === 'error' && (this.page == 'inventory master'|| this.page == 'menu master' || this.page == 'Subrecipe Master')">
          <mat-header-cell *matHeaderCellDef mat-sort-header style="min-width: 10rem;"> Exception </mat-header-cell>
          <mat-cell *matCellDef="let row" style="min-width: 10rem;">
            <button matTooltip="show exception" mat-raised-button color="primary" (click)="showError(showErrorDialog)"
              *ngIf="row[column['value']] === true" class="d-flex align-items-center">VIEW
            </button>
          </mat-cell>
        </div>

        <div *ngIf="column['value'] !== 'action'">
          <mat-header-cell *matHeaderCellDef class="custom-header" mat-sort-header>{{ column['displayName'] | titlecase
            }}</mat-header-cell>
          <mat-cell *matCellDef="let row" class="custom-cell">
            <ng-container *ngIf="!isEmpty(row[column['value']])">{{ row[column['value']]}}</ng-container>
            <ng-container *ngIf="isEmpty(row[column['value']])">-</ng-container>
          </mat-cell>
        </div>

      </ng-container>
      <mat-header-row *matHeaderRowDef="displayedColumns ; sticky: true"></mat-header-row>
      <mat-row *matRowDef="let row; columns: displayedColumns;" matRipple
        [ngClass]="{'highlighted-row': row.Discontinued === 'yes'}"></mat-row>
    </mat-table>

    <mat-tree [dataSource]="dataSourceTree" [treeControl]="treeControl" *ngIf="isDataReady && this.page == 'Roles'">
      <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding>
        <button mat-icon-button disabled></button>
        <div class="treeChildClass d-flex align-items-center"
          [ngClass]="{ 'highlighted-dicNode': shouldHighlightDicNode(node) }">
          {{node.name | uppercase}}
          <mat-icon class="highlighted-childNode"
            *ngIf="shouldHighlightChildNode(node) && !shouldHighlightDicNode(node)">star</mat-icon>
        </div>
        <mat-icon class="treeChildIconClass" matTooltip="edit" (click)="editTree(node)">edit</mat-icon>
        <mat-icon class="treeChildIconClass" matTooltip="discontinue"
          (click)="openInfoDialogs(openInfoDialog,node)">cancel_presentation</mat-icon>
        <mat-icon *ngIf="!checkNodeName(node.name)" matTooltip="page doesn't match"
          class="treeChildIconClass">info</mat-icon>
      </mat-tree-node>
      <mat-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding>
        <button mat-icon-button matTreeNodeToggle [attr.aria-label]="'Toggle ' + node.name">
          <mat-icon class="mat-icon-rtl-mirror "
            [ngClass]="{ 'highlighted-node': shouldHighlightNode(node) || shouldHighlightchildNode(node) }">
            {{treeControl.isExpanded(node) ? 'expand_more' : 'chevron_right'}}
          </mat-icon>
        </button>
        <div style="width: 400px;">
          {{node.name | uppercase}}
        </div>
        <mat-icon class="addCircleIcon" matTooltip="add" (click)="updateRoleTrees(node)">add_circle_outline</mat-icon>
      </mat-tree-node>
    </mat-tree>

    <div *ngIf="this.dataSource.data.length == 0">
      <!-- Party Management -->
      <app-empty-state
        *ngIf="this.page === 'Party Order'"
        icon="event_busy"
        title="No Parties Found"
        message="There are no parties to display at the moment. Click the 'Add' button to create a new party."
        [showAction]="true"
        actionLabel="Add Party"
        (actionClick)="navigate({})"
      ></app-empty-state>

      <!-- Inventory Management -->
      <app-empty-state
        *ngIf="this.page === 'inventory master'"
        icon="inventory_2"
        title="No Inventory Items Found"
        message="Your inventory is empty. Click the 'Add' button to start adding inventory items."
        [showAction]="true"
        actionLabel="Add Item"
        (actionClick)="navigate({})"
      ></app-empty-state>

      <!-- Menu Management -->
      <app-empty-state
        *ngIf="this.page === 'menu master'"
        icon="restaurant_menu"
        title="No Menu Items Found"
        message="Your menu is empty. Click the 'Add' button to start creating your menu."
        [showAction]="true"
        actionLabel="Add Menu Item"
        (actionClick)="navigate({})"
      ></app-empty-state>

      <!-- Subrecipe Management -->
      <app-empty-state
        *ngIf="this.page === 'Subrecipe Master'"
        icon="soup_kitchen"
        title="No Subrecipes Found"
        message="You haven't created any subrecipes yet. Click the 'Add' button to create your first subrecipe."
        [showAction]="true"
        actionLabel="Add Subrecipe"
        (actionClick)="navigate({})"
      ></app-empty-state>

      <!-- User Management -->
      <app-empty-state
        *ngIf="this.page === 'Users'"
        icon="people"
        title="No Users Found"
        message="There are no users in the system. Click the 'Add' button to add a new user."
        [showAction]="true"
        actionLabel="Add User"
        (actionClick)="navigate({})"
      ></app-empty-state>

      <!-- Roles Management -->
      <app-empty-state
        *ngIf="this.page === 'Roles'"
        icon="admin_panel_settings"
        title="No Roles Found"
        message="There are no roles defined in the system. Click the 'Add' button to create a new role."
        [showAction]="true"
        actionLabel="Add Role"
        (actionClick)="navigate({})"
      ></app-empty-state>

      <!-- CRM Management -->
      <app-empty-state
        *ngIf="this.page === 'Customers'"
        icon="person"
        title="No Customers Found"
        message="Your customer database is empty. Click the 'Add' button to add your first customer."
        [showAction]="true"
        actionLabel="Add Customer"
        (actionClick)="navigate({})"
      ></app-empty-state>

      <!-- Branches -->
      <app-empty-state
        *ngIf="this.page === 'branches'"
        icon="store"
        title="No Branches Found"
        message="No branches have been added yet. Click the 'Add' button to add your first branch."
        [showAction]="true"
        actionLabel="Add Branch"
        (actionClick)="navigate({})"
      ></app-empty-state>

      <!-- Default for any other pages -->
      <app-empty-state
        *ngIf="this.page !== 'Party Order' &&
               this.page !== 'inventory master' &&
               this.page !== 'menu master' &&
               this.page !== 'Subrecipe Master' &&
               this.page !== 'Users' &&
               this.page !== 'Roles' &&
               this.page !== 'Customers' &&
               this.page !== 'branches'"
        icon="info"
        title="No Data Found"
        message="There are no items to display at the moment. Click the 'Add' button to create a new entry."
        [showAction]="true"
        actionLabel="Add New"
        (actionClick)="navigate({})"
      ></app-empty-state>
    </div>
    <div [ngClass]="{ 'removePaginator': this.page === 'Roles' }">
      <mat-paginator class="mat-paginator-sticky" [pageSize]="10" [pageSizeOptions]="[5, 10, 25, 50, 100]"></mat-paginator>
    </div>
  </div>
</div>

<ng-template #showTableDialog>
  <div>
    <div class="closeBtn">
      <mat-icon (click)="closeAddStepDialog()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
    </div>

    <div class="m-3">
      <div class="text-center mb-3 p-2 bottomTitles">
        <span>{{headings | uppercase }}</span>
      </div>
      <div>
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput placeholder="Search" (keyup)="filterDialog($event)" aria-label="Search">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
      </div>

      <div>
        <div *ngFor="let data of filteredData;let i = index" class="my-2">
          {{i + 1}}. {{data}}
        </div>
        <div *ngIf="filteredData?.length == 0">
          <app-empty-state
            icon="search_off"
            title="No Results Found"
            message="No matching data found for your search criteria."
            customClass="dialog-empty-state"
          ></app-empty-state>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #showErrorDialog>
  <div class="m-3">
    <div class="text-center mt-3 p-2 bottomTitles">
      <span>Errors</span>
    </div>
    <div class="bt-3 my-3">
      <div *ngIf="this.page == 'inventory master'">
        1. Please add the required package; a minimum of one package is necessary
      </div>
      <div *ngIf="this.page == 'menu master'">
        1. Please add the required menu recipe; a minimum of one menu recipe is necessary
      </div>
      <div *ngIf="this.page == 'Subrecipe Master'">
        1. Please add the required subrecipe recipe; a minimum of one subrecipe recipe is necessary
      </div>
    </div>
    <div>
      <button mat-flat-button color="warn" class="mb-2 floatRightBtn" (click)="closeErrorDialog()" matTooltip="close">
        Close
      </button>
    </div>
  </div>
</ng-template>

<ng-template #openInfoDialog>
  <div class="registration-form px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span>Discontinue Item</span>
    </div>
    <div class="m-3 infoText">
      Are you sure to discontinue?
    </div>
    <div class="text-end m-2">
      <button (click)="deleteTree()" mat-raised-button color="accent" matTooltip="Update" class="m-1">
        Yes</button>
      <button (click)="closeInfoDialog()" mat-raised-button matTooltip="close" class="m-1">
        No</button>
    </div>
  </div>
</ng-template>

<ng-template #openRecipeDataDialog>
  <div class="closeBtn">
    <mat-icon (click)="closeRecipeDialog()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
  </div>

  <div class="registration-form px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span style="text-transform: uppercase;">Unlinked {{recipesHeading}} RECIPES</span>
    </div>

    <div>
      <mat-form-field appearance="outline">
        <mat-label>Search</mat-label>
        <input matInput placeholder="Search" (keyup)="filterRecipe($event)" aria-label="Search">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
    </div>

    <div style="font-size: medium;">
      <div *ngIf="recipeData.length > 0 && recipesHeading == 'INVENTORY'">
        <div *ngFor="let val of recipeData" class="d-flex align-items-center">
          <span>{{val.menuItemName}}</span>
        </div>
      </div>

      <div *ngIf="recipeData.length > 0 && recipesHeading == 'POS'">
        <div *ngFor="let val of recipeData" class="d-flex align-items-center justify-content-between">
            <span>{{val.itemName}}</span> <mat-icon matTooltip="add recipe" class="posAddIcon" (click)="addOption(val.itemName)">add</mat-icon>
        </div>
      </div>

      <div *ngIf="recipeData.length == 0">
        <app-empty-state
          icon="restaurant_menu"
          title="No {{recipesHeading}} Recipes Found"
          message="There are no recipes to display at the moment."
          customClass="dialog-empty-state"
        ></app-empty-state>
      </div>
    </div>
  </div>
</ng-template>


<ng-template #openPartyDataDialog>
  <div class="closeBtn">
    <mat-icon (click)="closeRecipeDialog()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
  </div>

  <div class="registration-form px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span style="text-transform: uppercase;">{{ getTransformedPartyStatus() | uppercase}}</span>
    </div>

    <div>
      <mat-form-field appearance="outline">
        <mat-label>Search</mat-label>
        <input matInput placeholder="Search" (keyup)="filterParties($event)" aria-label="Search">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
    </div>

    <div style="font-size: medium; min-width: 410px;">
      <div *ngIf="partiesName.length > 0">
        <!-- <div *ngFor="let val of partiesName" class="d-flex align-items-center">
          <span>{{val}}</span>
        </div> -->

        <table class="table">
          <thead>
            <tr>
              <th scope="col"><b>#</b></th>
              <th scope="col"><b>Party Name</b></th>
              <th scope="col"><b>Created Date And time</b></th>
              <th scope="col"><b>Action</b></th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let party of partiesName; let i = index">
              <th scope="row">{{ i + 1 }}</th>
              <td>{{ party.partyName | uppercase }}</td>
              <td>{{ adjustedCreateTs(party.createTs) | date: 'y-MM-dd HH:mm:ss' }}</td>
              <td>
                <mat-icon style="cursor: pointer;" matTooltip="view" (click)="viewParty(party , getTransformedPartyStatus())">remove_red_eye</mat-icon>
              </td>
            </tr>
          </tbody>
        </table>

      </div>

      <div *ngIf="partiesName.length == 0">
        <app-empty-state
          icon="event_busy"
          title="No Parties Found"
          message="There are no parties to display at the moment."
          customClass="dialog-empty-state"
        ></app-empty-state>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #draftDialog>

  <div class="registration-form px-3">
    <div class="text-center mb-2 p-2 bottomTitles" style="margin-top: 20px;">
      <span>Delete Party</span>
    </div>
    <div class="y-2">

      <div style="font-size: larger;
              font-weight: bold;
              margin: 15px 0px;">
        Are you sure you want to delete the Party?
      </div>
      <mat-divider></mat-divider>

      <div class="text-end mt-3">
        <button (click)="onOk()" mat-raised-button matTooltip="delete party" matTooltip="delete" style="margin-right: 10px;">
          ok</button>
        <button (click)="closeDialog()" mat-raised-button color="warn" matTooltip="close">
          Close</button>
      </div>

    </div>
  </div>
</ng-template>


<ng-template #cloneDialog>
  <div class="registration-form px-3">
    <div class="text-center mb-2 p-2 bottomTitles" style="margin-top: 20px;">
      <span>Clone Party</span>
    </div>
    <div class="y-2">
      <div style="font-size: larger;
              font-weight: bold;
              margin: 15px 0px;">
        Are you sure you want to clone the {{this.baseName}} Party?
      </div>
      <mat-divider></mat-divider>

      <div class="text-end mt-3">
        <button (click)="onOk()" mat-raised-button matTooltip="delete party" matTooltip="delete" style="margin-right: 10px;">
          ok</button>
        <button (click)="closeDialog()" mat-raised-button color="warn" matTooltip="close">
          Close</button>
      </div>

    </div>
  </div>
</ng-template>