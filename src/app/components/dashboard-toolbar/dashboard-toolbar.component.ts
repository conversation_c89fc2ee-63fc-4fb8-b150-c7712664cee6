import {
  ChangeDetectionStrategy,
  Component,
  Input,
  Output,
  OnInit,
  OnChanges,
  OnDestroy,
  SimpleChanges,
  EventEmitter,
  ViewChild,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { GlobalsService } from 'src/app/services/globals.service';
import { DialogComponent } from 'src/app/pages/dialog/dialog.component';
import { MatMenuModule } from '@angular/material/menu';
import { Router, RouterModule } from '@angular/router';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AuthService } from 'src/app/services/auth.service';
import { ShareDataService } from 'src/app/services/share-data.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCardModule } from '@angular/material/card';
import { first } from 'rxjs';
import { NotificationService } from 'src/app/services/notification.service';
import { MatOption } from '@angular/material/core';
import { MatSidenav } from '@angular/material/sidenav';
import { ReplaySubject, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';
import { Validators } from '@angular/forms';
@Component({
  selector: 'app-dashboard-toolbar',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatToolbarModule,
    MatMenuModule,
    MatSelectModule,
    MatTooltipModule,
    MatCardModule,
    RouterModule,
  ],
  templateUrl: './dashboard-toolbar.component.html',
  styleUrls: ['./dashboard-toolbar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardToolbarComponent implements OnInit, OnChanges, OnDestroy {
  user: any;
  @Input() showNavbarToggleButton = false;
  @Input() menuItems: any[] = null;
  @Input() logoUrl: string = '';
  @Output() toggleMenu = new EventEmitter();
  public branchList = [];
  public branches = new FormControl('');
  public globalLocation: FormControl = new FormControl();
  public VendorBank: any[] = [];
  public vendorFilterCtrl: FormControl = new FormControl();
  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  cardDesc: string = '';
  enableSettingBtn: boolean;
  @ViewChild(MatSidenav)
  sidenav!: MatSidenav;
  @Input() showBanner: boolean = false;
  @ViewChild('allSelected') private allSelected: MatOption;

  // No need to track if menu items are loaded - we use hardcoded placeholders
  @Input() message: string = 'Update to latest version by pressing';
  versionNumber: string;
  rolesList: any = [];
  links: any = [];
  access: any;
  filteredBranches: any[] = [];

  constructor(
    private selectedBranchesService: ShareDataService,
    private dialog: MatDialog,
    private router: Router,
    private auth: AuthService,
    private sharedData: ShareDataService,
    private cd: ChangeDetectorRef,
    private notify: NotificationService
  ) {
    this.user = this.auth.getCurrentUser();
    this.cardDesc += this.user.role;

    if (this.user.restaurantAccess && this.user.restaurantAccess.length > 0) {
      this.globalLocation.setValue(this.user.restaurantAccess[0]);
      this.sharedData.setGlLocation(this.user.restaurantAccess[0]);
    }

    this.sharedData.getVersionNumber
      .pipe(takeUntil(this._onDestroy))
      .subscribe((data) => {
        this.versionNumber = data;
        this.cd.markForCheck();
      });

    this.sharedData.checkSettingAvailable
      .pipe(takeUntil(this._onDestroy))
      .subscribe((data) => {
        this.enableSettingBtn = data;
        this.cd.markForCheck();
      });
  }
  ngOnInit() {
    // Initialize branch data
    if (this.user && this.user.restaurantAccess) {
      this.VendorBank = this.user.restaurantAccess.filter(
        (branch: any) => branch && branch.branchName
      );
      this.vendorsBanks.next(this.VendorBank.slice());
      this.selectedBranchesService.updateSelectedBranches(
        this.user.restaurantAccess
      );
    }

    // Initialize filter control
    this.vendorFilterCtrl = new FormControl(
      '',
      Validators.pattern('[a-zA-Z0-9\\s]*')
    );

    // Set up filter change subscription
    this.vendorFilterCtrl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe((newValue) => {
        this.vendorfilterBanks(newValue);
      });

    // Check if menu items are loaded
    this.checkMenuItems();
  }

  // Simplified change detection
  ngOnChanges(changes: SimpleChanges): void {
    // Always mark for check when menu items or logo changes
    if (changes['menuItems'] || changes['logoUrl']) {
      this.cd.markForCheck();
    }
  }

  // Simplified method - no need to track loading state
  checkMenuItems() {
    // No complex logic needed - we use hardcoded placeholders
    this.cd.markForCheck();
  }



  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.filteredBranches = this.user.restaurantAccess.filter(
      (branch) =>
        branch &&
        branch.branchName.toLowerCase().includes(filterValue.toLowerCase())
    );
  }

  setting() {
    this.router.navigate(['/dashboard/setting']);
  }

  protected vendorfilterBanks(newValue?: unknown) {
    if (!this.VendorBank) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.vendorsBanks.next(this.VendorBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (!search.includes(' ')) {
      this.vendorsBanks.next(
        this.VendorBank.filter((branch) =>
          branch.branchName.toLowerCase().replace(/\s/g, '').includes(search)
        )
      );
    } else {
      const searchTerms = search
        .split(' ')
        .filter((term) => term.trim() !== '');
      this.vendorsBanks.next(
        this.VendorBank.filter((branch) => {
          const branchNameLowerCase = branch.branchName.toLowerCase();
          return searchTerms.every((term) =>
            branchNameLowerCase.includes(term)
          );
        })
      );
    }
  }

  logout() {
    const dialogRef = this.dialog.open(DialogComponent, {
      autoFocus: false,
      data: {
        message: 'Are you sure you want to logout?',
        title: 'Logout',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        localStorage.clear();
        sessionStorage.clear();
        window.location.reload();
        this.router.navigate(['/signin']);
      }
    });
  }

  restaurantChange(event) {
    this.sharedData.setGlLocation(event);
  }

  /**
   * Handle logo loading errors
   */
  handleLogoError(event: any) {
    // Hide the broken image
    event.target.style.display = 'none';
    // Could set a default logo here if needed
  }

  /**
   * Clean up subscriptions when component is destroyed
   */
  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  /**
   * TrackBy function for menu items to optimize rendering
   * This helps Angular identify which items have changed and only re-render those
   */
  trackByPath(_index: number, item: any): string {
    return item?.path || _index.toString();
  }
}
