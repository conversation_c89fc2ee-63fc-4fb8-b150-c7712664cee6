:host {
    display: grid;
    grid-template-columns: var(--avatar-size) 1fr;
    grid-auto-rows: auto;
    gap: var(--grid-gap);
    max-width: 750px;
    width: 100%;
    margin: 0 auto;
    padding: 0.75rem;
  }
  
  .timeline-avatar {
    position: relative;
  
    &:first-child::before {
      content: '';
      position: absolute;
      background-color: var(--subdued-color);
      width: 1px;
      left: 0;
      right: 0;
      margin: 0 auto;
      top: calc(-1 * (var(--group-header-spacing) - 4px));
      bottom: 100%;
    }
  
    &:not(.last)::after {
      content: '';
      position: absolute;
      background-color: var(--subdued-color);
      width: 1px;
      left: 0;
      right: 0;
      margin: 0 auto;
      top: calc(var(--avatar-size) + 4px);
      bottom: calc(-1 * var(--avatar-size));
    }
  
    // .last::after {
    //   display: none;
    //   bottom: 0;
    // }
  }
  
  .timeline-card {
    background-color: white;
    width: 100%;
    border: 1px solid var(--subdued-color);
    border-radius: 0.25rem;
    padding: 0.75rem;
  
    .timeline-header {
      display: flex;
      gap: 0.5rem;
  
      svg {
        width: 20px;
        height: 20px;
  
        & > path {
          fill: var(--subdued-color);
        }
      }
    }
  
    .timeline-title {
      flex-grow: 1;
      font-size: 1.1rem;
    }
  
    .timeline-time {
      color: var(--subdued-color);
    }
  
    .timeline-content {
      background-color: #f0f0f0;
      margin-top: 0.5rem;
      padding: 0.25rem;
      border-radius: 0.25rem;
    }
  }
  