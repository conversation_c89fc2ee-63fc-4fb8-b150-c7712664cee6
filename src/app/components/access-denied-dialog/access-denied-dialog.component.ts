import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

export interface AccessDeniedDialogData {
  title: string;
  message: string;
}

@Component({
  selector: 'app-access-denied-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <div class="access-denied-dialog">
      <div class="dialog-header">
        <mat-icon class="warning-icon">warning</mat-icon>
        <h2 mat-dialog-title>{{ data.title }}</h2>
      </div>
      <mat-dialog-content>
        <p class="message">{{ data.message }}</p>
      </mat-dialog-content>
      <mat-dialog-actions align="center">
        <button mat-raised-button color="primary" [mat-dialog-close]="true" class="ok-button">
          OK
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .access-denied-dialog {
      text-align: center;
      padding: 12px;
      width: 280px;
      max-width: 90vw;
    }

    .dialog-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 8px;
    }

    .warning-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
      color: #ff9800;
      margin-bottom: 6px;
    }

    h2 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .message {
      font-size: 12px;
      line-height: 1.3;
      color: #666;
      margin: 8px 0;
      text-align: center;
    }

    .ok-button {
      min-width: 60px;
      height: 32px;
      font-size: 12px;
      font-weight: 500;
    }

    mat-dialog-actions {
      padding: 8px 0 0 0;
      margin: 0;
    }
  `]
})
export class AccessDeniedDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<AccessDeniedDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AccessDeniedDialogData
  ) {}
}
