@use '../../../styles/variables' as vars;

/* Chat layout */
.chat-layout {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

/* Set the width ratio to 60-40 */
.chat-container {
  flex: 0.6; /* 60% of the space */
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
  max-height: 600px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  background-color: #fff;
  border: 1px solid #e0e0e0;
  position: relative; /* For overlay positioning */
}

.restaurant-data-panel {
  flex: 0.4; /* 40% of the space */
  width: auto; /* Override the fixed width */
  height: 100%;
  min-height: 400px;
  max-height: 600px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
  font-family: 'Roboto', sans-serif;
}

/* Overlay styles */
.chat-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.overlay-content {
  text-align: center;
  padding: 2rem;
  max-width: 80%;
}

.overlay-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  color: vars.$orange-primary; /* Orange color for restaurant icon */
  margin-bottom: 1rem;
}

.overlay-content h2 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.5rem;
}

.overlay-content p {
  margin-bottom: 1.5rem;
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}

.start-button {
  padding: 0.5rem 1.5rem;
  font-size: 1rem;
  background-color: vars.$orange-primary; /* Orange color */
  color: white;
  border: none;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  transition: background-color 0.3s;
}

.start-button:hover {
  background-color: vars.$orange-light; /* Lighter orange on hover */
}

.start-button mat-icon {
  margin-right: 8px;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: white;
  color: #333;
  height: 48px;
  border-bottom: 1px solid #e0e0e0;
}

.chat-title {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-weight: 500;
  font-size: 16px;
}

.chat-icon {
  margin-right: 8px;
  font-size: 20px;
  height: 20px;
  width: 20px;
}

.assistant-title {
  font-size: 14px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: white;
  background-image: none;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 8px;
}

.message-container {
  display: flex;
  margin-bottom: 6px;
  max-width: 85%;
  width: auto; /* Let content determine width */
  animation: fadeIn 0.2s ease-in-out;
  will-change: transform, opacity;
  transform: translateZ(0);
  align-self: flex-start;
  margin-left: 8px;
  flex-wrap: wrap; /* Ensure proper wrapping */
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Base styling for all user message containers */
.user-message {
  margin-left: auto;
  margin-right: 8px;
  align-self: flex-end; /* Align user messages to the right */
  display: flex;
  justify-content: flex-end; /* Align content to the right */
  flex-direction: row; /* Ensure horizontal layout */
  max-width: 85%; /* Maximum width for all user messages */
  width: auto; /* Let the content determine the width */
  flex-wrap: wrap; /* Ensure proper wrapping */
}

/* Styling for number messages */
.user-message.number-container .message-content {
  width: auto !important;
  min-width: 40px !important;
  padding: 8px 16px !important;
  border-radius: 16px !important;
  background-color: #e8e8e8;
  text-align: center;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

/* Styling for short answer messages */
.user-message.short-answer-container .message-content {
  width: auto !important;
  min-width: 40px !important;
  padding: 8px 16px !important;
  border-radius: 16px !important;
  background-color: #e8e8e8;
  text-align: center;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.bot-message {
  margin-right: auto;
}

.message-content {
  padding: 10px 14px;
  border-radius: 14px;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  max-width: 100%;
  width: auto; /* Let content determine width */
  word-wrap: break-word;
  word-break: break-word; /* Ensure text wrapping */
  overflow-wrap: break-word; /* Better browser compatibility */
  line-height: 1.5;
  display: inline-block; /* Better text wrapping */

  .message-wrapper {
    display: flex;
    flex-wrap: wrap; /* Added to ensure proper wrapping */
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
    width: 100%; /* Added to ensure full width */
  }

  /* Special styling for number and short answer message wrappers */
  .number-message .message-wrapper,
  .short-answer-message .message-wrapper {
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }

  .message-timestamp {
    font-size: 0.7rem;
    color: rgba(0, 0, 0, 0.5);
    padding: 0 2px;
    font-style: italic;
    white-space: nowrap;
    align-self: flex-end;
    margin-left: auto;
  }

  /* Special styling for number and short answer message timestamps */
  .number-message .message-timestamp,
  .short-answer-message .message-timestamp {
    font-size: 0.6rem;
    margin-top: 2px;
    margin-left: 0 !important;
    text-align: center;
    opacity: 0.7;
    align-self: center;
  }

  .message-text {
    white-space: pre-wrap;
    word-break: break-word;
    overflow-wrap: break-word;
    flex: 1;
    width: 100%; /* Ensure full width */
    max-width: 100%; /* Ensure content doesn't overflow */
    display: inline-block; /* Better text wrapping */
  }

  /* Special styling for number and short answer message text */
  .number-message .message-text,
  .short-answer-message .message-text {
    font-weight: 500;
    font-size: 16px;
    text-align: center;
    padding: 0 !important;
    margin: 0 !important;
    flex: none;
    width: auto;
    display: inline-block;
  }

  // Markdown styling
  h1, h2, h3, h4, h5, h6 {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    font-weight: 600;
  }

  h1 { font-size: 1.5em; }
  h2 { font-size: 1.3em; }
  h3 { font-size: 1.2em; }

  p {
    margin-top: 0;
    margin-bottom: 0.8em; /* Increased spacing */
    font-size: 16px; /* Larger font size */
  }

  p:last-child {
    margin-bottom: 0; /* Remove margin from last paragraph */
  }

  ul, ol {
    margin-top: 0;
    margin-left: 1.8em; /* Increased indentation */
    margin-bottom: 0.8em; /* Increased spacing */
    padding-left: 0; /* Remove default padding */
  }

  ul:last-child, ol:last-child {
    margin-bottom: 0; /* Remove margin from last list */
  }

  li {
    margin-bottom: 0.5em; /* Increased spacing between list items */
    font-size: 16px; /* Larger font size */
  }

  li:last-child {
    margin-bottom: 0; /* No margin on last list item */
  }

  code {
    font-family: 'Courier New', monospace;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    white-space: pre-wrap; /* Ensure text wrapping */
    word-break: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
  }

  pre {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 6px 8px; /* Reduced vertical padding */
    border-radius: 4px;
    overflow-x: auto;
    margin-top: 0.3em;
    margin-bottom: 0.5em; /* Reduced from 0.8em */
    white-space: pre-wrap; /* Ensure text wrapping */
    word-break: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    width: 100%;
  }

  pre:last-child {
    margin-bottom: 0; /* Remove margin from last pre */
  }

  blockquote {
    border-left: 3px solid #ccc;
    padding: 2px 0 2px 10px; /* Add vertical padding */
    margin: 0.3em 0 0.5em 0; /* Compact margins */
    color: #666;
  }

  blockquote:last-child {
    margin-bottom: 0; /* Remove margin from last blockquote */
  }

  blockquote p {
    margin: 0.2em 0; /* Minimal paragraph spacing in blockquotes */
  }

  a {
    color: #0077cc;
    text-decoration: underline;
  }

  table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 0.8em;
  }

  th, td {
    border: 1px solid #ddd;
    padding: 6px;
  }

  th {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.message-content:hover {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.user-message .message-content {
  background-color: #f5f5f5;
  color: #333;
  border-top-right-radius: 4px;

  // Override some markdown styles for user messages
  code {
    background-color: rgba(255, 255, 255, 0.2);
  }

  pre {
    background-color: rgba(255, 255, 255, 0.1);
  }

  blockquote {
    border-left-color: rgba(255, 255, 255, 0.5);
    color: rgba(255, 255, 255, 0.9);
  }

  a {
    color: #90caf9;
  }

  th {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .message-timestamp {
    color: rgba(0, 0, 0, 0.5);
  }

  th, td {
    border-color: rgba(255, 255, 255, 0.3);
  }
}

.user-message .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.bot-message .message-content {
  background-color: white;
  border-top-left-radius: 4px;
  border: 1px solid #e0e0e0;
}

.message-text {
  font-size: 15px;
  line-height: 1.5;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  width: 100%;
  max-width: 100%;
  display: inline-block; /* Better text wrapping */
}



/* Styling for number messages */
.number-message {
  min-width: 40px !important;
  max-width: none !important;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px !important;
  border-radius: 18px !important;
}

/* Styling for short answer messages */
.short-answer-message {
  min-width: 40px !important;
  max-width: none !important;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px !important;
  border-radius: 18px !important;
}



.chat-input {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  background-color: white;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);
  min-height: 50px;
}

.message-field {
  flex: 1;
  margin-right: 12px;
  width: 100%; /* Ensure full width */
}

.message-field ::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

.message-field ::ng-deep .mat-mdc-text-field-wrapper {
  border-radius: 24px;
  padding: 0 8px;
}

.message-field ::ng-deep .mat-mdc-form-field-flex {
  margin-top: -4px;
}

.submit-spinner {
  margin-left: 8px;
}

/* Make sure the form field takes up the full width */
::ng-deep .message-field .mat-mdc-form-field-infix {
  width: 100% !important;
}

/* Restaurant Data Panel Styles */
.restaurant-empty-state {
  height: 100%;
  background-color: white;
  margin-bottom: 20px;

  ::ng-deep .empty-state-container {
    height: 100%;
    min-height: 250px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    }

    .icon-container {
      width: 80px;
      height: 80px;
    }

    .empty-state-icon {
      font-size: 40px;
      height: 40px;
      width: 40px;
      color: vars.$orange-primary; /* Orange color for restaurant icon */
    }

    .empty-state-title {
      font-size: 18px;
      font-weight: 500;
      margin: 0 0 12px 0;
      color: #555;
    }

    .empty-state-message {
      font-size: 14px;
      line-height: 1.5;
      margin: 0;
      max-width: 240px;
    }
  }
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  background-color: white;
  color: #333;
  height: 48px;
  border-bottom: 1px solid #e0e0e0;

  .header-left {
    display: flex;
    align-items: center;
  }

  .header-right {
    display: flex;
    align-items: center;
  }

  .action-button {
    color: #666;
    transition: all 0.3s ease;
    margin-left: 4px;
  }

  .refresh-button:hover {
    color: vars.$soft-blue; // Blue for refresh
  }

  .save-button:hover {
    color: vars.$sage-green; // Green for save
  }

  .delete-button:hover {
    color: vars.$dusty-rose; // Red for delete
  }

  .rotating {
    animation: rotate 1.5s linear infinite;
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

.panel-header .header-left mat-icon {
  margin-right: 8px;
  font-size: 20px;
  height: 20px;
  width: 20px;
  color: #f57c00; /* Orange color for restaurant icon */
}

.panel-header .header-left h2 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.panel-content {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  background-color: #f5f5f5;
  gap: 20px;
  display: flex;
  flex-direction: column;
}

.data-section {
  margin-bottom: 24px;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  font-size: 16px;

  &:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
  }
}

.data-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.data-section h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: #333;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;

  mat-icon {
    margin-right: 10px;
    color: #f57c00;
    font-size: 20px;
    height: 20px;
    width: 20px;
  }
}

.summary-section {
  background-color: #fff9f0;
}

.outlet-section {
  background-color: #f9f9f9;
}

.work-area-tag {
  display: inline-block;
  padding: 2px 0;
}

.data-item {
  display: flex;
  margin-bottom: 16px;
  font-size: 16px;
  line-height: 1.5;
  padding: 6px 0;
  border-bottom: 1px dashed #f0f0f0;

  &:last-child {
    margin-bottom: 0;
    border-bottom: none;
  }
}

.data-item .label {
  font-weight: 600;
  color: #444;
  min-width: 140px;
  padding-right: 12px;
}

.data-item .value {
  color: #333;
  flex: 1;
  word-break: break-word;
  line-height: 1.6;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 8px 16px;
  border-radius: 18px;
  width: auto;
  justify-content: flex-start;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  margin: 8px 0 8px 16px;
  animation: fadeIn 0.3s ease-in-out;
  border: 1px solid #e0e0e0;
}

.typing-dots {
  display: flex;
  align-items: center;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  margin: 0 3px;
  background-color: #57705d;
  border-radius: 50%;
  display: inline-block;
  opacity: 0.7;
}

.typing-indicator span:nth-child(1) {
  animation: typing 1.4s infinite ease-in-out;
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation: typing 1.4s infinite ease-in-out;
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation: typing 1.4s infinite ease-in-out;
  animation-delay: 0.4s;
}

.typing-text {
  margin-left: 12px;
  font-size: 13px;
  color: #555;
  font-weight: 400;
}

@keyframes typing {
  0% { transform: translateY(0) scale(1); opacity: 0.5; }
  50% { transform: translateY(-4px) scale(1.2); opacity: 0.9; }
  100% { transform: translateY(0) scale(1); opacity: 0.5; }
}

/* Blinking cursor animation */
@keyframes cursor-blink {
  0% { opacity: 1; }
  50% { opacity: 0; }
  100% { opacity: 1; }
}

:host ::ng-deep .blinking-cursor {
  display: inline-block;
  animation: cursor-blink 0.5s infinite;
  font-weight: bold;
  color: #1976d2;
  will-change: opacity;
  transform: translateZ(0); /* Force GPU acceleration */
}
