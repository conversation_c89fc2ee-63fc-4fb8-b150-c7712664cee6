import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Pipe({
  name: 'safeHtml',
  standalone: true
})
export class SafeHtmlPipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}

  transform(value: string): SafeHtml {
    const textWithLineBreaks = value ? value.replace(/\n/g, '<br>') : '';    
    return this.sanitizer.bypassSecurityTrustHtml(textWithLineBreaks);
  }
}
