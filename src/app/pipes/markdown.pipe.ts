import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { marked } from 'marked';

@Pipe({
  name: 'markdown',
  standalone: true
})
export class MarkdownPipe implements PipeTransform {
  private markedRenderer: marked.Renderer;

  constructor(private sanitizer: DomSanitizer) {
    this.initializeRenderer();
  }

  private initializeRenderer() {
    this.markedRenderer = new marked.Renderer();

    // Paragraph rendering with proper spacing
    this.markedRenderer.paragraph = (text) => `<p style="margin-bottom: 1em;">${text}</p>`;
    
    // List rendering with proper spacing and styling
    this.markedRenderer.list = (body, ordered, start) => {
      const type = ordered ? 'ol' : 'ul';
      const startAttr = (ordered && start !== 1) ? ` start="${start}"` : '';
      const listStyle = ordered ? 'decimal' : 'disc';
      return `<${type}${startAttr} style="margin-bottom: 1em; padding-left: 2em; list-style-type: ${listStyle};">${body}</${type}>`;
    };

    // List item rendering with proper spacing
    this.markedRenderer.listitem = (text) => {
      return `<li style="margin-bottom: 0.5em; display: list-item;">${text}</li>`;
    };

    // Code block rendering
    this.markedRenderer.code = (code, language) => {
      return `<pre style="white-space: pre-wrap; word-break: break-word; overflow-wrap: break-word; max-width: 100%; overflow-x: auto; background-color: #f5f5f5; padding: 1em; border-radius: 4px; margin: 1em 0;"><code class="language-${language || 'text'}" style="font-size: 1em;">${code}</code></pre>`;
    };

    // Heading rendering with proper spacing and sizing
    this.markedRenderer.heading = (text, level) => {
      return `<h${level} style="margin-top: 1em; margin-bottom: 0.5em; font-weight: 600; font-size: ${1.5 - (level * 0.1)}em;">${text}</h${level}>`;
    };

    // Table rendering with proper styling
    this.markedRenderer.table = (header, body) => {
      return `<table style="border-collapse: collapse; width: 100%; margin-bottom: 1em; border: 1px solid #ddd;">
        <thead style="background-color: #f5f5f5;">${header}</thead>
        <tbody>${body}</tbody>
      </table>`;
    };

    // Table row rendering
    this.markedRenderer.tablerow = (content) => {
      return `<tr style="border-bottom: 1px solid #ddd;">${content}</tr>`;
    };

    // Table cell rendering
    this.markedRenderer.tablecell = (content, { header, align }) => {
      const tag = header ? 'th' : 'td';
      const style = `padding: 8px; text-align: ${align || 'left'}; border-right: 1px solid #ddd;`;
      return `<${tag} style="${style}">${content}</${tag}>`;
    };
    
    // Set global options for marked
    marked.setOptions({
      renderer: this.markedRenderer,
      gfm: true,           // GitHub Flavored Markdown
      breaks: true,        // Convert \n to <br>
      pedantic: false,     // Don't be too strict with original markdown spec
      smartLists: true,    // Use smarter list behavior
      smartypants: true,   // Use smart punctuation
      xhtml: true,         // Use self-closing tags
      headerIds: false     // Don't add IDs to headers
    });
  }

  transform(value: string): SafeHtml {
    if (!value) {
      return '';
    }

    try {
      const hasCursor = value.includes('<span class="blinking-cursor">|</span>');
      const textWithoutCursor = value.replace(/<span class="blinking-cursor">\|<\/span>$/, '');
      
      // Convert markdown to HTML using the standard marked library
      const html = marked(textWithoutCursor);

      // Add the cursor back if it was present
      const finalHtml = hasCursor ? html + '<span class="blinking-cursor">|</span>' : html;

      return this.sanitizer.bypassSecurityTrustHtml(finalHtml);
    } catch (error) {
      console.error('Error rendering markdown:', error);
      // Fallback to simple text rendering if markdown processing fails
      const safeText = value
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/\n/g, '<br>');
      return this.sanitizer.bypassSecurityTrustHtml(safeText);
    }
  }
}
