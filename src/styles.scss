@use './styles/reset';
@use "@angular/material" as mat;
@use "./material/themes/light" as l;
@use "./material/themes/dark" as d;
@use "./material/material-components/index" as components;
@use './material/material-components/index2' as components2;
@use './styles/gradient-cards';
@use './styles/form-field';
@use './styles/form-card';
@use './styles/picture-box';
@use './styles/variables' as vars;
@include mat.core();
$global-font: "'Montserrat', fantacy";
$custom-typography-font: mat.define-typography-config(
  $font-family: $global-font
);
@include mat.all-component-themes(l.$my-app-light-theme);
@include mat.all-component-typographies($custom-typography-font);
@include mat.typography-hierarchy($custom-typography-font);
@include components.theme(l.$my-app-light-theme);
@include components2.theme(l.$my-app-light-theme);
.dark {
  @include mat.all-component-colors(d.$my-app-dark-theme);
  @include components2.theme(d.$my-app-dark-theme);
  @include components.theme(d.$my-app-dark-theme);
}

.custom-header {
  min-width: 10rem !important;
}

.custom-cell {
  min-width: 10rem !important;
}

.custom-footer {
  min-width: 10rem !important;
}

.bottomTitles{
  color: rgba(0, 0, 0, 0.6);
  font-size: larger;
  font-weight: bolder;
  background-color: #e5e5e5;
}

.formError{
  font-size: 12px;
  // margin-top: -23px;
  color: red;
  position: absolute;
}

.smallDialog{
  width: 500px;
}

@media only screen and (max-width: 767px) {
  .smallDialog {
    width: 100%;
  }
}

.parentCornerCancelBtn{
  position: relative;
  top: -30px;
  text-align: end;
  margin-right: 20px;
}

.cornerCancelBtn{
  width: 35px !important;
  height: 35px !important;
}

.editIconBtn{
  width: 35px;
  height: 35px;
  border-radius: 10px;
  text-align: center;
  border: 1px solid darkgrey;
}

// Tab styling moved to material/material-components/_tabs.scss

// Force tab rendering globally
.mat-mdc-tab-header {
  visibility: visible !important;
  opacity: 1 !important;
}

.mat-mdc-tab-body-content {
  visibility: visible !important;
  opacity: 1 !important;
}

.highlighted-input{
  .mat-mdc-text-field-wrapper.mdc-text-field.mdc-text-field--outlined {
    background-color: #e5e5e5 !important;
  }
}

.highlighted-input{
  .mat-mdc-text-field-wrapper.mdc-text-field.mdc-text-field {
    background-color: #e5e5e5 !important;
  }
}

@media (max-width: 460px)  {
.cdk-overlay-pane {
  max-width: 97vw !important;
}
}

.spinner-border {
  display: inline-block;
  width: 1rem !important;
  height: 1rem !important;
  vertical-align: -0.125em;
  border: 2px solid currentcolor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: .75s linear infinite spinner-border;
}

mat-header-row.mat-mdc-header-row.mdc-data-table__header-row.cdk-header-row{
  color: rgb(36,35,54) !important;
  font-weight: bolder;
  border: 1px solid #e7e7e7;
  background-color: #e5e5e5 !important;

}

tr.mat-mdc-header-row.mdc-data-table__header-row.cdk-header-row {
  color: rgb(36,35,54) !important;
  font-weight: bolder;
  background-color: #e5e5e5 !important;
  height: 40px; /* Adjust the height as needed */
}

/* Align header cells and reduce padding */
.mdc-data-table__header-cell {
  border-right: 0.1px solid #dbdbdb !important;
  justify-content: center !important;
  text-align: center !important;
  padding: 8px 12px; /* Adjust padding as needed */
}

/* Align regular cells */
.mdc-data-table__cell {
  border-right: 0.1px solid #dbdbdb !important;
  justify-content: center !important;
  text-align: center !important;
}

.dataHover:hover{
  background-color: lightgray !important;
}

.cancelIcon{
  color: red !important;
}

.checkIcon{
  color: green !important;
}

.highlighted-row{
  color: darkgrey !important;
}

.modified-row{
  background-color: aliceblue !important;
}

//  SNACK BAR
.mat-mdc-snack-bar-container .mdc-snackbar__surface {
  max-width: none !important;
}

.message-snackbar {
  .mdc-snackbar__surface {
    background-color: #B30202 !important;
    color: white !important;
    width: 97vw !important;
    text-align: center !important;
  }
  .mat-mdc-snack-bar-label.mdc-snackbar__label {
    font-size: larger !important;
    font-weight: bold !important;
  }
}

.success-snackbar {
  .mdc-snackbar__surface {
    background-color: #28a745 !important;
    color: white !important;
  }
}

.error-snackbar {
  .mdc-snackbar__surface {
    background-color: #B30202 !important;
    color: white !important;
  }
}
.warning-snackbar {
  .mdc-snackbar__surface {
    background-color: #ff7900 !important;
    color: white !important;
  }
}

.info-snackbar {
  .mdc-snackbar__surface {
    background-color: #54B4D3 !important;
    color: white !important;
  }
}

.search-container {
  display: flex;
  margin-top: 1px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.search-icon {
  margin-left: -35px;
  margin-top: 8px;
  cursor: pointer;
  color: #555;
}

.search-input {
  padding: 10px 30px 10px 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  width: 100%;
  box-sizing: border-box;
}

@media (max-width: 460px)  {
  .search-input {
    width: 85%;
  }
}

.searchInputClass{
  position: absolute;
  margin-top: 15px;
}

.example-input{
  font-weight: 700;
  border-radius: 5px;
  padding: 8px;
  border: 1px solid rgb(36,35,54);
}

.example-input2{
  border: 1px solid gray;
  border-radius: 5px;
}

@media (max-width: 600px) {
  md-card {
    min-width:17rem;
  }
}

.dropDndDialog{
  overflow-y: auto;
  max-height: 285px;
  width: 100%;
  margin: 10px;
}

.spinner-border {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: -0.125em;
  border: 2px solid currentcolor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: .75s linear infinite spinner-border;
}

// Tab icon styling moved to material/material-components/_tabs.scss

.topHeadingBtns{
  float: right;
  margin-right: -10px;
}

.parentCornerCancelBtnRecipe{
  position: relative;
  top: -18px;
  text-align: end;
  margin-right: 20px;
}

.headingText{
  display: inline-flex !important;
  vertical-align: middle;
  height: 45px;
}

.sync{
  height: 2.2rem !important;
  width: 8.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1 !important;
  padding: 0 12px !important;
}

.reset{
  height: 2.2rem !important;
  width: auto !important;
  margin-right: 8px !important;
  font-size: 0.875rem !important;
  line-height: 1 !important;
  padding: 0 12px !important;
}

.headingBtns{
  float: right;
}

mat-header-cell.mat-mdc-header-cell.mdc-data-table__header-cell.cdk-header-cell {
  font-weight: bolder;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  display: none;
}

mat-paginator.mat-mdc-paginator.mat-paginator-sticky {
  position: sticky;
  left: 0;
}

mat-error{
  font-size: 10px;
  margin-top: -5px;
}


.mat-mdc-card-content{
  padding: 1px !important;
}

.closeBtn{
  position: sticky;
  top: 0px;
  background: white;
  display: flex;
  justify-content: flex-end;
  padding: 5px 10px;;
  z-index: 100;
}

.closeBtnIcon{
  color: red;
  height: 31px;
  width: 31px;
  overflow: hidden;
  font-size: 30px;
  cursor: pointer;
}


.mat-resize-dialog-container .dilogContent {
  min-width: 40vw;
  resize: horizontal !important;
  overflow: auto;
}

.active-row{
  background-color: #c4f0ff;
}

.infoText{
  font-size: larger;
  font-weight: bold;
}

.smallCustomDialog{
  max-width: 35vw !important;
}

.mediumCustomDialog{
  max-width: 65vw !important;
}

.largeCustomDialog{
  max-width: 85vw !important;
  // min-height: 94vh !important;
}

@media (max-width: 460px)  {
  .smallCustomDialog {
  max-width: 97vw !important;
  }
}

@media (max-width: 460px)  {
  .mediumCustomDialog {
  max-width: 97vw !important;
  }
}

@media (max-width: 460px)  {
  .largeCustomDialog {
  max-width: 97vw !important;
  }
}

.topCreateAndUpdateBtn{
  text-align: right !important;
}

.floatRightBtn{
  float: right !important;
}

.tableSnoCol{
  min-width: 5rem !important;
  max-width: 5rem !important;
}

.tableActionColdel {
  min-width: 6rem !important;
  max-width: 6rem !important;
}

.tableActionCol{
  min-width: 6rem !important;
  max-width: 6rem !important;
}

.tableModCol{
  min-width: 135px !important;
}

.menuMasterCustomTable{
  min-width: 100px !important;
}

.menuMasterStatus{
  min-width: 125px !important;
}

.example-spacer {
  flex: 1 1 auto;
}
mat-form-field {
  width: 100%;
}


.hide-checkbox .mat-pseudo-checkbox {
  display: none !important;
}

.hide-checkbox .mat-pseudo-checkbox {
  display: none !important;
}

.dense-1 {
  @include mat.all-component-densities(-1);
}

.dense-2 {
  @include mat.all-component-densities(-2);
}

.rowAddIcon{
  font-size: 30px !important;
}

.rowAddIconBtn{
  margin-left: -20px !important;
}

.link{
  text-decoration: underline;
  cursor: grab;
}

.link:hover{
  color: indianred;
}

.deleteBtn{
  background-color: rgb(191 26 26) !important;
  color: white !important;
}

.discBtn{
  background-color: white !important;
  color: black !important;
  border: 1px solid blue !important;
}

.formContainer{
  width: 80% !important;
}

.registration-form{
  margin: 10px !important;
  // 5px 15px 20px
}

.selectInputCustom{
  white-space: nowrap;
}

.deleteIconForMatSelect{
  position: absolute;
  right: 0;
  cursor: grab;
}

.disabledSelect {
  color: red !important;
  opacity: 0.6;
  pointer-events: none;
}

.deleteIconForMatSelect {
  pointer-events: auto;
  cursor: pointer;
  color: rgb(0, 0, 0);
}

.mat-option[disabled] .deleteIconForMatSelect {
  opacity: 1 !important;
}

.disabled-option {
  pointer-events: none;
}

// Removed ng-pick-datetime import as it's no longer used

/* Compact button styles */
button.mat-mdc-raised-button {
  height: 36px !important;
  line-height: 1 !important;
  font-size: 0.875rem !important;
  padding: 0 16px !important;
}

/* Icon buttons in toolbar */
.mat-mdc-icon-button {
  width: 36px !important;
  height: 36px !important;
  padding: 6px !important;

  .mat-icon {
    font-size: 20px !important;
    width: 20px !important;
    height: 20px !important;
  }
}

/* Specific styles for Reset and Sync Options buttons */
button[mat-raised-button] {
  &.reset, &.sync {
    height: 32px !important;
    line-height: 32px !important;
    padding: 0 12px !important;

    .mat-icon {
      font-size: 16px !important;
      height: 16px !important;
      width: 16px !important;
      margin-right: 4px !important;
      vertical-align: middle !important;
    }
  }
}

/* Top toolbar action buttons */
.topCreateAndUpdateBtn button[mat-raised-button],
.floatRightBtn button[mat-raised-button],
.headingBtns button[mat-raised-button] {
  height: 32px !important;
  line-height: 32px !important;
  padding: 0 12px !important;
  font-size: 0.875rem !important;
}
