@use "sass:map";
@use "sass:meta";

$breakpoints: (
  XSmall: (max-width: 599.98px),
  small: (min-width: 600px),
  medium: ( min-width: 960px),

);

@mixin responsiveMaterial($breakpoint) {
  @if (map-has-key($map: $breakpoints, $key: $breakpoint)) {
    @media screen and #{inspect(map-get($breakpoints,$breakpoint))} {
      @content;
    }
  } @else {
    @warn "Use these breakpoints:, #{map-keys($breakpoints)}";
  }
}

@mixin responsive($breakpoint) {
  @if $breakpoint == smartphone-portrait {
    @media only screen and (max-width: 575.98px) {
      @content;
    }
  }
  @if $breakpoint == smartphone-landscape {
    @media only screen and (min-width: 575.99px) and (max-width: 767.98px) {
      @content;
    }
  }
  @if $breakpoint == tablet {
    @media only screen and (min-width: 767.99px) and (max-width: 1199.97px) {
      @content;
    }
  }
  @if $breakpoint == laptop {
    @media only screen and (min-width: 1199.98px) and (max-width: 1399.98px) {
      @content;
    }
  }
  @if $breakpoint == desktop {
    @media only screen and (min-width: 1399.99px) {
      @content;
    }
  }
}
