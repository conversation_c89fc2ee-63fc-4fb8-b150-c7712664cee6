// Global tab styling
@use "@angular/material" as mat;
@use "sass:map";

@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);
  $is-dark-theme: map.get($color-config, "is-dark");
  $primary-palette: map.get($color-config, "primary");
  $accent-palette: map.get($color-config, "accent");
  $warn-palette: map.get($color-config, "warn");

  // Global tab styling
  .mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
    color: #f44336 !important;
  }

  .mat-mdc-tab .mdc-tab-indicator__content--underline {
    border-color: #f44336 !important;
  }

  // Force tab rendering
  .mat-mdc-tab-header {
    visibility: visible !important;
    opacity: 1 !important;
  }

  .mat-mdc-tab-body-content {
    visibility: visible !important;
    opacity: 1 !important;
  }

  // Compact tabs styling
  .compact-tabs {
    display: block !important;

    .mat-mdc-tab-header {
      width: auto !important;
      display: inline-block !important;
      border-bottom: 1px solid #e0e0e0;
    }

    .mat-mdc-tab-header-pagination-container {
      width: auto !important;
    }

    .mat-mdc-tab-list {
      width: auto !important;
    }

    .mat-mdc-tab-labels {
      display: inline-flex !important;
      width: auto !important;
    }

    // Make tabs take only the space they need
    .mat-mdc-tab {
      min-width: auto !important;
      padding: 0 16px !important;
      flex-basis: auto !important;
      flex-grow: 0 !important;
      height: 40px !important; // Reduced height for tabs
    }

    // Force tabs to be left-aligned
    .mdc-tab-scroller__scroll-content {
      justify-content: flex-start !important;
    }
  }

  // Default tab group styling
  .mat-mdc-tab-group {
    margin: 5px !important;
  }

  // Override Material's default tab group stretch behavior
  .mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs > .mat-mdc-tab-header .mat-mdc-tab {
    flex-grow: 0.1 !important;
    max-width: fit-content !important;
  }

  // Tab icon styling
  .tabIcon {
    font-size: 20px;
    margin-top: 4px;
    margin-right: 7px;
  }
}
