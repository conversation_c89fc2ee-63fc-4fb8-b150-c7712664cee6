@use "@angular/material" as mat;
@use "sass:map";

@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);
  $is-dark-theme: map.get($color-config, "is-dark");
  $primary-palette: map.get($color-config, "primary");
  $color-1: mat.get-color-from-palette($primary-palette, 400);
  $color-2: mat.get-color-from-palette($primary-palette, 900);

  @if $is-dark-theme {
    .sidebar-container {
      background: $color-2;   
    }
    .sidenav-icon{
        color: white !important;
      }
    
} @else {
    mat-sidenav {
        background: $color-2 !important;
    }
    .sidenav-icon{
      color: white !important;
    }
    
  }
}